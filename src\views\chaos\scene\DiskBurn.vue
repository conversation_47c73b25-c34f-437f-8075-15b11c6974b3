<template>
  <div class="disk-burn-injection">
    <div v-loading="injectionLoading">
      <div class="card-header">
        <div class="title-area">
          <el-icon class="icon"><Files /></el-icon>
          <span class="title">磁盘IO负载故障注入</span>
        </div>
      </div>

      <el-divider class="divider" />

      <div class="description">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title>此操作将注入磁盘读写压力，可能影响服务器存储性能</template>
        </el-alert>
      </div>

      <el-form :model="diskBurnInfo" :rules="rules" ref="formRef" label-position="top" class="injection-form">
        <div class="form-row">
          <el-form-item label="IO压力类型" prop="ioType" class="io-type-form-item">
            <div class="checkbox-group">
              <el-checkbox v-model="diskBurnInfo.read" label="读负载" size="large" />
              <el-checkbox v-model="diskBurnInfo.write" label="写负载" size="large" />
            </div>
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="目标路径" prop="path">
            <el-input v-model="diskBurnInfo.path" placeholder="/home">
              <template #prefix>
                <el-icon><Folder /></el-icon>
              </template>
            </el-input>
            <div class="path-hint">压力将会在此目录下生成IO负载</div>
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="持续时长" prop="timeout">
            <div class="duration-input">
              <el-input-number v-model="diskBurnInfo.timeout" :min="10" :max="1800" controls-position="right" />
              <span class="unit">秒</span>
            </div>
          </el-form-item>
        </div>

        <div class="form-actions">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="diskBurn" :loading="injectionLoading" :disabled="injectionLoading">
            <el-icon><Warning /></el-icon>
            注入故障
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { api_injection, ChaosReqtDto } from "@/api/modules/service-server-operation/chaos";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { Files, Folder, Warning } from "@element-plus/icons-vue";

// 定义 props：接收父组件传入的服务器 ID
const props = defineProps<{ serverId: string }>();
const emit = defineEmits(["injectSuccess", "closeDialog"]);
// chaos 类型
const chaosType = "disk-burn";
const formRef = ref<FormInstance>();

// 注入表单数据和状态
const diskBurnInfo = ref({
  serverInfoId: props.serverId,
  read: true,
  write: false,
  path: "/home",
  timeout: 180
});

const rules = ref<FormRules>({
  ioType: [
    {
      validator: (rule, value, callback) => {
        if (!diskBurnInfo.value.read && !diskBurnInfo.value.write) {
          callback(new Error("请至少选择一种IO负载类型"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  path: [
    { required: true, message: "请输入目标路径", trigger: "blur" },
    { pattern: /^\/.*/, message: "路径必须以/开头", trigger: "blur" }
  ],
  timeout: [
    { required: true, message: "请设置持续时长", trigger: "change" },
    { type: "number", min: 10, max: 1800, message: "时长必须在10-1800秒之间", trigger: "change" }
  ]
});

const injectionLoading = ref(false);

// 关闭对话框
const closeDialog = () => {
  emit("closeDialog");
};

// 执行磁盘IO注入
const diskBurn = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (!valid) {
      return;
    }

    if (!diskBurnInfo.value.read && !diskBurnInfo.value.write) {
      ElMessage.warning("请至少选择一种IO负载类型");
      return;
    }

    injectionLoading.value = true;

    // 构建目标信息描述
    const targetDesc = diskBurnInfo.value.read ? "读负载" + (diskBurnInfo.value.write ? "、写负载" : "") : "写负载";

    const chaosReqtDto: ChaosReqtDto = {
      serverInfoId: diskBurnInfo.value.serverInfoId,
      chaosType,
      chaosCommandPrefix:
        "create disk burn" + (diskBurnInfo.value.read ? " --read" : "") + (diskBurnInfo.value.write ? " --write" : ""),
      paramMap: {
        "--path": diskBurnInfo.value.path,
        "--timeout": diskBurnInfo.value.timeout
      },
      targetInfo: `磁盘IO: ${targetDesc} (${diskBurnInfo.value.path})`
    };

    api_injection(chaosReqtDto)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("磁盘IO故障注入成功");
          emit("injectSuccess");
          closeDialog();
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        injectionLoading.value = false;
      });
  });
};
</script>

<style scoped lang="scss">
.disk-burn-injection {
  padding: 0 16px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    .title-area {
      display: flex;
      align-items: center;

      .icon {
        color: #e6a23c;
        font-size: 20px;
        margin-right: 8px;
      }

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .divider {
    margin: 12px 0;
  }

  .description {
    margin-bottom: 20px;
  }

  .injection-form {
    padding: 16px 0;

    .form-row {
      margin-bottom: 20px;
    }

    .io-type-form-item {
      margin-bottom: 10px;
    }

    .checkbox-group {
      display: flex;
      gap: 16px;
    }

    .path-hint {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }

    .duration-input {
      display: flex;
      align-items: center;

      .unit {
        margin-left: 8px;
        color: #606266;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 30px;
    }
  }
}
</style>
