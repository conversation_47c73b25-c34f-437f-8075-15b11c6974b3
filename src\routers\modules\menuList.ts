export const menuList = [
  {
    path: "/home/<USER>",
    name: "home",
    component: () => import("@/views/home/<USER>"),
    meta: {
      icon: "HomeFilled",
      title: "首页",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: true,
      isKeepAlive: true
    }
  },
  {
    path: "/project-management/git",
    name: "git",
    component: () => import("@/views/projectManagement/git.vue"),
    meta: {
      icon: "Collection",
      title: "Git本地仓库管理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: true,
      isKeepAlive: true
    }
  },
  {
    path: "/scheduler-management",
    name: "scheduler-management",
    component: () => import("@/views/schedulerManagement/index.vue"),
    meta: {
      icon: "Calendar",
      title: "定时任务调度管理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: true,
      isKeepAlive: true
    }
  },
  {
    path: "/server-management",
    name: "server-management",
    component: () => import("@/views/ServerManagement/index.vue"),
    meta: {
      icon: "DataLine",
      title: "服务器信息管理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/esb-network-replay",
    name: "ESBNetworkReplay",
    redirect: "/esb-network-replay/config",
    meta: {
      icon: "Menu",
      title: "ESB流量回放",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/esb-network-replay/enrConfig",
        name: "enrConfig",
        component: () => import("@/views/esbNetworkReplay/configManagement/index.vue"),
        meta: {
          icon: "Setting",
          title: "配置管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/esb-network-replay/record",
        name: "record",
        component: () => import("@/views/esbNetworkReplay/recordManagement/index.vue"),
        meta: {
          icon: "VideoPlay",
          title: "录制查看",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/esb-network-replay/deduplicate",
        name: "deduplicate",
        component: () => import("@/views/esbNetworkReplay/deduplicate/index.vue"),
        meta: {
          icon: "Memo",
          title: "报文去重",
          isLink: "",
          isFull: false,
          isHide: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/esb-network-replay/asset",
        name: "asset",
        component: () => import("@/views/esbNetworkReplay/assetManagement/index.vue"),
        meta: {
          icon: "Files",
          title: "资产库",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/esb-network-replay/replay",
        name: "replay",
        component: () => import("@/views/esbNetworkReplay/replayManagement/index.vue"),
        meta: {
          icon: "RefreshRight",
          title: "回放管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  },
  {
    path: "/ESBSystemLink",
    name: "ESBSystemLink",
    component: () => import("@/views/esbSystemLink/index.vue"),
    meta: {
      icon: "DataLine",
      title: "ESB系统调用链路",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/chaos-dashboard",
    name: "chaos-dashboard",
    component: () => import("@/views/chaos/index.vue"),
    meta: {
      icon: "WarnTriangleFilled",
      title: "混沌工程控制台",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/financialStatement",
    name: "financialStatement",
    component: () => import("@/views/financialStatement/index.vue"),
    meta: {
      icon: "ScaleToOriginal",
      title: "协同办公报表比对",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/authorityPacket",
    name: "authorityPacket",
    component: () => import("@/views/authorityPacket/index.vue"),
    meta: {
      icon: "Notebook",
      title: "有权机关报文包生成",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/loanFileGenerate",
    name: "loanFileGenerate",
    component: () => import("@/views/loanFileGenerate/index.vue"),
    meta: {
      icon: "Memo",
      title: "网贷放还款文件生成",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/llm",
    name: "llm",
    redirect: "/llm/accessTestDoc",
    meta: {
      icon: "ChatLineRound",
      title: "智能助理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/llm/testHelper",
        name: "testHelper",
        component: () => import("@/views/aiAssist/testHelper/index.vue"),
        meta: {
          icon: "InfoFilled",
          title: "智能测试助理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/llm/testHelper/demandList/:id",
        name: "demandList",
        component: () => import("@/views/aiAssist/testHelper/component/DemandList.vue"),
        meta: {
          icon: "Document",
          title: "文件详情",
          isLink: "",
          isHide: true,
          isFull: false,
          isAffix: false,
          isKeepAlive: false
        }
      },
      {
        path: "/llm/accessTestDoc",
        name: "accessTestDoc",
        component: () => import("@/views/aiAssist/accessTestDoc/index.vue"),
        meta: {
          icon: "InfoFilled",
          title: "准入测试文档审核",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ],
  },
  {
    path: "/tool",
    name: "tool",
    redirect: "/tool/transferCheck",
    meta: {
      icon: "Tools",
      title: "小工具",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/tool/sibsTransCheck",
        name: "sibsTransCheck",
        component: () => import("@/views/tool/sibsTransCheck/index.vue"),
        meta: {
          icon: "CircleCheck",
          title: "核心报文转换校验",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/tool/esbFieldEnum",
        name: "esbFieldEnum",
        component: () => import("@/views/tool/esbFieldEnum/index.vue"),
        meta: {
          icon: "Management",
          title: "XML枚举值提取",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/tool/esbSendCompare",
        name: "esbSendCompare",
        component: () => import("@/views/tool/esbSendCompare/index.vue"),
        meta: {
          icon: "Setting",
          title: "XML报文响应比对",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ],
  },
  {
    path: "/link",
    name: "link",
    redirect: "/link/test-bench-server",
    meta: {
      icon: "Connection",
      title: "外部工具",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/link/test-bench-server",
        name: "testBenchServer",
        component: () => import("@/views/link/testBenchServer/index.vue"),
        meta: {
          icon: "Link",
          title: "测试工作台",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/link/ESB-search",
        name: "ESBSearch",
        component: () => import("@/views/link/ESBSearch/index.vue"),
        meta: {
          icon: "Link",
          title: "ESB服务检索平台",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  }
];
