import { Login, ResultData } from "@/api/interface";
import http from "@/api";

/**
 * @name 登录模块
 */
// 用户登录
export const loginApi = (params: Login.ReqLoginForm) => {
  return http.post<ResultData<{ access_token: string; userInfo: any }>>("/login", params);
};

// 获取用户信息
export const getUserInfoApi = () => {
  return http.get<ResultData<any>>("/user/info");
};

// 获取菜单列表
export const getMenuListApi = () => {
  return http.get<ResultData<any[]>>("/menu/list");
};

// 退出登录
export const logoutApi = () => {
  return http.post("/logout");
}; 