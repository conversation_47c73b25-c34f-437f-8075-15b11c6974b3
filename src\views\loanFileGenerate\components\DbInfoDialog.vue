<template>
  <div>
    <!-- 数据库连接管理对话框 -->
    <el-dialog v-model="visible" :title="dialogTitle" width="70%" @close="handleClose">
      <el-table v-if="!showAddForm" :data="dbConnectionInfoList" border style="width: 100%">
        <el-table-column prop="dbName" label="数据库名称" width="200" />
        <el-table-column prop="dbType" label="数据库类型" width="120">
          <template #default="scope">
            <el-tag type="success">{{ scope.row.dbType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="url" label="连接URL" show-overflow-tooltip width="270" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column label="操作" min-width="220">
          <template #default="scope">
            <el-button size="small" @click="editConnection(scope.row)">编辑</el-button>
            <el-button size="small" type="success" @click="testConnectionById(scope.row.id)">测试连接</el-button>
            <el-button size="small" type="danger" @click="deleteConnection(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 新增/编辑表单 -->
      <div v-if="showAddForm">
        <el-form :model="dbConnectionInfoForm" label-width="120px" :rules="rules" ref="formRef">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据库名称" prop="dbName">
                <el-input v-model="dbConnectionInfoForm.dbName" placeholder="请输入数据库名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据库类型" prop="dbType">
                <el-select v-model="dbConnectionInfoForm.dbType" placeholder="请选择数据库类型" style="width: 100%">
                  <el-option label="Oracle" value="ORACLE" />
                  <el-option label="MySQL" value="MYSQL" />
                  <el-option label="DB2" value="DB2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="连接URL" prop="url">
            <el-input v-model="dbConnectionInfoForm.url" placeholder="*******************************" />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input v-model="dbConnectionInfoForm.username" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" prop="password">
                <el-input v-model="dbConnectionInfoForm.password" placeholder="请输入密码" show-password />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button v-if="showAddForm" @click="showAddForm = false">返回列表</el-button>
          <el-button v-if="!showAddForm" type="primary" @click="showAddForm = true">新增连接</el-button>
          <el-button v-if="!showAddForm" @click="visible = false">关闭</el-button>
          <el-button v-if="showAddForm" type="success" @click="testConnection" :loading="testing">测试连接</el-button>
          <el-button v-if="showAddForm" type="primary" @click="submitForm">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  api_addDbConnectionInfo,
  api_deleteDbConnectionInfo,
  api_testDbConnection,
  api_testDbConnectionById,
  api_updateDbConnectionInfo,
  DbConnectionInfo
} from "@/api/modules/service-integrated-tester/db-management";

const props = defineProps<{
  modelValue: boolean;
  dbConnectionInfoList: DbConnectionInfo[];
}>();

const emit = defineEmits(["update:modelValue", "fetchList"]);

const formRef = ref();
const visible = ref(false);
const showAddForm = ref(false);
const testing = ref(false);

const dbConnectionInfoForm = reactive<DbConnectionInfo>({
  dbName: "",
  dbType: "",
  url: "",
  username: "",
  password: "",
});

const rules = reactive({
  dbName: [{ required: true, message: "请输入数据库名称", trigger: "blur" }],
  dbType: [{ required: true, message: "请选择数据库类型", trigger: "change" }],
  url: [{ required: true, message: "请输入连接URL", trigger: "blur" }],
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }]
});

const dialogTitle = computed(() => {
  return showAddForm.value ? (dbConnectionInfoForm.id ? "编辑数据库连接" : "新增数据库连接") : "数据库连接管理";
});

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
    if (!val) {
      resetForm();
      showAddForm.value = false;
    }
  }
);

const handleClose = () => {
  emit("update:modelValue", false);
};

const editConnection = function (dbConnectionInfo: DbConnectionInfo) {
  Object.assign(dbConnectionInfoForm, { ...dbConnectionInfo });
  showAddForm.value = true;
};

const deleteConnection = function (id: string) {
  ElMessageBox.confirm("确定要删除此数据库连接配置吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    api_deleteDbConnectionInfo(id).then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("删除成功");
        emit("fetchList");
      } else {
        ElMessage.error(res.respMsg);
      }
    });
  });
};

const resetForm = () => {
  formRef.value?.resetFields();
};

const submitForm = async function () {
  try {
    await formRef.value.validate();

    if (dbConnectionInfoForm.id) {
      // 更新
      api_updateDbConnectionInfo(dbConnectionInfoForm).then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("更新成功");
          emit("fetchList");
        } else {
          ElMessage.error(res.respMsg);
        }
      });
    } else {
      // 新增
      api_addDbConnectionInfo(dbConnectionInfoForm).then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("新增成功");
          emit("fetchList");
        } else {
          ElMessage.error(res.respMsg);
        }
      });
    }
    showAddForm.value = false;
  } catch (error) {}
};

const testConnection = () => {
  api_testDbConnection(
    dbConnectionInfoForm.dbType!,
    dbConnectionInfoForm.url!,
    dbConnectionInfoForm.username!,
    dbConnectionInfoForm.password!
  ).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("连接成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const testConnectionById = (id: string) => {
  api_testDbConnectionById(id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("连接成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};
</script>

<style scoped></style>
