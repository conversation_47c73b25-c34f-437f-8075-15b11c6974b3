<template>
  <div class="table-tab table-main">
    <div class="header-button-lf">
      <!--<el-form>-->
      <!--  <el-form-item label="调用关系:">-->
      <!--    <el-select v-model="selectedOption">-->
      <!--      <el-option label="被调用" :value="1" />-->
      <!--      <el-option label="调用" :value="2" />-->
      <!--    </el-select>-->
      <!--  </el-form-item>-->
      <!--</el-form>-->
    </div>

    <el-table :data="props.tableData" style="width: 100%">
      <el-table-column label="系统名称" prop="label"></el-table-column>
      <el-table-column label="常用交易">
        <template #default="scope">
          <el-button type="primary" text :icon="View" @click="openDrawer(scope.row.id)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
// import { ref } from "vue";
import { View } from "@element-plus/icons-vue";
import { TreeFilterNode } from "@/api/interface";

const props = defineProps<{ tableData?: TreeFilterNode[] }>();
// const selectedOption = ref(1);

const emit = defineEmits<{
  openDrawer: any;
}>();
const openDrawer = (id: string) => {
  for (let i = 0; i < (props.tableData?.length ?? 0); i++) {
    if (props.tableData![i].id === id) {
      emit(
        "openDrawer",
        "[" + id + "] 的常用交易",
        props.tableData![i].children!.map(item => item.label)
      );
    }
  }
};
</script>

<style scoped lang="scss">
@import "./index";
</style>
