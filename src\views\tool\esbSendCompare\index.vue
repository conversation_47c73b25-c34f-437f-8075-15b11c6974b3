<template>
  <div class="container">

    <div class="compare-config">
      <div class="card">
        <el-form inline>
          <el-form-item label="接口ID" style="width: 160px">
            <el-input size="small" v-model="searchCondition.interfaceId"></el-input>
          </el-form-item>
          <el-form-item label="类型" style="width: 120px">
            <el-select size="small" v-model="searchCondition.type" @change="queryVersionNumberList">
              <el-option key="reqt" label="reqt" value="reqt"/>
              <el-option key="resp" label="resp" value="resp"/>
            </el-select>
          </el-form-item>
          <el-form-item label="版本号" style="width: 130px">
            <el-select size="small" v-model="searchCondition.versionNumber" @change="queryNameList">
              <el-option v-for="item in filterFormData.versionNumberList" :key="item" :label="item" :value="item"/>
            </el-select>
          </el-form-item>
          <el-form-item label="名称" style="width: 600px;">
            <el-select size="small" v-model="searchCondition.name">
              <el-option v-for="item in filterFormData.nameList" :key="item" :label="item" :value="item"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="queryFieldTree">配置比对忽略</el-button>
            <el-button type="primary" size="small" @click="showSampleXml">查看样例报文</el-button>
            <el-button type="primary" size="small" @click="getLatestSampleXml">获取最新数据</el-button>
          </el-form-item>
          <el-form-item label="当前接口报文获取时间">
            <el-tag round effect="light" type="primary">{{ sampleXmlUpdateTime }}</el-tag>
          </el-form-item>
        </el-form>
      </div>

      <div class="card" style="margin-top: 0.5%">
        <el-scrollbar>
          <el-tree
            style="margin-left: 25%; max-width: 600px"
            :data="fieldTree"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            highlight-current
            ref="fieldTreeRef"
          >
            <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span v-if="data.isAdded || data.isAddedGlobal" style="color: #d12b58">{{ node.label }}<el-icon><Warning/></el-icon></span>
              <span v-else>{{ node.label }}</span>
                    <span>
                      <el-button v-if="!data.isAdded" size="small" type="primary" link @click="addIgnoredField(data)">添加</el-button>
                      <el-button v-if="data.isAdded" size="small" type="danger" link @click="removeIgnoredField(data)">删除</el-button>
                      <el-button v-if="!data.isAddedGlobal" size="small" type="warning" link @click="addIgnoredFieldGlobal(data)">添加全局</el-button>
                      <el-button
                        v-if="data.isAddedGlobal"
                        size="small"
                        type="danger"
                        link
                        @click="removeIgnoredFieldGlobal(data)"
                      >删除全局</el-button>
                    </span>
            </span>
            </template>
          </el-tree>
        </el-scrollbar>

        <el-dialog v-model="sampleXmlDialog.visible" width="70%" :title="sampleXmlDialog.title" @closed="initSampleDialog" draggable>
          <el-input v-model="sampleXmlDialog.sampleXml" type="textarea" :rows="20" style="white-space: nowrap;"/>
        </el-dialog>
      </div>
    </div>

    <div class="sendInfo" style="display: flex; margin-top: 0.7rem">
      <div class="card" style="width: 50%">
        <el-text size="large" type="primary">发送信息1</el-text>
        <el-divider/>
        <el-form label-width="auto">
          <el-form-item label="URL">
            <el-input placeholder="请求url" v-model="esbDbChangeReqtDto.url1"></el-input>
          </el-form-item>
          <el-form-item label="请求报文(xml)">
            <el-input type="textarea" autosize v-model="esbDbChangeReqtDto.content1"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <div class="card" style="width: 50%; margin-left: 1%">
        <el-text size="large" type="primary">发送信息2</el-text>
        <el-divider/>
        <el-form label-width="auto">
          <el-form-item label="URL">
            <el-input placeholder="请求url" v-model="esbDbChangeReqtDto.url2"></el-input>
          </el-form-item>
          <el-form-item label="请求报文(xml)">
            <el-input type="textarea" autosize v-model="esbDbChangeReqtDto.content2"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="card" style="margin-top: 0.5%">
      <el-form inline>
        <el-form-item>
          <el-button @click="sendAndCompare">发送并比对</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="showResp(compareResult.resp1)">查看响应报文1</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="showResp(compareResult.resp2)">查看响应报文2</el-button>
        </el-form-item>
      </el-form>
      <el-text>比对结果：{{compareResult.compareResult}}</el-text>
    </div>

    <el-dialog v-model="compareResult.visible">
      <el-button type="primary" style="float: right" @click="copy">复制</el-button>
      <pre v-text="compareResult.currShow"></pre>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {Warning} from "@element-plus/icons-vue";
import {ref} from "vue";
import {SampleXmlDto} from "@/api/modules/service-esb-data/sample-xml";
import {ElMessage} from "element-plus";
import {TreeFilterNode} from "@/api/interface";
import {
  api_downloadSampleXmlFromRemote,
  api_getFieldTree,
  api_getSampleXml,
  api_getValueByCondition
} from "@/api/modules/service-esb-data/sample-xml";
import {
  api_addIgnoredFieldGlobal, api_deleteIgnoredFieldGlobal,
  api_getIgnoredField,
  api_getIgnoredFieldGlobal,
  api_updateIgnoredField
} from "@/api/modules/esbNetworkReplay/compare";
import {api_sendAndCompare} from "@/api/modules/service-esb-data/esb-db-change";
import vkbeautify from "vkbeautify";

interface FilterFormData {
  typeList: string[];
  versionNumberList: string[];
  nameList: string[];
}

interface FieldTree extends TreeFilterNode {
  isAdded?: boolean;
  isAddedGlobal?: boolean;
}

const searchCondition = ref<SampleXmlDto>({
  interfaceId: "",
  name: "",
  versionNumber: "",
  type: "",
  sampleXml: ""
});
const filterFormData = ref<FilterFormData>({
  typeList: [],
  versionNumberList: [],
  nameList: [],
});
const sampleXmlUpdateTime = ref<string>("");
const fieldTree = ref<FieldTree[]>([]);
const sampleXmlDialog = ref({
  visible: false,
  sampleXml: "",
  title: "样例报文"
});
const fieldTreeRef = ref();
const esbDbChangeReqtDto = ref({
  url1: "",
  content1: "",
  url2: "",
  content2: "",
  interfaceId: ""
});
const compareResult = ref({
  visible: false,
  currShow: "",
  resp1: "",
  resp2: "",
  compareResult: ""
});

const queryVersionNumberList = function () {
  initVersionNumber();
  initName();
  api_getValueByCondition(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.versionNumberList = [...new Set(res.respData.sampleXmlDtoList.map(item => item.versionNumber))];
      sampleXmlUpdateTime.value = res.respData.updateTime;
    }
  });
};

const queryNameList = function () {
  initName();
  api_getValueByCondition(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.nameList = res.respData.sampleXmlDtoList.map(item => item.name);
      sampleXmlUpdateTime.value = res.respData.updateTime;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
}

const queryFieldTree = async function () {
  await api_getFieldTree(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      fieldTree.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
  queryIgnoredField();
  queryIgnoredFieldGlobal();
}

const queryIgnoredField = function () {
  api_getIgnoredField(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      updateTreeWithIds(fieldTree.value[0], res.respData, false);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const queryIgnoredFieldGlobal = function () {
  api_getIgnoredFieldGlobal().then(res => {
    if (res.respCode === 2000) {
      updateTreeWithIds(fieldTree.value[0], res.respData, true);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const updateTreeWithIds = function (tree: FieldTree, ids: string[], isGlobal: boolean): void {
  // 如果当前节点的 id 在数组中，则将 isAdded 设置为 true
  if (isGlobal) {
    tree.isAddedGlobal = ids.includes(tree.id);
  } else {
    tree.isAdded = ids.includes(tree.id);
  }

  // 如果有子节点，递归调用函数处理每个子节点
  if (tree.children && tree.children.length > 0) {
    tree.children.forEach(child => updateTreeWithIds(child, ids, isGlobal));
  }
};

const showSampleXml = function () {
  sampleXmlDialog.value.visible = true;
  querySampleXMl();
};

const querySampleXMl = function () {
  api_getSampleXml(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      sampleXmlDialog.value.sampleXml = res.respData.sampleXml;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
}

const initSampleDialog = function () {
  sampleXmlDialog.value.sampleXml = "";
  sampleXmlDialog.value.title = "";
}

const getLatestSampleXml = async function () {
  initVersionNumber();
  initName();
  initSampleDialog();
  fieldTree.value = [];
  await api_downloadSampleXmlFromRemote(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("获取最新数据成功");
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
  queryVersionNumberList();
};

const addIgnoredField = function (data) {
  data.isAdded = true;
  updateIgnoredField();
};

const removeIgnoredField = function (data) {
  data.isAdded = false;
  updateIgnoredField();
};

const updateIgnoredField = function () {
  const tree = fieldTreeRef.value.data[0];
  const addedNodeList = getAddedNode(tree);
  api_updateIgnoredField({
    fieldList: addedNodeList,
    interfaceId: searchCondition.value.interfaceId
  }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("修改成功");
      queryIgnoredField();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const getAddedNode = function (tree: any) {
  let result: string[] = [];

  function traverse(node: any) {
    if (node.isAdded) {
      result.push(node.id);
    }

    if (node.children && node.children.length > 0) {
      node.children.forEach(child => traverse(child));
    }
  }

  traverse(tree);
  return result;
};

const addIgnoredFieldGlobal = function (data: FieldTree) {
  api_addIgnoredFieldGlobal(data.id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("添加全局成功");
      queryIgnoredFieldGlobal();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const removeIgnoredFieldGlobal = function (data) {
  api_deleteIgnoredFieldGlobal(data.id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除全局成功");
      queryIgnoredFieldGlobal();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const initVersionNumber = function () {
  searchCondition.value.versionNumber = "";
  filterFormData.value.versionNumberList = [];
}

const initName = function () {
  searchCondition.value.name = "";
  filterFormData.value.nameList = [];
}

const sendAndCompare = function () {
  compareResult.value.compareResult = "";
  compareResult.value.resp1 = "";
  compareResult.value.resp2 = "";
  compareResult.value.currShow = "";
  esbDbChangeReqtDto.value.interfaceId = searchCondition.value.interfaceId;
  api_sendAndCompare(esbDbChangeReqtDto.value).then(res => {
    if (res.respCode === 2000) {
      compareResult.value.resp1 = res.respData.resp1;
      compareResult.value.resp2 = res.respData.resp2;
      compareResult.value.compareResult = res.respData.diffField;
      ElMessage.success("处理成功");
    }
  })
}

const showResp = function (data) {
  compareResult.value.visible = true;
  compareResult.value.currShow = vkbeautify.xml(data);
}

const copy = function() {
  navigator.clipboard.writeText(compareResult.value.currShow);
  ElMessage.success("复制成功");
}

</script>

<style scoped lang="scss">
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
