<template>
  <div class="container">
    <div class="card">
      <el-form inline class="search-form">
        <el-form-item class="item-intfId" label="接口ID">
          <el-select v-model="infoSearchCondition.interfaceId" filterable clearable placeholder="请输入接口ID">
            <el-option
              v-for="item in filterFormData.recordIntfIdList"
              :key="item.interfaceId"
              :label="item.interfaceId"
              :value="item.interfaceId"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-subjectDomain" label="主题域">
          <el-select v-model="infoSearchCondition.subjectDomain" filterable clearable>
            <el-option
              v-for="item in filterFormData.recordSubjectDomainList"
              :key="item.subjectDomain"
              :label="item.subjectDomain"
              :value="item.subjectDomain"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-serviceProvider" label="服务提供方">
          <el-select v-model="infoSearchCondition.serviceProvider" filterable clearable>
            <el-option
              v-for="item in filterFormData.recordServiceProviderList"
              :key="item.serviceProvider"
              :label="item.serviceProvider"
              :value="item.serviceProvider"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-interfaceType" label="接口类型">
          <el-select v-model="infoSearchCondition.interfaceType">
            <el-option
              v-for="item in filterFormData.recordIntfTypeList"
              :key="item.interfaceType"
              :label="item.interfaceType"
              :value="item.interfaceType"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-timeRange" label="时间范围">
          <el-date-picker
            v-model="infoSearchCondition.selectedTime"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="recordInfoTableData?.data" border v-loading="recordInfoTableData.loading">
        <el-table-column prop="interfaceId" label="接口号" width="100" fixed="left"/>
        <el-table-column prop="interfaceName" label="接口名称" width="400"/>
        <el-table-column prop="serviceProvider" label="服务提供方" width="350"/>
        <el-table-column prop="recordCount" label="录制数量" width="100"/>
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="changeComponent('RecordDetail', scope.row.interfaceId)">查看
            </el-button>
            <el-button size="small" plain color="#8B4726" @click="changeComponent('Deduplicate', scope.row.interfaceId)">去重
            </el-button>
            <el-button type="danger" plain size="small" @click="showDeleteDialog(scope.row.interfaceId)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="recordInfoTableData.pageNo"
        :page-size="recordInfoTableData.pageSize"
        :total="recordInfoTableData.totalCount"
        :page-count="recordInfoTableData.pageCount"
        layout="prev, pager, next, jumper"
        @current-change="pageChange"
      />

      <el-dialog v-model="deleteRecordDialog.visible" :title="'删除' + deleteRecordDialog.interfaceId + '的接口报文'">
        时间范围：<el-date-picker
          v-model="deleteRecordDialog.timeRange"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
        <el-button type="primary" @click="deleteRecord" size="default" style="margin-left: 1%">删除</el-button>
      </el-dialog>

    </div>
  </div>
</template>

<script setup lang="ts">
import {onBeforeMount, ref, watch} from "vue";
import {
  api_deleteRecord,
  api_getPagedRecordInfo,
  api_getRecordInfoFieldValue,
  InfoSearchCondition, RecordInfoDto,
  RecordSearchFilterDto
} from "@/api/modules/esbNetworkReplay/record";
import {ElMessage} from "element-plus";

export interface PropsParams {
  selectedInterfaceId: string;
  carriedInfoSearchCondition: InfoSearchCondition
}

interface FilterFormData {
  recordIntfIdList: RecordSearchFilterDto[];
  recordSubjectDomainList: RecordSearchFilterDto[];
  recordServiceProviderList: RecordSearchFilterDto[];
  recordIntfTypeList: RecordSearchFilterDto[];
}

interface RecordInfoTableData {
  data: RecordInfoDto[];
  pageNo: number;
  pageSize: number;
  pageCount: number;
  totalCount: number;
  loading: boolean;
}

const emits = defineEmits(["changeComponent"]);
const props = defineProps<{ params: PropsParams }>();

const selectedInterfaceId = ref("");
const filterFormData = ref<FilterFormData>({
  recordIntfIdList: [],
  recordSubjectDomainList: [],
  recordServiceProviderList: [],
  recordIntfTypeList: [],
});
const infoSearchCondition = ref<InfoSearchCondition>({
  pageNo: 1,
  pageSize: 10
});
const recordInfoTableData = ref<RecordInfoTableData>({
  data: [],
  pageNo: 1,
  pageSize: 10,
  pageCount: 0,
  totalCount: 0,
  loading: false
});
const deleteRecordDialog = ref({
  visible: false,
  interfaceId: "",
  timeRange: undefined,
  startTime: "",
  endTime: ""
});

const queryRecordIntfId = function () {
  api_getRecordInfoFieldValue("INTERFACE_ID").then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.recordIntfIdList = res.respData;
    }
  })
};

const queryRecordSubjectDomain = function () {
  api_getRecordInfoFieldValue("SUBJECT_DOMAIN").then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.recordSubjectDomainList = res.respData;
    }
  })
};

const queryRecordServiceProvider = function () {
  api_getRecordInfoFieldValue("SERVICE_PROVIDER").then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.recordServiceProviderList = res.respData;
    }
  })
};

const queryIntfType = function () {
  api_getRecordInfoFieldValue("INTERFACE_TYPE").then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.recordIntfTypeList = res.respData;
    }
  })
};

const search = async function () {
  recordInfoTableData.value.loading = true;
  await api_getPagedRecordInfo(infoSearchCondition.value).then(res => {
    if (res.respCode === 2000) {
      recordInfoTableData.value.data = res.respData.pageData;
      recordInfoTableData.value.pageNo = res.respData.pageNo;
      recordInfoTableData.value.pageSize = res.respData.pageSize;
      recordInfoTableData.value.totalCount = res.respData.totalCount;
      recordInfoTableData.value.pageCount = res.respData.pageCount;
    }
  }).finally(() => {
    recordInfoTableData.value.loading = false;
  });
};

const pageChange = function (pageNo: number) {
  infoSearchCondition.value.pageNo = pageNo;
  search();
};

const changeComponent = function (componentName: string, interfaceId: string) {
  selectedInterfaceId.value = interfaceId;
  emits("changeComponent", componentName, {selectedInterfaceId, carriedInfoSearchCondition: infoSearchCondition.value})
};

const showDeleteDialog = function (interfaceId: string) {
  deleteRecordDialog.value.interfaceId = interfaceId;
  deleteRecordDialog.value.visible = true;
};

const deleteRecord = function () {
  api_deleteRecord(deleteRecordDialog.value.interfaceId, deleteRecordDialog.value.startTime, deleteRecordDialog.value.endTime).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除已开始");
      deleteRecordDialog.value.visible = false;
    }
  })
};

const refresh = function () {
  queryRecordIntfId();
  queryRecordSubjectDomain();
  queryRecordServiceProvider();
  queryIntfType();
  search();
};

watch(() => infoSearchCondition.value.selectedTime, (newVal) => {
  if (newVal === null) {
    infoSearchCondition.value.startTime = undefined;
    infoSearchCondition.value.endTime = undefined;
    return;
  }
  infoSearchCondition.value.startTime = newVal[0];
  infoSearchCondition.value.endTime = newVal[1];
});

watch(() => deleteRecordDialog.value.timeRange, (newVal) => {
  if (newVal) {
    deleteRecordDialog.value.startTime = newVal[0];
    deleteRecordDialog.value.endTime = newVal[1];
    return;
  }
  deleteRecordDialog.value.startTime = "";
  deleteRecordDialog.value.endTime = "";
});

onBeforeMount(() => {
  if (props.params.carriedInfoSearchCondition) {
    infoSearchCondition.value = props.params.carriedInfoSearchCondition;
  }
  refresh();
});

defineExpose({refresh});

</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
