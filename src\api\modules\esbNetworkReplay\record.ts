import http from "@/api";
import {SERVICE_ENR} from "@/api/config/servicePort";
import {Page} from "@/api/interface";
import {ENRCommonRespDto} from "@/api/modules/service-esb-data/service-model";

export interface StartRecordForm {
  recordDBEnvIdList: string[];
  recordServiceProviderList: string[];
}

export interface InfoSearchCondition {
  pageNo: number;
  pageSize: number;
  interfaceId?: string;
  subjectDomain?: string;
  serviceProvider?: string;
  interfaceType?: string;
  startTime?: string;
  endTime?: string;
  selectedTime?: any;
}

export interface RecordInfo {
  id: string;
  url: string;
  interfaceId: string;
  requestTime: string;
  responseTime: string;
  svcCorrId: string;
  channelId: string;
  transCde: string;
  csmrId: string;
  versionNumber: string;
  esbRespMsg: string;
  esbRespCode: string;
  providerId: string
}

export interface RecordInfoDto extends ENRCommonRespDto {
  recordCount: string;
}

export interface RecordSearchFilterDto extends ENRCommonRespDto {
  channelId: string;
  transCde: string;
  csmrId: string;
  versionNumber: string;
  esbRespMsg: string;
  esbRespCode: string;
  providerId: string;
}

export interface InfoDetailSearchCondition {
  pageNo: number;
  pageSize: number;
  interfaceId: string;
  startTime?: string;
  endTime?: string;
  channelId?: string;
  transCde?: string;
  csmrId?: string;
  versionNumber?: string;
  esbRespMsg?: string;
  esbRespCode?: string;
  selectedTime?: any;
}

export interface InfoDetailDto {
  recordInfoId: string;
  csmrId: string;
  versionNumber: string;
  transCde: string;
  channelId: string;
  esbRespCode: string;
  esbRespMsg: string;
  requestTime: string;
}

export interface RecordDetail {
  infoId?: string;
  requestBody: string;
  responseBody: string;
}

export const api_getRecordInfoFieldValue = (field: string, interfaceId?: string) => {
  return http.get<RecordSearchFilterDto[]>(SERVICE_ENR + "/record/getRecordInfoFieldValue", {interfaceId, field});
}

export const api_getPagedRecordInfo = (condition: InfoSearchCondition) => {
  return http.post<Page<RecordInfoDto[]>>(SERVICE_ENR + "/record/getPagedRecordInfo", condition);
}

export const api_getPagedRecordInfoDetail = (condition: InfoDetailSearchCondition) => {
  return http.post<Page<InfoDetailDto[]>>(SERVICE_ENR + "/record/getPagedRecordInfoDetail", condition);
}

export const api_getRecordDetailFieldValue = (infoId: string, field: string) => {
  return http.get<RecordDetail>(SERVICE_ENR + "/record/getRecordDetailFieldValue", {infoId, field});
}

export const api_deleteRecord = (interfaceId: string, startTime: string, endTime: string) => {
  return http.get(SERVICE_ENR + "/record/deleteRecord", {interfaceId, startTime, endTime});
}

export const api_getPagedRecordInfoByCondition = (condition: InfoSearchCondition) => {
  return http.post<Page<RecordInfo[]>>(SERVICE_ENR + "/record/getPagedRecordInfoByCondition", condition);
}
