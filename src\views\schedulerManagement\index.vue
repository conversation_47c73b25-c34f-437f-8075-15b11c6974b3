<template>
  <div style="height: 100%">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>任务调度管理</span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="任务名称">
            <el-input v-model="searchForm.jobName" placeholder="请输入任务名称" clearable />
          </el-form-item>
          <el-form-item label="任务状态" style="width: 200px">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="正常" value="NORMAL" />
              <el-option label="暂停" value="PAUSED" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 任务列表 -->
      <el-table :data="tableData" border v-loading="loading">
        <el-table-column prop="triggerName" label="触发器名称" width="300" />
        <el-table-column prop="jobName" label="任务名称" width="300" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip min-width="300" />
        <el-table-column prop="status" label="状态" width="150">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 'NORMAL'" type="success">
              {{ scope.row.status }}
            </el-tag>
            <el-tag v-else-if="scope.row.status === 'ERROR'" type="danger">
              {{ scope.row.status }}
            </el-tag>
            <el-tag v-else type="warning">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cronExpression" label="Cron表达式" width="200" />
        <el-table-column prop="prevFireTime" label="上次执行时间" width="200" />
        <el-table-column prop="nextFireTime" label="下次计划执行时间" width="200" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button
              size="small"
              :type="scope.row.status === 'NORMAL' ? 'warning' : 'success'"
              @click="changeJobStatus(scope.row)"
            >
              {{ scope.row.status === "NORMAL" ? "暂停" : "恢复" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <job-dialog v-model="dialogVisible" :current-job="currentJob" :mode="dialogMode" @refresh="fetchJobList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import JobDialog from "./jobDialog/index.vue";
import { api_getJobsByCondition, api_updateTriggerStatus, TriggerInfoDto } from "@/api/modules/scheduler/jobs.js";
import { ElMessage } from "element-plus";

// 表格数据
const tableData = ref<TriggerInfoDto[]>([]);
const loading = ref(false);

// 搜索表单
const searchForm = ref({
  jobName: "",
  status: ""
});

// 对话框控制
const dialogVisible = ref(false);
const dialogMode = ref("add"); // 'add' or 'edit'
const currentJob = ref();

// 获取任务列表
const fetchJobList = () => {
  loading.value = true;
  const triggerInfoDto: TriggerInfoDto = {
    jobName: searchForm.value.jobName,
    status: searchForm.value.status
  };
  api_getJobsByCondition(triggerInfoDto)
    .then(res => {
      if (res.respCode === 2000) {
        tableData.value = res.respData;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 搜索
const handleSearch = () => {
  fetchJobList();
};

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    jobName: "",
    status: ""
  };
  handleSearch();
};

// 编辑任务
const handleEdit = row => {
  dialogMode.value = "edit";
  currentJob.value = { ...row };
  dialogVisible.value = true;
};

// 切换任务状态
const changeJobStatus = function (row) {
  loading.value = true;
  api_updateTriggerStatus(row.triggerGroup + "." + row.triggerName, row.status == "NORMAL" ? "PAUSED" : "NORMAL")
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("状态修改成功");
        fetchJobList();
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 初始化
onMounted(() => {
  fetchJobList();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
}
</style>
