<template>
  <div class="container">

    <div class="card">
      <el-form :inline="true" size="small">
        <el-form-item label="接口ID">
          <el-select v-model="searchCondition.interfaceId" clearable filterable style="width: 120px">
            <el-option v-for="item in interfaceIdOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" style="width: 230px">
          <el-date-picker v-model="searchCondition.reqtStartTime" type="datetime" placeholder="开始时间" value-format="x"/>
        </el-form-item>
        <el-form-item label="结束时间" style="width: 230px">
          <el-date-picker v-model="searchCondition.reqtEndTime" type="datetime" placeholder="开始时间" value-format="x"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="queryRecordInfo">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="card" style="margin-top: 10px">
      <el-table :data="recordInfoTableData.pageData" border>
        <el-table-column prop="interfaceId" label="接口id" width="120" fixed="left"/>
        <el-table-column prop="host" label="请求主机" width="150"/>
        <el-table-column prop="esbRespMsg" label="ESB响应信息" min-width="200"/>
        <el-table-column prop="esbRespCode" label="ESB响应码" min-width="150"/>
        <el-table-column label="请求时间" min-width="150">
          <template #default="scope">
            {{ formatTimestamp(scope.row.requestTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="150">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="showReqt(scope.row.recordId)">请求报文</el-button>
            <el-button type="primary" size="small" link @click="showResp(scope.row.recordId)">响应报文</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        layout="prev, pager, next"
        v-model:current-page="recordInfoTableData.pageNo"
        :page-size="10"
        :total="recordInfoTableData.totalCount"
        :page-count="recordInfoTableData.pageCount"
        @current-change="handleCurrentChange"
      />

      <el-dialog v-model="dialogData.visible" :title="dialogData.title">
        <pre v-text="dialogData.content"/>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onBeforeMount, ref} from "vue";
import vkbeautify from "vkbeautify";
import {
  api_fetchDetailColData,
  api_fetchInfoByCondition,
  api_fetchInfoColData,
  RecordInfo,
  RecordInfoDto
} from "@/api/modules/service-enr/record";
import {Page} from "@/api/interface";
import {ElMessage} from "element-plus";

const interfaceIdOptions = ref<string[]>([]);
const recordInfoTableData = ref<Page<RecordInfo[]>>({
  pageData: [],
  pageNo: 1,
  pageSize: 10,
  totalCount: 0,
  pageCount: 1
});
const dialogData = ref({
  visible: false,
  title: "",
  content: ""
});
const searchCondition = ref<RecordInfoDto>({});

const queryRecordInfo = () => {
  api_fetchInfoByCondition(recordInfoTableData.value.pageNo, recordInfoTableData.value.pageSize, searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      recordInfoTableData.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
};

const queryInterfaceIdOptions = function() {
  api_fetchInfoColData(["INTERFACE_ID"]).then(res => {
    if (res.respCode === 2000) {
      interfaceIdOptions.value = res.respData.map(item => item.interfaceId!);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const handleCurrentChange = (currPage: number) => {
  recordInfoTableData.value.pageNo = currPage;
  queryRecordInfo();
};

const showReqt = (recordId: string) => {
  api_fetchDetailColData(recordId, ["REQUEST_BODY"]).then(res => {
    if (res.respCode === 2000) {
      dialogData.value.visible = true;
      dialogData.value.content = vkbeautify.xml(res.respData.requestBody);
      dialogData.value.title = "请求报文";
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const showResp = (id) => {
  api_fetchDetailColData(id, ["RESPONSE_BODY"]).then(res => {
    if (res.respCode === 2000) {
      dialogData.value.visible = true;
      dialogData.value.content = vkbeautify.xml(res.respData.responseBody);
      dialogData.value.title = "响应报文";
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const formatTimestamp = function(timestamp: number) {
  const date = new Date(timestamp);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以需要加1
  const day = String(date.getDate()).padStart(2, '0');

  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

onBeforeMount(() => {
  queryRecordInfo();
  queryInterfaceIdOptions();
})
</script>

<style scoped lang="scss">

</style>
