import http from "@/api";
import {SERVICE_ENR} from "@/api/config/servicePort";

export interface EsbDeduplicateIgnoreDto {
  interfaceId: string;
  ignoredFieldList: string[];
}

export const api_fetchIgnoredField = function (interfaceId: string) {
  return http.get<string[]>(SERVICE_ENR + "/deduplicate/fetchIgnoredField", {interfaceId});
}

export const api_updateIgnoredField = function (esbDeduplicateIgnoreDto: EsbDeduplicateIgnoreDto) {
  return http.post(SERVICE_ENR + "/deduplicate/updateIgnoredField", esbDeduplicateIgnoreDto);
}

export const api_fetchIgnoredFieldGlobal = () => {
  return http.get<string[]>(SERVICE_ENR + "/deduplicate/global/fetchIgnoredField");
}

export const api_addIgnoredFieldGlobal = (ignoredField: string) => {
  return http.get(SERVICE_ENR + "/deduplicate/global/addIgnoredField", {ignoredField});
}

export const api_deleteIgnoredFieldGlobal = (ignoredField: string) => {
  return http.get(SERVICE_ENR + "/deduplicate/global/deleteIgnoredField", {ignoredField});
}
