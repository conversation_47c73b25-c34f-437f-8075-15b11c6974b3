import http from "@/api/index";
import {SERVICE_ESB_DATA} from "@/api/config/servicePort";

export interface EsbServiceModel {
  interfaceId: string;
  interfaceName: string;
  subjectDomain: string
  releaseRange: string;
  serviceProvider: string;
  interfaceType: string;
  parseTime: string;
}

export interface ENRCommonRespDto {
  interfaceId: string;
  interfaceName: string;
  serviceProvider: string;
  subjectDomain: string;
  interfaceType: string;
}

export interface UpdateIgnoredFieldDto {
  interfaceId: string;
  fieldList: string[];
}

export const api_getServiceModelFiledValue = (field: string) => {
  return http.get<EsbServiceModel[]>(SERVICE_ESB_DATA + "/serviceModel/getServiceModelFiledValue?field="+field);
}
