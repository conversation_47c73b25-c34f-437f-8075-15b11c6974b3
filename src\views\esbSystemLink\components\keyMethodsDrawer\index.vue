<template>
  <div class="drawer_style">
    <el-drawer v-model="drawerVisible" :destroy-on-close="true" :title="drawerParams?.title">
      <div style="box-sizing: border-box; margin-bottom: 15px">
        <el-button @click="addTrade" type="primary">新增交易</el-button>
      </div>
      <div class="card_style">
        <el-card shadow="hover" v-for="item in drawerParams?.cardData" :key="item">
          {{ item }}
          <div style="float: right">
            <el-button @click="editTrade(item)" link type="warning">编辑</el-button>
            <el-button @click="deleteTrade(item)" link type="warning">删除</el-button>
          </div>
        </el-card>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

interface DrawerParams {
  title: string;
  cardData: string[];
}

const drawerVisible = ref(false);
const drawerParams = ref<DrawerParams>();
const setDrawerParams = function (params: DrawerParams) {
  drawerVisible.value = true;
  drawerParams.value = params;
};

function addTrade() {
  ElMessageBox.prompt("请输入交易名称", "Tip", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(({ value }) => {
    ElMessage({
      type: "success",
      message: `交易名称: ${value}添加成功`
    });
  });
}

function editTrade(item) {
  ElMessageBox.prompt("请输入交易名称", "Tip", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: item
  }).then(() => {
    ElMessage({
      type: "success",
      message: `编辑成功`
    });
  });
}

function deleteTrade(item: string) {
  ElMessageBox.confirm(`此操作将永久删除[${item}], 是否继续?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    ElMessage({
      type: "success",
      message: "删除成功!"
    });
  });
}

defineExpose({ setDrawerParams });
</script>

<style scoped lang="scss">
@import "./index";
</style>
