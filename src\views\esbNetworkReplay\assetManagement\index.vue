<template>
  <div>
    <component :is="currComponent" @change-component="changeComponent" :trans-params="transParams" />
  </div>
</template>

<script setup lang="ts">
import AssetInfo from "@/views/esbNetworkReplay/assetManagement/assetInfo/index.vue";
import AssetInfoDetail from "@/views/esbNetworkReplay/assetManagement/assetInfoDetail/index.vue";
import ReplayConfig from "@/views/esbNetworkReplay/assetManagement/replayConfig/index.vue"
import {ref, shallowRef} from "vue";

const transParams = ref({});
const currComponent = shallowRef<any>(AssetInfo);

const changeComponent = function (componentName: string, params: any) {
  transParams.value = params;
  switch (componentName) {
    case "AssetInfo":
      currComponent.value = AssetInfo;
      return;
    case "AssetInfoDetail":
      currComponent.value = AssetInfoDetail;
      return;
    case "ReplayConfig":
      currComponent.value = ReplayConfig;
      return;
    default:
      currComponent.value = AssetInfo;
      return;
  }
}
</script>

<style scoped lang="scss">

</style>
