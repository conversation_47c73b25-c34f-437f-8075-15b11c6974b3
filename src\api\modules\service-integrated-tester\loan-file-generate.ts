import http from "@/api";
import { SERVICE_INTEGRATED_TESTER } from "@/api/config/servicePort";
import { CommonReqtDto } from "@/api/modules/service-server-operation/chaos";

interface BusinessApply {
  serialno: string;
  occurdate: string;
  customerid: string;
  customername: string;
  businesstype: string;
  productid: string;
  specificsrlno: string;
  occurtype: string;
  revolveflag: string;
  contractsigntype: string;
  contractartificialno: string;
  businesscurrency: string;
  businesssum: string;
  maturitydate: string;
  drawdowntype: string;
  direction: string;
  purpose_type: string;
  purposedescription: string;
  vouchtype: string;
  approveorgid: string;
  approveuserid: string;
  approvedate: string;
  approvestatus: string;
  operateorgid: string;
  operateuserid: string;
  operatedate: string;
  inputorgid: string;
  inputuserid: string;
  inputdate: string;
  updatedate: string;
  remark: string;
  tempsaveflag: string;
  systemchannelflag: string;
  accountingorgid: string;
  paymenttype: string;
  businesspriority: string;
  businesstermday: string;
  batchserialno: string;
  businesstermmonth: string;
  salesstore: string;
  salesperson: string;
  activeno: string;
  interestmode: string;
  paymentsource: string;
  prepaymentflag: string;
  usewaydescripe: string;
  usecommpanyname: string;
  usecommpanytype: string;
  usecommpanycerttype: string;
  industor: string;
  contractsigndate: string;
  gxbusinesssum: string;
  gxcontract: string;
  thirdapplyno: string;
  channelno: string;
  phoneno: string;
  usecommpanycertid: string;
  partnercode: string;
  policyno: string;
  inscomtype: string;
  inscompany: string;
  isqdbank: string;
  marriage: string;
  educational: string;
  companynature: string;
  industry: string;
  occupation: string;
  address1: string;
  mobiletelephone: string;
  rulestatus: string;
  putoutflag: string;
  registrationtime: string;
  firstorderapplicationtime: string;
  age: string;
  facerecognitionscore: string;
  creditcardno: string;
  partnersscore: string;
  antifraudscore: string;
  partnersrating: string;
  partnersapproveflag: string;
  isquerypboc: string;
  creditamount: string;
  isnewcustomer: string;
  credittime: string;
  historyoverdays: string;
  historyoverbuinesssum: string;
  loanapprovenum15d: string;
  cardaccnum18m: string;
  maxrepayments24m: string;
  maxoverloan3m: string;
  notusedration3m: string;
  loanenquirynum9m: string;
  overaccount9m: string;
  loanquerytime: string;
  firstcreditcardm: string;
  payoffloansum: string;
  earlyopaccunclearedloansum: string;
  norccusagerate: string;
  dcbaddebts: string;
  accuratedcbaddebt: string;
  dcfreeze: string;
  dc3mapp2times: string;
  dc6mapp3times: string;
  dc12mapp4times: string;
  loansecondaryno: string;
  loansuspiciousno: string;
  loanlossno: string;
  loan3mapp2times: string;
  loan6mapp3times: string;
  loan12mapp4times: string;
  normdcnum: string;
  isenforcem: string;
  dcoversum: string;
  loanoverdueamount: string;
  accountrepaym: string;
  crsquery3m: string;
  firstcardage: string;
  maxlinelimit: string;
  pbccrsscore: string;
  lastloantime: string;
  lastloanamount: string;
  lastloanterms: string;
  loanbalance: string;
  payprincipalamt: string;
  loanterms: string;
  overdueterms12m: string;
  overdueamt12m: string;
  loanamt12m: string;
  location: string;
  coreloantype: string;
  orientation: string;
  busname: string;
  mainindustry: string;
  storelevel: string;
  regdate: string;
  turnover: string;
  idcardvaliddate: string;
  idcardaddress: string;
  isinblacklisttd: string;
  isinblacklistzh: string;
  isinblacklistqh: string;
  isinblacklistbr: string;
  iscreditcardbaddebt: string;
  isaccountbaddebt: string;
  iscreditcardoverdue: string;
  bscore: string;
  isbususer: string;
  thirdapplydate: string;
  pcbgreenflag: string;
  relativeserialno: string;
  relativethirdapplyno: string;
  thirdrequestno: string;
  monthlyincome: string;
  quotastatus: string;
  lbsaddress: string;
  provinceflag: string;
  businessincome: string;
  comprehensive_rate: string;
  idcardvalidstartdate: string;
  loancontractno: string;
  quotano: string;
  provinceaddress: string;
  guarantor: string;
  guarantratio: string;
  sessionid: string;
  transactionid: string;
  transactiontype: string;
  bankcard: string;
  frontid: string;
  backid: string;
  facephotoid: string;
  protocalinfo: string;
  location_reason: string;
  creditincrcode: string;
  creditincrflag: string;
  policytransflag: string;
  balancecomcose: string;
  orglendparrate: string;
  isneedauxinfo: string;
  auxinfoqueryurl: string;
  auxeventtype: string;
  callbackurl: string;
  rate: string;
  creditrejectionreason: string;
  creditrequesttime: string;
  repaymethod: string;
  risktype: string;
  deductible: string;
  suggestamteffectivetime: string;
  suggestamtexpiretime: string;
  suggestratemax: string;
  suggestratemin: string;
  suggesttmpamt: string;
  tmpamteffectivetime: string;
  tmpamtexpiretime: string;
  dailyrate: string;
  versionflag: string;
  pcr_auth: string;
  actuallender: string;
  guarantamt: string;
  enablecreditamt: string;
  enableamteffectivetime: string;
  applytype: string;
  credittag: string;
  isnewindustry: string;
  newindustrycast: string;
  adstime: string;
}

export interface CustomerInfo {
  customerid: string;
  customername: string;
  customertype: string;
  certtype: string;
  certid: string;
  inputorgid: string;
  inputuserid: string;
  inputdate: string;
  remark: string;
  mfcustomerid: string;
  status: string;
  issuecountry: string;
  objectno: string;
  objecttype: string;
  englishname: string;
  datasource: string;
  cstrrisklevel: string;
  taxpayertype: string;
  counterpartytype: string;
  phoneno: string;
  channelno: string;
  coreid: string;
  sibscustomerid: string;
  thirdcustomerno: string;
  photoisconsistent: string;
}

export interface CommonReqtListDto<T> extends CommonReqtDto {
  listParam1?: T[];
}

export const api_getBusinessApply = (loanId: string, dbConnectionInfoId: string) => {
  return http.get<BusinessApply>(SERVICE_INTEGRATED_TESTER + `/loanFileGenerate/businessApply/${loanId}`, {
    dbConnectionInfoId
  });
};

export const api_getCustomerInfo = (customerId: string, dbConnectionInfoId: string) => {
  return http.get<CustomerInfo>(SERVICE_INTEGRATED_TESTER + `/loanFileGenerate/customerInfo/${customerId}`, {
    dbConnectionInfoId
  });
};

export const api_createDestFolder = (templateDir: string, destFolderName: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + `/loanFileGenerate/createDestFolder`, {
    templateDir,
    destFolderName
  });
};

export const api_writeCsv = <T>(commonReqtListDto: CommonReqtListDto<T>) => {
  return http.post(SERVICE_INTEGRATED_TESTER + `/loanFileGenerate/writeCsv`, commonReqtListDto);
};

export const api_download = (destFolderName: string) => {
  window.open(
    `${import.meta.env.VITE_API_URL}${SERVICE_INTEGRATED_TESTER}/loanFileGenerate/download?destFolderName=${destFolderName}`
  );
};
