// 请求响应参数（包含data）
export interface ResultData<T = any> {
  respCode: number;
  respMsg: string;
  respData: T;
}

export interface Page<T = any> {
  pageNo: number;
  pageSize: number;
  pageCount: number;
  totalCount:number;
  pageData: T;
}

// 登录模块
export namespace Login {
  export interface ReqLoginForm {
    username: string;
    password: string;
  }
}

// TreeFilter节点
export interface TreeFilterNode {
  id: string;
  label: string;
  children?: TreeFilterNode[];
}
