<template>
  <div class="container">

    <div class="card">
      <el-form inline>
        <el-form-item label="接口ID" style="width: 160px">
          <el-select v-model="searchCondition.interfaceId" clearable filterable>
            <el-option v-for="item in searchFormData.interfaceId" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item label="主题域" style="width: 200px">
          <el-select v-model="searchCondition.subjectDomain" clearable filterable>
            <el-option v-for="item in searchFormData.subjectDomain" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item label="服务提供方" style="width: 350px">
          <el-select v-model="searchCondition.serviceProvider" clearable filterable>
            <el-option v-for="item in searchFormData.serviceProvider" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item label="接口类型" style="width: 200px">
          <el-select v-model="searchCondition.interfaceType" clearable filterable>
            <el-option v-for="item in searchFormData.interfaceType" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchCondition.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD hh:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="card" style="margin-top: 0.5%">
      <el-table :data="assetInfoTableData.data" border>
        <el-table-column prop="interfaceId" label="接口ID" width="100" fixed="left"/>
        <el-table-column prop="interfaceName" label="接口名称" width="300"/>
        <el-table-column prop="serviceProvider" label="服务提供方" width="300"/>
        <el-table-column prop="subjectDomain" label="主题域" width="150"/>
        <el-table-column prop="interfaceType" label="接口类型" width="170"/>
        <el-table-column prop="count" label="报文数量" width="120"/>
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="scope">
            <el-button type="primary" size="small" @click="changeComponent('AssetInfoDetail', scope.row.interfaceId)">详情
            </el-button>
            <el-button type="info" size="small" color="#2F4F4F" plain @click="showInternalDeduplicateDialog(scope.row.interfaceId)">去重</el-button>
            <el-button type="warning" size="small" plain @click="changeComponent('ReplayConfig', scope.row.interfaceId)">回放</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="assetInfoTableData.pageNo"
        :page-size="assetInfoTableData.pageSize"
        layout="prev, pager, next, jumper"
        :total="assetInfoTableData.totalCount"
        @current-change="pageChange"
      />

      <el-dialog v-model="internalDeduplicateDialog.visible" :title="'接口'+internalDeduplicateDialog.interfaceId+'的二次去重配置'">
        <el-select v-model="internalDeduplicateDialog.selectedVersionNumber" style="width: 30%">
          <el-option v-for="item in internalDeduplicateDialog.versionNumberList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <el-button type="primary" @click="internalDeduplicate" style="margin-left: 1%">二次去重</el-button>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  api_getAssetInfoFieldValue, api_getInfoFieldValueByInterfaceId,
  api_getPagedAssetInfo, AssetInfoDto, AssetInfoSearchDto
} from "@/api/modules/esbNetworkReplay/asset";
import {onBeforeMount, ref, watch} from "vue";
import {ElMessage} from "element-plus";
import {api_internalDeduplicate} from "@/api/modules/esbNetworkReplay/deduplicate";

export interface TransParams {
  selectedInterfaceId: string;
  carriedInfoSearchCondition: AssetInfoSearchDto;
}

interface AssetInfoTableData {
  data: AssetInfoDto[];
  pageNo: number;
  pageSize: number;
  pageCount: number;
  totalCount: number;
}

interface SearchFormData {
  interfaceId: string[];
  subjectDomain: string[];
  serviceProvider: string[];
  interfaceType: string[];
  timeRange: any;
}

interface InternalDeduplicateDialog {
  visible: boolean;
  versionNumberList: string[];
  selectedVersionNumber: string;
  interfaceId: string;
}

const emits = defineEmits(["changeComponent"]);
const props = defineProps<{ transParams: TransParams }>();
const searchFormData = ref<SearchFormData>({
  interfaceId: [],
  subjectDomain: [],
  serviceProvider: [],
  interfaceType: [],
  timeRange: [],
});
const assetInfoTableData = ref<AssetInfoTableData>({
  data: [],
  pageCount: 0,
  pageNo: 0,
  pageSize: 10,
  totalCount: 0,
});
const searchCondition = ref<AssetInfoSearchDto>({
  pageNo: 1,
  pageSize: 10,
  interfaceId: "",
  subjectDomain: "",
  serviceProvider: "",
  interfaceType: "",
  timeRange: [],
  startTime: "",
  endTime: ""
});
const internalDeduplicateDialog = ref<InternalDeduplicateDialog>({
  visible: false,
  versionNumberList: [],
  selectedVersionNumber: "",
  interfaceId: ""
});

const changeComponent = function (componentName: string, interfaceId: string) {
  const transParams: TransParams = {
    selectedInterfaceId: interfaceId,
    carriedInfoSearchCondition: searchCondition.value
  };
  emits("changeComponent", componentName, transParams);
};

const showInternalDeduplicateDialog = function (interfaceId: string) {
  internalDeduplicateDialog.value.interfaceId = interfaceId;
  internalDeduplicateDialog.value.visible = true;
  api_getInfoFieldValueByInterfaceId(internalDeduplicateDialog.value.interfaceId, "VERSION_NUMBER").then(res => {
    if (res.respCode === 2000) {
      internalDeduplicateDialog.value.versionNumberList = res.respData;
    }
  })
};

const internalDeduplicate = function () {
  api_internalDeduplicate(internalDeduplicateDialog.value.interfaceId, internalDeduplicateDialog.value.selectedVersionNumber).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("二次去重成功");
      internalDeduplicateDialog.value.visible = false;
    }
  })
};

const queryInterfaceIdList = function () {
  api_getAssetInfoFieldValue("INTERFACE_ID").then(res => {
    if (res.respCode === 2000) {
      searchFormData.value.interfaceId = res.respData.map(item => item.interfaceId);
    }
  })
};

const querySubjectDomainList = function () {
  api_getAssetInfoFieldValue("SUBJECT_DOMAIN").then(res => {
    if (res.respCode === 2000) {
      searchFormData.value.subjectDomain = res.respData.map(item => item.subjectDomain);
    }
  })
};

const queryServiceProviderList = function () {
  api_getAssetInfoFieldValue("SERVICE_PROVIDER").then(res => {
    if (res.respCode === 2000) {
      searchFormData.value.serviceProvider = res.respData.map(item => item.serviceProvider);
    }
  });
};

const queryInterfaceTypeList = function () {
  api_getAssetInfoFieldValue("INTERFACE_TYPE").then(res => {
    if (res.respCode === 2000) {
      searchFormData.value.interfaceType = res.respData.map(item => item.interfaceType);
    }
  });
}

const search = function () {
  api_getPagedAssetInfo(searchCondition.value).then((res) => {
    if (res.respCode === 2000) {
      assetInfoTableData.value.data = res.respData.pageData;
      assetInfoTableData.value.pageNo = res.respData.pageNo;
      assetInfoTableData.value.pageSize = res.respData.pageSize;
      assetInfoTableData.value.pageCount = res.respData.pageCount;
      assetInfoTableData.value.totalCount = res.respData.totalCount;
    }
  })
}

const pageChange = function (pageNo: number) {
  searchCondition.value.pageNo = pageNo;
  search();
};

watch(() => searchCondition.value.timeRange, (newValue) => {
  if (newValue) {
    searchCondition.value.startTime = newValue[0];
    searchCondition.value.endTime = newValue[1];
    return;
  }
  searchCondition.value.startTime = "";
  searchCondition.value.endTime = "";
});

onBeforeMount(() => {
  if (props.transParams.carriedInfoSearchCondition) {
    searchCondition.value.startTime = props.transParams.carriedInfoSearchCondition.startTime;
    searchCondition.value.endTime = props.transParams.carriedInfoSearchCondition.endTime;
    searchCondition.value.interfaceId = props.transParams.carriedInfoSearchCondition.interfaceId;
    searchCondition.value.subjectDomain = props.transParams.carriedInfoSearchCondition.subjectDomain;
    searchCondition.value.serviceProvider = props.transParams.carriedInfoSearchCondition.serviceProvider;
    searchCondition.value.interfaceType = props.transParams.carriedInfoSearchCondition.interfaceType;
    searchCondition.value.timeRange = [props.transParams.carriedInfoSearchCondition.startTime, props.transParams.carriedInfoSearchCondition.endTime];
  }
  search();
  queryInterfaceIdList();
  querySubjectDomainList();
  queryServiceProviderList();
  queryInterfaceTypeList();
})
</script>

<style scoped lang="scss">

</style>
