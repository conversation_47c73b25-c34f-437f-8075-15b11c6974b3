import http from "@/api";
import {SERVICE_SERVER_OPERATION} from "@/api/config/servicePort";
import { Page } from "@/api/interface";

export interface ChaosExecutingInfo {
  id: string;
  chaosType: string;
  serverIp: string;
  startTime: number;
  duration: number;
  endTime: number;
  targetInfo: string;
  serverInfoId: string;
}

export interface ChaosExecutingInfoDto extends ChaosExecutingInfo{
  startTimeString: string;
  estEndTimeString: string;
  secondsRemain: string;
}

export interface ChaosReqtDto {
  serverInfoId: string;
  chaosType: string;
  chaosCommandPrefix: string;
  paramMap: object;
  targetInfo: string;
}

export interface CommonReqtDto {
  param1?: string;
  param2?: string;
  param3?: string;
}

export interface ChaosServerDto {
  serverInfoId?: string;
  agentStatus?: number;
  serverName?: string;
  serverIp?: string;
  username?: string;
  pw?: string;
  environment?: string;
  authType?: string;
  remark?: string;
}

export const api_setup = function (serverInfoId: string) {
  return http.get(SERVICE_SERVER_OPERATION + "/chaos/setup", {serverInfoId});
}

export const api_getExecutingList = function (key?: string) {
  return http.get<ChaosExecutingInfoDto[]>(`${SERVICE_SERVER_OPERATION}/chaos/getExecutingList`, {key});
}

export const api_injection = function (chaosReqtDto: ChaosReqtDto) {
  return http.post(SERVICE_SERVER_OPERATION + "/chaos/injection", chaosReqtDto);
}

export const api_getDefaultNicName = (serverInfoId: string) => {
  return http.get<string>(SERVICE_SERVER_OPERATION +"/chaos/getDefaultNicName", {serverInfoId});
}

export const api_httpOnewayLoss = (commonReqtDto: CommonReqtDto) => {
  return http.post(SERVICE_SERVER_OPERATION + "/chaos/httpOnewayLoss", commonReqtDto);
}

export const api_destroy = function (id: string) {
  return http.get(SERVICE_SERVER_OPERATION + "/chaos/destroy", {id});
}

export const api_addChaosServer = function(serverInfoId: string) {
  return http.get(SERVICE_SERVER_OPERATION + "/chaos/addChaosServer", {serverInfoId});
}

export const api_getChaosServerList = function(pageNo: number, pageSize: number, chaosServerDto: ChaosServerDto) {
  return http.post<Page<ChaosServerDto[]>>(`${SERVICE_SERVER_OPERATION}/chaos/list?pageNo=${pageNo}&pageSize=${pageSize}`, chaosServerDto);
}

export const api_checkStatus = function(serverInfoId: string) {
  return http.get<number>(SERVICE_SERVER_OPERATION + "/chaos/checkStatus", {serverInfoId});
}

export const api_deleteById = function(id: string) {
  return http.get(`${SERVICE_SERVER_OPERATION}/chaos/delete/${id}`);
}
