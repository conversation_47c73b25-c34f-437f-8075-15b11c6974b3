<template>
  <div class="disk-fill-injection">
    <div v-loading="injectionLoading">
      <div class="card-header">
        <div class="title-area">
          <el-icon class="icon"><Folder /></el-icon>
          <span class="title">磁盘填充故障注入</span>
        </div>
      </div>
      
      <el-divider class="divider" />
      
      <div class="description">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title>此操作将填充目标服务器磁盘空间，可能导致磁盘空间不足</template>
        </el-alert>
      </div>
      
      <el-form :model="diskFillInfo" :rules="rules" ref="formRef" label-position="top" class="injection-form">
        <div class="form-row">
          <el-form-item label="填充方式" prop="fillType" class="fill-type-form-item">
            <el-radio-group v-model="fillType">
              <el-radio label="percent">按百分比填充</el-radio>
              <el-radio label="size">按指定大小填充</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        
        <div class="form-row" v-if="fillType === 'percent'">
          <el-form-item label="磁盘使用率" prop="percent">
            <el-slider 
              v-model="diskFillInfo.percent" 
              :min="10" 
              :max="100"
              :format-tooltip="value => `${value}%`"
              show-input
            />
            <div class="warning-text" v-if="diskFillInfo.percent > 80">
              <el-icon><Warning /></el-icon> 高使用率可能导致系统异常
            </div>
          </el-form-item>
        </div>
        
        <div class="form-row" v-if="fillType === 'size'">
          <el-form-item label="填充大小(MB)" prop="size">
            <el-input-number 
              v-model="diskFillInfo.size" 
              :min="10"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </div>
        
        <div class="form-row">
          <el-form-item label="填充目录" prop="path">
            <el-input v-model="diskFillInfo.path" placeholder="/home">
              <template #prefix>
                <el-icon><FolderAdd /></el-icon>
              </template>
            </el-input>
            <div class="path-hint">此目录将被用于创建填充文件</div>
          </el-form-item>
        </div>
        
        <div class="form-row">
          <el-form-item label="持续时长" prop="timeout">
            <div class="duration-input">
              <el-input-number 
                v-model="diskFillInfo.timeout" 
                :min="10" 
                :max="3600" 
                controls-position="right"
              />
              <span class="unit">秒</span>
            </div>
          </el-form-item>
        </div>
        
        <div class="form-actions">
          <el-button @click="closeDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="diskFill" 
            :loading="injectionLoading" 
            :disabled="injectionLoading"
          >
            <el-icon><Warning /></el-icon>
            注入故障
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { api_injection, ChaosReqtDto } from "@/api/modules/service-server-operation/chaos";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { Folder, FolderAdd, Warning } from "@element-plus/icons-vue";

// 定义 props：接收父组件传入的服务器 ID
const props = defineProps<{ serverId: string }>();
const emit = defineEmits(["injectSuccess", "closeDialog"]);
const chaosType = "disk-fill";
const formRef = ref<FormInstance>();

// 填充方式选择
const fillType = ref("percent");

// 注入表单数据和状态
const diskFillInfo = ref({
  serverInfoId: props.serverId,
  percent: 80,
  size: 100,
  path: "/home",
  timeout: 180
});

const rules = ref<FormRules>({
  percent: [
    { required: true, message: "请设置磁盘使用率", trigger: "change" },
    { type: "number", min: 10, max: 100, message: "使用率必须在10-100%之间", trigger: "change" }
  ],
  size: [
    { required: true, message: "请设置填充大小", trigger: "change" },
    { type: "number", min: 10, message: "填充大小必须大于10MB", trigger: "change" }
  ],
  path: [
    { required: true, message: "请输入填充目录", trigger: "blur" },
    { pattern: /^\/.*/, message: "路径必须以/开头", trigger: "blur" }
  ],
  timeout: [
    { required: true, message: "请设置持续时长", trigger: "change" },
    { type: "number", min: 10, max: 3600, message: "时长必须在10-3600秒之间", trigger: "change" }
  ]
});

const injectionLoading = ref(false);

// 关闭对话框
const closeDialog = () => {
  emit("closeDialog");
};

// 执行磁盘填充注入
const diskFill = async function () {
  if (!formRef.value) return;
  
  await formRef.value.validate(async valid => {
    if (!valid) {
      return;
    }
    
    injectionLoading.value = true;
    
    // 根据填充类型确定参数
    const paramMap: Record<string, any> = {
      "--path": diskFillInfo.value.path,
      "--timeout": diskFillInfo.value.timeout
    };
    
    // 根据选择的填充方式添加对应参数
    if (fillType.value === "percent") {
      paramMap["--percent"] = diskFillInfo.value.percent;
    } else {
      paramMap["--size"] = diskFillInfo.value.size;
    }
    
    // 构建目标信息描述
    const targetDesc = fillType.value === "percent" 
      ? `磁盘使用率: ${diskFillInfo.value.percent}%` 
      : `填充大小: ${diskFillInfo.value.size}MB`;
    
    const chaosReqtDto: ChaosReqtDto = {
      serverInfoId: diskFillInfo.value.serverInfoId,
      chaosType,
      chaosCommandPrefix: "create disk fill",
      paramMap,
      targetInfo: targetDesc
    };
    
    api_injection(chaosReqtDto)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("磁盘填充故障注入成功");
          emit("injectSuccess");
          closeDialog();
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        injectionLoading.value = false;
      });
  });
};

// 监听填充方式变更
watch(fillType, (newType) => {
  if (newType === "percent") {
    diskFillInfo.value.percent = 80;
  } else {
    diskFillInfo.value.size = 100;
  }
});
</script>

<style scoped lang="scss">
.disk-fill-injection {
  padding: 0 16px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    
    .title-area {
      display: flex;
      align-items: center;
      
      .icon {
        color: #67c23a;
        font-size: 20px;
        margin-right: 8px;
      }
      
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .divider {
    margin: 12px 0;
  }
  
  .description {
    margin-bottom: 20px;
  }
  
  .injection-form {
    padding: 16px 0;
    
    .form-row {
      margin-bottom: 20px;
    }
    
    .fill-type-form-item {
      margin-bottom: 10px;
    }
    
    .warning-text {
      margin-top: 8px;
      color: #e6a23c;
      font-size: 12px;
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 4px;
      }
    }
    
    .path-hint {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
    
    .duration-input {
      display: flex;
      align-items: center;
      
      .unit {
        margin-left: 8px;
        color: #606266;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 30px;
    }
  }
}
</style>
