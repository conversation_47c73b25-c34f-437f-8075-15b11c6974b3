<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>文件上传管理</span>
        </div>
      </template>

      <!-- 文件上传区域 -->
      <el-upload
        class="upload-demo"
        drag
        multiple
        :action="api_upload()"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        :on-progress="handleUploadProgress"
        :show-file-list="false"
        accept=".docx"
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">拖拽文件到此处或 <em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">仅支持上传Word文档（.docx）</div>
        </template>
      </el-upload>

      <!-- 上传进度条 -->
      <div v-if="uploadingFiles.length > 0" class="upload-progress-container">
        <div v-for="(file, index) in uploadingFiles" :key="index" class="upload-progress-item">
          <div class="file-info">
            <el-icon class="file-icon"><Document /></el-icon>
            <span class="file-name">{{ file.name }}</span>
            <span class="progress-text">{{ Math.floor(file.percentage) }}%</span>
          </div>
          <el-progress :percentage="file.percentage" :status="file.status" />
        </div>
      </div>

      <!-- 文件列表展示 -->
      <div class="file-list-container">
        <div class="file-list-header">
          <h3>已上传文件列表</h3>
          <el-button
            type="primary"
            :icon="Refresh"
            circle
            size="small"
            @click="loadFileList"
            title="刷新列表"
            :loading="loading"
          />
        </div>
        <el-table :data="fileList" style="width: 100%" stripe border v-loading="loading">
          <el-table-column prop="name" label="文件名" width="200">
            <template #default="{ row }">
              <div class="file-name-cell">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                <span class="file-name">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="上传时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="isProcessing" label="状态">
            <template #default="{ row }">
              <el-tag v-if="row.isProcessing === 0" type="success">处理成功</el-tag>
              <el-tag v-else type="warning">处理中</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button size="small" type="primary" @click="handleCustomAction(row)">详情</el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)"> 删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页控件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { UploadFilled, Document, Refresh } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { api_deleteFileById, api_getFileList, api_upload, DemandDoc } from "@/api/modules/service-llm/demand-doc.js";
import { formatTime } from "@/tools/DateTool";
import { useRouter } from "vue-router";

const router = useRouter();
const fileList = ref<DemandDoc[]>([]);
const uploadingFiles = ref<
  Array<{ name: string; percentage: number; status: "" | "success" | "exception" | "warning" | undefined }>
>([]);
const loading = ref(false);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  loadFileList();
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置为第一页
  loadFileList();
};

// 初始化加载文件列表
onMounted(() => {
  loadFileList();
});

// 加载文件列表
const loadFileList = () => {
  loading.value = true;
  api_getFileList(currentPage.value, pageSize.value)
    .then(res => {
      if (res.respCode === 2000) {
        fileList.value = res.respData.pageData;
        currentPage.value = res.respData.pageNo;
        pageSize.value = res.respData.pageSize;
        total.value = res.respData.totalCount;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 上传前的校验
const beforeUpload = file => {
  const isDocx = file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
  if (!isDocx) {
    ElMessage.error("只能上传Word文档（.docx）格式!");
    return false;
  }

  // 添加文件到上传列表中
  uploadingFiles.value.push({
    name: file.name,
    percentage: 0,
    status: "exception"
  });

  return true;
};

// 处理上传进度
const handleUploadProgress = (event, file) => {
  const fileIndex = uploadingFiles.value.findIndex(item => item.name === file.name);
  if (fileIndex !== -1) {
    uploadingFiles.value[fileIndex].percentage = event.percent;
    uploadingFiles.value[fileIndex].status = undefined; // 正在上传时没有特殊状态
  }
};

// 上传成功处理
const handleUploadSuccess = (response: any, file) => {
  ElMessage.success(`${file.name} 上传成功!`);

  // 更新上传状态为成功
  const fileIndex = uploadingFiles.value.findIndex(item => item.name === file.name);
  if (fileIndex !== -1) {
    uploadingFiles.value[fileIndex].percentage = 100;
    uploadingFiles.value[fileIndex].status = "success";

    // 延时移除该文件的进度条显示
    setTimeout(() => {
      uploadingFiles.value = uploadingFiles.value.filter(item => item.name !== file.name);
    }, 3000);
  }

  loadFileList();
};

// 上传失败处理
const handleUploadError = (error, file) => {
  console.error("上传失败:", error);
  ElMessage.error(`${file.name} 上传失败!`);

  // 更新上传状态为失败
  const fileIndex = uploadingFiles.value.findIndex(item => item.name === file.name);
  if (fileIndex !== -1) {
    uploadingFiles.value[fileIndex].status = "exception";

    // 延时移除该文件的进度条显示
    setTimeout(() => {
      uploadingFiles.value = uploadingFiles.value.filter(item => item.name !== file.name);
    }, 5000);
  }
};

// 删除文件
const handleDelete = (row: DemandDoc) => {
  ElMessageBox.confirm(`确定要删除文件 "${row.name}" 吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    loading.value = true;
    api_deleteFileById(row.id)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success(`文件 "${row.name}" 已删除!`);
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        loadFileList();
      });
  });
};

// 自定义操作按钮功能
const handleCustomAction = row => {
  router.push(`/llm/testHelper/demandList/${row.id}`);
};
</script>

<style scoped>
.card-header {
  font-size: 18px;
  font-weight: bold;
}

.upload-demo {
  margin-bottom: 30px;
}

.file-list-container {
  margin-top: 30px;
}

.file-list-container h3 {
  margin-bottom: 15px;
  font-size: 16px;
  color: #606266;
  margin-right: 10px;
}

.file-list-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.file-name-cell {
  display: flex;
  align-items: center;
}

.file-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-progress-container {
  margin: 20px 0;
}

.upload-progress-item {
  margin-bottom: 15px;
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  margin-left: auto;
  font-size: 14px;
  color: #606266;
}
</style>
