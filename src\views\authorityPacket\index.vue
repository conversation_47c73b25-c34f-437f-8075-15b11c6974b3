<template>
  <div>
    <div class="search-card">
      <div class="card-header">
        <div class="title-container">
          <h2>有权机关报文包生成工具</h2>
        </div>
      </div>
      
      <el-form inline>
        <el-form-item label="报文类型">
          <el-select 
            v-model="type" 
            @change="componentChange" 
            clearable 
            placeholder="请选择报文类型" 
            filterable
            class="packet-select"
          >
            <el-option-group label="检察院">
              <el-option v-for="item in jcyOptions" :key="item.key" :value="item.value" :label="item.label" />
            </el-option-group>
            <el-option-group label="银监公安">
              <el-option v-for="item in yjgaOptions" :key="item.key" :value="item.value" :label="item.label" />
            </el-option-group>
            <el-option-group label="高法">
              <el-option v-for="item in gfOptions" :key="item.key" :value="item.value" :label="item.label" />
            </el-option-group>
            <el-option-group label="国监委">
              <el-option v-for="item in gjwOptions" :key="item.key" :value="item.value" :label="item.label" />
            </el-option-group>
            <el-option-group label="证监会">
              <el-option v-for="item in zjhOptions" :key="item.key" :value="item.value" :label="item.label" />
            </el-option-group>
            <el-option-group label="中央军委">
              <el-option v-for="item in zyjwOptions" :key="item.key" :value="item.value" :label="item.label" />
            </el-option-group>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <transition name="fade">
      <div v-if="currComponent" class="component-container">
          <component :is="currComponent" />
      </div>
    </transition>
    
    <div v-if="!currComponent" class="empty-state">
      <el-empty description="请先选择报文类型" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, shallowRef } from "vue";
import JcyDjXdJd from "@/views/authorityPacket/jcyDjXdJd/index.vue";
import JcyZfJczf from "@/views/authorityPacket/jcyZfJczf/index.vue";
import JcyCxZjh from "@/views/authorityPacket/jcyCxZjh/index.vue";
import JcyCxZkh from "@/views/authorityPacket/jcyCxZkh/index.vue";
import YjgaDjXdJd from "@/views/authorityPacket/yjgaDjXdJd/index.vue";
import YjgaZfJczf from "@/views/authorityPacket/yjgaZfJczf/index.vue";
import YjgaCxZjhZjlx from "@/views/authorityPacket/yjgaCxZjhZjlx/index.vue";
import YjgaCxZkh from "@/views/authorityPacket/yjgaCxZkh/index.vue"
import GjwDjXdJd from "@/views/authorityPacket/gjwDjXdJd/index.vue";
import GjwZfJczf from "@/views/authorityPacket/gjwZfJczf/index.vue";
import GjwCxZjhZjlx from "@/views/authorityPacket/gjwCxZjhZjlx/index.vue";
import GjwCxZkh from "@/views/authorityPacket/gjwCxZkh/index.vue";
import GjwCxSjh from "@/views/authorityPacket/gjwCxSjh/index.vue";
import GfKzl from "@/views/authorityPacket/gfKzl/index.vue";
import GfCx from "@/views/authorityPacket/gfCx/index.vue";
import ZjhCxZjh from "@/views/authorityPacket/zjhCxZjh/index.vue";
import ZjhCxZkh from "@/views/authorityPacket/zjhCxZkh/index.vue";
import ZyjwCxZjh from "@/views/authorityPacket/zyjwCxZjh/index.vue";
import ZyjwCxZkh from "@/views/authorityPacket/zyjwCxZkh/index.vue";
import ZyjwCxSjh from "@/views/authorityPacket/zyjwCxSjh/index.vue";

const packetTypeOptions = [
  {
    key: "JCY-DJ-XD-JD",
    value: "jcyDjXdJd",
    label: "检察院-冻结/续冻/解冻",
    category: "jcy"
  },
  {
    key: "JCY-ZF-JCZF",
    value: "jcyZfJczf",
    label: "检察院-止付/解除止付",
    category: "jcy"
  },
  {
    key: "JCY-CX-ZJH",
    value: "jcyCxZjh",
    label: "检察院-查询-证件号",
    category: "jcy"
  },
  {
    key: "JCY-CX-ZKH",
    value: "jcyCxZkh",
    label: "检察院-查询-账卡号",
    category: "jcy"
  },
  {
    key: "YJGA-DJ-XD-Jd",
    value: "yjgaDjXdJd",
    label: "银监公安-冻结/续冻/解冻",
    category: "yjga"
  },
  {
    key: "YJGA-ZF-JCZF",
    value: "yjgaZfJczf",
    label: "银监公安-止付/解除止付",
    category: "yjga"
  },
  {
    key: "YJGA-CX-ZJH-ZJLX",
    value: "yjgaCxZjhZjlx",
    label: "银监公安-查询-证件号+证件类型",
    category: "yjga"
  },
  {
    key: "YJGA-CX-ZKH",
    value: "yjgaCxZkh",
    label: "银监公安-查询-账卡号",
    category: "yjga"
  },
  {
    key: "GJW-DJ-XD-JD",
    value: "gjwDjXdJd",
    label: "国监委-冻结/续冻/解冻",
    category: "gjw"
  },
  {
    key: "GJW-ZF-JCZF",
    value: "gjwZfJczf",
    label: "国监委-止付/解除止付",
    category: "gjw"
  },
  {
    key: "GJW-CX-ZJH-ZJLX",
    value: "gjwCxZjhZjlx",
    label: "国监委-查询-证件号+证件类型",
    category: "gjw"
  },
  {
    key: "GJW-CX-ZKH",
    value: "gjwCxZkh",
    label: "国监委-查询-账卡号",
    category: "gjw"
  },
  {
    key: "GJW-CX-SJH",
    value: "gjwCxSjh",
    label: "国监委-查询-手机号",
    category: "gjw"
  },
  {
    key: "GF-KZL",
    value: "gfKzl",
    label: "高法-控制类",
    category: "gf"
  },
  {
    key: "GF-CX",
    value: "gfCx",
    label: "高法-查询",
    category: "gf"
  },
  {
    key: "ZJH-CX-ZJH",
    value: "zjhCxZjh",
    label: "证监会-查询-证件号",
    category: "zjh"
  },
  {
    key: "ZJH-CX-ZKH",
    value: "zjhCxZkh",
    label: "证监会-查询-账卡号",
    category: "zjh"
  },
  {
    key: "ZYJW-CX-ZJH",
    value: "zyjwCxZjh",
    label: "中央军委-查询-证件号",
    category: "zyjw"
  },
  {
    key: "ZYJW-CX-ZKH",
    value: "zyjwCxZkh",
    label: "中央军委-查询-账卡号",
    category: "zyjw"
  },
  {
    key: "ZYJW-CX-SJH",
    value: "zyjwCxSjh",
    label: "中央军委-查询-手机号",
    category: "zyjw"
  }
];

// 按机构分类的选项
const jcyOptions = computed(() => packetTypeOptions.filter(item => item.category === 'jcy'));
const yjgaOptions = computed(() => packetTypeOptions.filter(item => item.category === 'yjga'));
const gjwOptions = computed(() => packetTypeOptions.filter(item => item.category === 'gjw'));
const gfOptions = computed(() => packetTypeOptions.filter(item => item.category === 'gf'));
const zjhOptions = computed(() => packetTypeOptions.filter(item => item.category === 'zjh'));
const zyjwOptions = computed(() => packetTypeOptions.filter(item => item.category === 'zyjw'));

const type = ref('');
const currComponent = shallowRef();

const componentChange = function (newType: string) {
  switch (newType) {
    case "jcyDjXdJd":
      currComponent.value = JcyDjXdJd;
      break;
    case "jcyZfJczf":
      currComponent.value = JcyZfJczf;
      break;
    case "jcyCxZjh":
      currComponent.value = JcyCxZjh;
      break;
    case "jcyCxZkh":
      currComponent.value = JcyCxZkh;
      break;
    case "yjgaDjXdJd":
      currComponent.value = YjgaDjXdJd;
      break;
    case "yjgaZfJczf":
      currComponent.value = YjgaZfJczf;
      break;
    case "yjgaCxZjhZjlx":
      currComponent.value = YjgaCxZjhZjlx;
      break;
    case "yjgaCxZkh":
      currComponent.value = YjgaCxZkh;
      break;
    case "gjwDjXdJd":
      currComponent.value = GjwDjXdJd;
      break;
    case "gjwZfJczf":
      currComponent.value = GjwZfJczf;
      break;
    case "gjwCxZjhZjlx":
      currComponent.value = GjwCxZjhZjlx;
      break;
    case "gjwCxZkh":
      currComponent.value = GjwCxZkh;
      break;
    case "gfKzl":
      currComponent.value = GfKzl;
      break;
    case "gfCx":
      currComponent.value = GfCx;
      break;
    case "gjwCxSjh":
      currComponent.value = GjwCxSjh;
      break;
    case "zjhCxZjh":
      currComponent.value = ZjhCxZjh;
      break;
    case "zjhCxZkh":
      currComponent.value = ZjhCxZkh;
      break;
    case "zyjwCxZjh":
      currComponent.value = ZyjwCxZjh;
      break;
    case "zyjwCxZkh":
      currComponent.value = ZyjwCxZkh;
      break;
    case "zyjwCxSjh":
      currComponent.value = ZyjwCxSjh;
      break;
    default:
      currComponent.value = null;
  }
}
</script>

<style scoped lang="scss">
.search-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-header {
    margin-bottom: 20px;
  }
  
  .title-container {
    display: inline-block;
    position: relative;
    padding-bottom: 8px;
  }
  
  h2 {
    color: #303133;
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 5px;
  }
  
  .el-form {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .packet-select {
    width: 300px;
  }
}

.component-container {
  margin-top: 20px;
}

.content-card {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    
    span {
      font-size: 16px;
    }
  }
}

.empty-state {
  margin-top: 40px;
  display: flex;
  justify-content: center;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@media (max-width: 768px) {
  .search-card .packet-select {
    width: 100%;
  }
  
  .search-card .el-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-card h2 {
    font-size: 20px;
  }
}
</style>
