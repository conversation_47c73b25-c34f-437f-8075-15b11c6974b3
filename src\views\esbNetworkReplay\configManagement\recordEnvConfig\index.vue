<template>
  <div class="container">
    <!--新增环境按钮-->
    <div>
      <el-button type="primary" round @click="changeDrawerStatus(true)" style="margin-left: auto">新增环境</el-button>
    </div>

    <!--环境表格-->
    <div class="env-table" v-loading="envTableLoading">
      <el-table :data="recordDBEnvList" border>
        <el-table-column prop="envName" label="环境名称" width="230"/>
        <el-table-column prop="url" label="连接地址" width="500"/>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="primary" size="small" plain @click="editRecordDBEnv(scope.row)">编辑</el-button>
            <el-popconfirm title="确定删除该环境吗？" @confirm="deleteRecordDBEnv(scope.row.id)">
              <template #reference>
                <el-button type="danger" size="small" plain>删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!--新增环境抽屉-->
    <el-drawer v-model="drawerOpen" title="新增环境" direction="rtl" @close="initDrawerFormData">
      <el-form class="drawer-form" label-width="auto">
        <el-form-item label="环境名称">
          <el-input v-model="envFormData.envName"></el-input>
        </el-form-item>
        <el-form-item label="连接URL">
          <el-input v-model="envFormData.url"></el-input>
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="envFormData.userName"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="envFormData.password"></el-input>
        </el-form-item>
      </el-form>
      <el-button type="primary" plain @click="testDBConnection">测试连接</el-button>
      <el-button type="primary" @click="submitEnvForm">保存</el-button>
      <el-button @click="changeDrawerStatus(false)">取消</el-button>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import {
  api_addRecordDBEnv, api_deleteRecordDBEnv,
  api_queryRecordDBEnvList,
  api_updateRecordDBEnv, RecordDBEnv
} from "@/api/modules/esbNetworkReplay/recordEnvConfig";
import { ElLoading, ElMessage } from "element-plus";
import { api_testDBConnection } from "@/api/modules/common/db";

const recordDBEnvList = ref<RecordDBEnv[]>([]);
const envTableLoading = ref(false);

const drawerOpen = ref(false);
const changeDrawerStatus = function (status: boolean) {
  drawerOpen.value = status;
};

const envFormData = ref<RecordDBEnv>({
  envName: "",
  url: "",
  userName: "",
  password: ""
});

// 获取录制数据库环境列表
const queryRecordDBEnv = async function () {
  envTableLoading.value = true;
  await api_queryRecordDBEnvList().then(res => {
    if (res.respCode === 2000) {
      recordDBEnvList.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    envTableLoading.value = false;
  });
};

// 测试数据库连接
const testDBConnection = async function () {
  const params = {
    url: envFormData.value.url,
    userName: envFormData.value.userName,
    password: envFormData.value.password
  }
  const fullscreenLoading = ElLoading.service({
    lock: true,
    text: '测试连接中...',
  });
  await api_testDBConnection(params).then(res => {
    if (res.respCode === 2000) {
      if (res.respData) {
        ElMessage.success("连接成功");
      } else {
        ElMessage.error("连接失败");
      }
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    fullscreenLoading.close();
  });
};

// 新增/修改录制数据库环境表单
const submitEnvForm = async function () {
  changeDrawerStatus(false);
  envTableLoading.value = true;
  if (!envFormData.value.id) {
    // 新增
    await api_addRecordDBEnv(envFormData.value).then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("新增成功");
      }
      if (res.respCode === 5000) {
        ElMessage.error(res.respMsg);
      }
    }).finally(() => {
      envTableLoading.value = false;
    })
  } else {
    // 修改
    await api_updateRecordDBEnv(envFormData.value).then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("修改成功");
      }
      if (res.respCode === 5000) {
        ElMessage.error(res.respMsg);
      }
    }).finally(() => {
      envTableLoading.value = false;
    });
  }
  await queryRecordDBEnv();
};

const initDrawerFormData = function () {
  envFormData.value = {
    envName: "",
    url: "",
    userName: "",
    password: ""
  };
};
const editRecordDBEnv = function (data: RecordDBEnv) {
  changeDrawerStatus(true);
  envFormData.value = JSON.parse(JSON.stringify(data));
};
const deleteRecordDBEnv = async function (id: string) {
  envTableLoading.value = true;
  await api_deleteRecordDBEnv(id).then(async res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除成功");
      await queryRecordDBEnv();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    envTableLoading.value = false;
  });
}

onMounted(() => {
  queryRecordDBEnv();
});
</script>

<style scoped lang="scss">
@import "index";
</style>
