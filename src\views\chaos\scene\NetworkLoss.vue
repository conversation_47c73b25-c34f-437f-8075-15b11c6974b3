<template>
  <div class="network-loss-injection">
    <div v-loading="injectionLoading">
      <div class="card-header">
        <div class="title-area">
          <el-icon class="icon"><Close /></el-icon>
          <span class="title">网络丢包故障注入</span>
        </div>
      </div>

      <el-divider class="divider" />

      <div class="description">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title>此操作将导致网络数据包丢失，可能影响系统通信稳定性和服务可用性</template>
        </el-alert>
      </div>

      <el-form :model="networkLossInfo" :rules="rules" ref="formRef" label-position="top" class="injection-form">
        <!-- 通用参数区域 -->
        <div class="common-params">
          <el-form-item label="丢包率" prop="lossRate">
            <div class="loss-rate-input">
              <el-slider
                v-model="networkLossInfo.lossRate"
                :min="0"
                :max="100"
                :format-tooltip="value => `${value}%`"
                show-input
              />
            </div>
            <div class="severity-indicator">
              <div class="severity-label">影响程度:</div>
              <el-tag :type="getLossLevelType" size="small">{{ getLossLevelText }}</el-tag>
            </div>
          </el-form-item>

          <el-form-item label="持续时长" prop="timeout">
            <div class="duration-input">
              <el-input-number v-model="networkLossInfo.timeout" :min="10" :max="3600" controls-position="right" />
              <span class="unit">秒</span>
            </div>
          </el-form-item>

          <el-form-item label="网卡名称" prop="nicName">
            <el-input v-model="networkLossInfo.nicName" placeholder="eth0">
              <template #prefix>
                <el-icon><Monitor /></el-icon>
              </template>
            </el-input>
            <div class="nic-hint" v-if="networkLossInfo.nicName">将在 {{ networkLossInfo.nicName }} 网卡上应用丢包规则</div>
          </el-form-item>
        </div>

        <!-- 场景选择标签页 -->
        <div class="scenario-tabs">
          <el-tabs v-model="activeScenario" @tab-click="handleScenarioChange">
            <el-tab-pane label="指定IP及端口丢包" name="remote">
              <div class="scenario-content">
                <el-form-item label="目标服务器IP" prop="remoteIp">
                  <el-input v-model="networkLossInfo.remoteIp" placeholder="支持多个IP，用英文逗号分隔">
                    <template #prefix>
                      <el-icon><LocationInformation /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="目标端口号" prop="remotePort">
                  <el-input v-model="networkLossInfo.remotePort" placeholder="支持多个端口，用英文逗号分隔">
                    <template #prefix>
                      <el-icon><Share /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <div class="scenario-description">此场景将对指定的目标IP和端口的通信产生丢包</div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="本机端口丢包" name="local">
              <div class="scenario-content">
                <el-form-item label="本机端口号" prop="localPort">
                  <el-input v-model="networkLossInfo.localPort" placeholder="支持多个端口，用英文逗号分隔">
                    <template #prefix>
                      <el-icon><Share /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <div class="scenario-description">此场景将对本机指定端口的所有流量产生丢包</div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="网卡全局丢包" name="nic">
              <div class="scenario-content">
                <el-form-item label="排除IP" prop="excludeIp">
                  <el-input v-model="networkLossInfo.excludeIp" placeholder="排除的IP，用英文逗号分隔">
                    <template #prefix>
                      <el-icon><LocationInformation /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="排除端口号" prop="excludePort">
                  <el-input v-model="networkLossInfo.excludePort" placeholder="排除的端口，默认排除32917">
                    <template #prefix>
                      <el-icon><Share /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <div class="scenario-description">此场景将对指定网卡的所有流量产生丢包（可排除指定IP和端口）</div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="HTTP单向丢包" name="http">
              <div class="scenario-content">
                <el-form-item label="目标服务器IP" prop="remoteIp">
                  <el-input v-model="networkLossInfo.remoteIp" placeholder="请输入目标服务器IP">
                    <template #prefix>
                      <el-icon><LocationInformation /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="目标端口号" prop="remotePort">
                  <el-input v-model="networkLossInfo.remotePort" placeholder="请输入目标端口号">
                    <template #prefix>
                      <el-icon><Share /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <div class="scenario-description">此场景将对HTTP请求产生单向丢包，发往目标地址正常但无法接收响应</div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div class="form-actions">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleInjection" :loading="injectionLoading" :disabled="injectionLoading">
            <el-icon><Warning /></el-icon>
            注入故障
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import {
  api_getDefaultNicName,
  api_httpOnewayLoss,
  api_injection,
  ChaosReqtDto
} from "@/api/modules/service-server-operation/chaos";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { Close, Warning, Monitor, LocationInformation, Share } from "@element-plus/icons-vue";

// 定义 props：接收父组件传入的服务器 ID
const props = defineProps<{ serverId: string }>();
const emit = defineEmits(["injectSuccess", "closeDialog"]);
const chaosType = "network-loss";
const formRef = ref<FormInstance>();

// 场景选择
const activeScenario = ref("remote");

// 注入表单数据和状态
const networkLossInfo = ref({
  serverInfoId: props.serverId,
  lossRate: 100,
  timeout: 180,
  nicName: "",
  remoteIp: "",
  remotePort: "",
  localPort: "",
  excludeIp: "",
  excludePort: ""
});

// 计算丢包级别
const getLossLevelType = computed(() => {
  const loss = networkLossInfo.value.lossRate;
  if (loss <= 30) return "success";
  if (loss <= 70) return "warning";
  return "danger";
});

const getLossLevelText = computed(() => {
  const loss = networkLossInfo.value.lossRate;
  if (loss <= 30) return "轻微影响";
  if (loss <= 70) return "中度影响";
  return "严重影响";
});

// 验证规则
const rules = ref<FormRules>({
  lossRate: [
    { required: true, message: "请设置丢包率", trigger: "change" },
    { type: "number", min: 0, max: 100, message: "丢包率必须在0-100%之间", trigger: "change" }
  ],
  timeout: [
    { required: true, message: "请设置持续时长", trigger: "change" },
    { type: "number", min: 10, max: 3600, message: "时长必须在10-3600秒之间", trigger: "change" }
  ],
  nicName: [{ required: true, message: "请输入网卡名称", trigger: "blur" }],
  remoteIp: [
    {
      validator: (rule, value, callback) => {
        if ((activeScenario.value === "remote" || activeScenario.value === "http") && !value) {
          callback(new Error("请输入目标服务器IP"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  localPort: [
    {
      validator: (rule, value, callback) => {
        if (activeScenario.value === "local" && !value) {
          callback(new Error("请输入本机端口号"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});

const injectionLoading = ref(false);

// 关闭对话框
const closeDialog = () => {
  emit("closeDialog");
};

// 切换场景时清空相关字段
const handleScenarioChange = () => {
  if (activeScenario.value === "remote" || activeScenario.value === "http") {
    networkLossInfo.value.localPort = "";
    networkLossInfo.value.excludeIp = "";
    networkLossInfo.value.excludePort = "";
  } else if (activeScenario.value === "local") {
    networkLossInfo.value.remoteIp = "";
    networkLossInfo.value.remotePort = "";
    networkLossInfo.value.excludeIp = "";
    networkLossInfo.value.excludePort = "";
  } else if (activeScenario.value === "nic") {
    networkLossInfo.value.remoteIp = "";
    networkLossInfo.value.remotePort = "";
    networkLossInfo.value.localPort = "";
  }
};

// 查询默认网卡名称
const queryDefaultNicName = function () {
  if (!networkLossInfo.value.serverInfoId) {
    networkLossInfo.value.nicName = "";
    return;
  }
  injectionLoading.value = true;
  api_getDefaultNicName(networkLossInfo.value.serverInfoId)
    .then(res => {
      if (res.respCode === 2000) {
        networkLossInfo.value.nicName = res.respData;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      injectionLoading.value = false;
    });
};

// 根据当前场景构建目标信息描述
const getTargetInfoForCurrentScenario = () => {
  if (activeScenario.value === "remote") {
    return `到指定目标${networkLossInfo.value.remoteIp}${
      networkLossInfo.value.remotePort ? `:${networkLossInfo.value.remotePort}` : ""
    }丢包${networkLossInfo.value.lossRate}%`;
  } else if (activeScenario.value === "local") {
    return `本机端口${networkLossInfo.value.localPort}丢包${networkLossInfo.value.lossRate}%`;
  } else if (activeScenario.value === "nic") {
    return `网卡${networkLossInfo.value.nicName}全局丢包${networkLossInfo.value.lossRate}%`;
  } else {
    return `HTTP单向丢包${networkLossInfo.value.lossRate}%`;
  }
};

// 执行注入
const handleInjection = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (!valid) {
      return;
    }

    // 根据当前选择的场景进行校验
    if ((activeScenario.value === "remote" || activeScenario.value === "http") && !networkLossInfo.value.remoteIp) {
      ElMessage.warning("请输入目标服务器IP");
      return;
    } else if (activeScenario.value === "local" && !networkLossInfo.value.localPort) {
      ElMessage.warning("请输入本机端口号");
      return;
    } else if (!networkLossInfo.value.nicName) {
      ElMessage.warning("请输入网卡名称");
      return;
    }

    // 对于 100% 丢包率的情况给予特别警告
    if (networkLossInfo.value.lossRate === 100) {
      const confirmed = await ElMessageBox.confirm("设置100%丢包率将导致网络完全中断，确定要继续吗？", "高风险操作警告", {
        confirmButtonText: "确定注入",
        cancelButtonText: "取消",
        type: "warning"
      }).catch(() => false);

      if (!confirmed) return;
    }

    injectionLoading.value = true;

    // 构建目标信息描述
    const targetInfo = getTargetInfoForCurrentScenario();

    // 特殊处理HTTP单向丢包场景
    if (activeScenario.value === "http") {
      api_httpOnewayLoss({
        param1: networkLossInfo.value.serverInfoId,
        param2: networkLossInfo.value.remoteIp,
        param3: networkLossInfo.value.remotePort
      })
        .then(res => {
          if (res.respCode === 2000) {
            ElMessage.success("网络丢包故障注入成功");
            emit("injectSuccess");
          } else {
            ElMessage.error(res.respMsg);
          }
        })
        .finally(() => {
          injectionLoading.value = false;
        });
      return;
    }

    const chaosReqtDto: ChaosReqtDto = {
      serverInfoId: networkLossInfo.value.serverInfoId,
      chaosType,
      chaosCommandPrefix: "create network loss",
      paramMap: {
        "--destination-ip": networkLossInfo.value.remoteIp,
        "--exclude-port": networkLossInfo.value.excludePort ? "32917," + networkLossInfo.value.excludePort : "32917",
        "--exclude-ip": networkLossInfo.value.excludeIp,
        "--interface": networkLossInfo.value.nicName,
        "--local-port": networkLossInfo.value.localPort,
        "--percent": networkLossInfo.value.lossRate,
        "--remote-port": networkLossInfo.value.remotePort,
        "--timeout": networkLossInfo.value.timeout
      },
      targetInfo
    };

    api_injection(chaosReqtDto)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("网络丢包故障注入成功");
          emit("injectSuccess");
          closeDialog();
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        injectionLoading.value = false;
      });
  });
};

// 组件挂载时查询网卡名称
watch(
  () => props.serverId,
  () => {
    if (props.serverId) {
      networkLossInfo.value.serverInfoId = props.serverId;
      queryDefaultNicName();
    }
  },
  { immediate: true }
);

// 在组件挂载时添加onMounted钩子确保获取网卡名称
onMounted(() => {
  if (networkLossInfo.value.serverInfoId) {
    queryDefaultNicName();
  }
});
</script>

<style scoped lang="scss">
.network-loss-injection {
  padding: 0 16px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    .title-area {
      display: flex;
      align-items: center;

      .icon {
        color: #f56c6c;
        font-size: 20px;
        margin-right: 8px;
      }

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .divider {
    margin: 12px 0;
  }

  .description {
    margin-bottom: 20px;
  }

  .injection-form {
    padding: 16px 0;

    .common-params {
      margin-bottom: 24px;
    }

    .severity-indicator {
      display: flex;
      align-items: center;
      margin-top: 8px;

      .severity-label {
        font-size: 12px;
        color: #909399;
        margin-right: 8px;
      }
    }

    .nic-hint {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }

    .duration-input {
      display: flex;
      align-items: center;

      .unit {
        margin-left: 8px;
        color: #606266;
      }
    }

    .scenario-tabs {
      margin-bottom: 24px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 16px;
      background-color: #f9fafc;

      .scenario-content {
        padding: 8px 0;
      }

      .scenario-description {
        font-size: 12px;
        color: #909399;
        margin-top: 12px;
        padding: 8px;
        background-color: #ecf5ff;
        border-radius: 4px;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 30px;
    }
  }
}
</style>
