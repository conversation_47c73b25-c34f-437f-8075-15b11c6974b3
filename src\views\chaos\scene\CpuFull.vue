<template>
  <div class="cpu-full-injection">
    <div v-loading="injectionLoading">
      <div class="card-header">
        <div class="title-area">
          <el-icon class="icon"><Cpu /></el-icon>
          <span class="title">CPU 满载故障注入</span>
        </div>
      </div>

      <el-divider class="divider" />

      <div class="description">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title> 此操作将使目标服务器的CPU负载提高到指定百分比 </template>
        </el-alert>
      </div>

      <el-form :model="cpuFullInfo" :rules="rules" ref="formRef" label-position="top" class="injection-form">
        <div class="form-row">
          <el-form-item label="CPU负载百分比" prop="cpuPercent">
            <el-slider v-model="cpuFullInfo.cpuPercent" :min="10" :max="100" :format-tooltip="value => `${value}%`" show-input />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="持续时长" prop="timeout">
            <div class="duration-input">
              <el-input-number v-model="cpuFullInfo.timeout" :min="10" :max="1800" controls-position="right" />
              <span class="unit">秒</span>
            </div>
          </el-form-item>
        </div>

        <div class="form-actions">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="injectCpu" :loading="injectionLoading" :disabled="injectionLoading">
            <el-icon><Warning /></el-icon>
            注入故障
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { api_injection, ChaosReqtDto } from "@/api/modules/service-server-operation/chaos";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { Cpu, Warning } from "@element-plus/icons-vue";

// 接收父组件传入的 serverId
const props = defineProps<{ serverId: string }>();
const emit = defineEmits(["injectSuccess", "closeDialog"]);
const chaosType = "cpu-full";
const formRef = ref<FormInstance>();

// 注入表单数据和状态
const cpuFullInfo = ref({
  serverInfoId: props.serverId,
  cpuPercent: 80,
  timeout: 180
});

const rules = ref<FormRules>({
  cpuPercent: [
    { required: true, message: "请设置CPU负载百分比", trigger: "change" },
    { type: "number", min: 10, max: 100, message: "负载值必须在10-100%之间", trigger: "change" }
  ],
  timeout: [
    { required: true, message: "请设置持续时长", trigger: "change" },
    { type: "number", min: 10, max: 1800, message: "时长必须在10-1800秒之间", trigger: "change" }
  ]
});

const injectionLoading = ref(false);

// 关闭对话框
const closeDialog = () => {
  emit("closeDialog");
};

// 执行注入
async function injectCpu() {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (!valid) {
      return;
    }
    injectionLoading.value = true;
    const dto: ChaosReqtDto = {
      serverInfoId: cpuFullInfo.value.serverInfoId,
      chaosType,
      chaosCommandPrefix: "create cpu fullload",
      paramMap: {
        "--cpu-percent": cpuFullInfo.value.cpuPercent,
        "--timeout": cpuFullInfo.value.timeout
      },
      targetInfo: `CPU负载: ${cpuFullInfo.value.cpuPercent}%`
    };

    api_injection(dto)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("CPU满载故障注入成功");
          emit("injectSuccess");
          closeDialog();
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        injectionLoading.value = false;
      });
  });
}
</script>

<style scoped lang="scss">
.cpu-full-injection {
  padding: 0 16px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    .title-area {
      display: flex;
      align-items: center;

      .icon {
        color: #f56c6c;
        font-size: 20px;
        margin-right: 8px;
      }

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .tag {
      font-size: 12px;
    }
  }

  .divider {
    margin: 12px 0;
  }

  .description {
    margin-bottom: 20px;
  }

  .injection-form {
    padding: 16px 0;

    .form-row {
      margin-bottom: 20px;
    }

    .duration-input {
      display: flex;
      align-items: center;

      .unit {
        margin-left: 8px;
        color: #606266;
      }
    }

    .time-preview {
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 30px;
    }
  }
}
</style>
