<template>
  <div class="card" v-loading="cardLoading">
    <div>
      <span v-if="recordStatus === '0'">
        <el-button type="primary" plain size="small" @click="showDrawer">开始录制</el-button>
        &nbsp;
        <el-tag type="warning">录制已停止</el-tag>
      </span>
      <span v-if="recordStatus === '1'">
        <el-popconfirm title="确定要暂停录制吗？" disabled width="190">
          <template #reference>
            <el-button type="danger" plain size="small">暂停录制</el-button>
          </template>
        </el-popconfirm>
        &nbsp;
        <el-tag type="success">录制进行中</el-tag>
      </span>
      &nbsp;
      <el-button type="info" plain size="small" @click="refresh">刷新</el-button>
    </div>

    <el-drawer class="start-record-drawer" v-model="drawerVisible">
      <el-form class="start-record-form" label-width="90" label-position="right">
        <el-form-item label="录制环境">
          <el-select v-model="startRecordForm.recordDBEnvIdList" clearable multiple :multiple-limit="3" placeholder="选择录制环境">
            <el-option v-for="item in recordDBEnvList" :value="item.id!" :label="item.envName" :key="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="服务提供方">
          <el-select v-model="startRecordForm.recordServiceProviderList" clearable filterable multiple placeholder="录制指定服务提供方">
            <el-option v-for="item in serviceProviderList" :value="item.serviceProvider" :label="item.serviceProvider" :key="item.serviceProvider"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="start-record-btns">
        <el-button type="primary" disabled>开始录制</el-button>
        <el-button type="info" plain @click="() => drawerVisible = false">取消</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import {api_queryRecordDBEnvList, RecordDBEnv} from "@/api/modules/esbNetworkReplay/recordEnvConfig";
import { ElMessage } from "element-plus";
import {StartRecordForm} from "@/api/modules/esbNetworkReplay/record";
import {api_getServiceModelFiledValue, EsbServiceModel} from "@/api/modules/service-esb-data/service-model";

const emits = defineEmits(["refreshComponent"]);

const recordStatus = ref("0");
const drawerVisible = ref(false);
const recordDBEnvList = ref<RecordDBEnv[]>();
const startRecordForm = ref<StartRecordForm>({
  recordDBEnvIdList: [],
  recordServiceProviderList: [],
});
const serviceProviderList = ref<EsbServiceModel[]>();
const cardLoading = ref<boolean>(false);

const showDrawer = function () {
  queryRecordDBEnv();
  queryServiceProviderList();
  drawerVisible.value = true;
}

const queryRecordDBEnv = function () {
  api_queryRecordDBEnvList().then(res => {
    if (res.respCode === 2000) {
      recordDBEnvList.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
}

const refresh = function () {
  emits("refreshComponent");
}

const queryServiceProviderList = function () {
  api_getServiceModelFiledValue("SERVICE_PROVIDER").then(res => {
    if (res.respCode === 2000) {
      serviceProviderList.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
}

</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
