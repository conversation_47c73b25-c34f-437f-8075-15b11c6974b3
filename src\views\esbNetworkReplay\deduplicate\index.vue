<template>
  <div class="container">
    <div class="card">
      <el-form inline>
        <el-form-item label="接口ID" style="width: 160px">
          <el-select size="small" v-model="searchCondition.interfaceId" @change="queryVersionNumberList" filterable>
            <el-option v-for="item in interfaceIdOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" style="width: 120px">
          <el-select size="small" v-model="searchCondition.type" @change="queryVersionNumberList" disabled>
            <el-option key="reqt" label="reqt" value="reqt"/>
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" style="width: 130px">
          <el-select size="small" v-model="searchCondition.versionNumber" @change="queryNameList">
            <el-option v-for="item in filterFormData.versionNumberList" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" style="width: 600px;">
          <el-select size="small" v-model="searchCondition.name">
            <el-option v-for="item in filterFormData.nameList" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="queryFieldTree" color="#5f4646" plain>配置去重忽略</el-button>
          <el-button type="primary" size="small" @click="processDeduplicate">开始去重</el-button>
          <el-button type="primary" size="small" @click="showSampleXml" color="#564463" plain>查看样例报文</el-button>
          <el-button type="primary" size="small" @click="getLatestSampleXml" color="#c6d3db">获取最新数据</el-button>
        </el-form-item>
        <el-form-item label="当前接口报文获取时间">
          <el-tag round effect="light" type="primary">{{ sampleXmlUpdateTime }}</el-tag>
        </el-form-item>
      </el-form>
    </div>

    <div class="card" style="height: 50%">
      <el-scrollbar>
        <el-tree
          style="margin-left: 25%; max-width: 600px"
          :data="fieldTree"
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          highlight-current
          ref="fieldTreeRef"
        >
          <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span v-if="data.isAdded || data.isAddedGlobal" style="color: #d12b58">{{ node.label }}<el-icon><Warning/></el-icon></span>
          <span v-else>{{ node.label }}</span>
          <span>
            <el-button v-if="!data.isAdded" size="small" type="primary" link @click="addIgnoredField(data)">添加</el-button>
            <el-button v-if="data.isAdded" size="small" type="danger" link @click="removeIgnoredField(data)">删除</el-button>
            <el-button v-if="!data.isAddedGlobal" size="small" type="warning" link @click="addIgnoredFieldGlobal(data)">添加全局</el-button>
            <el-button v-if="data.isAddedGlobal" size="small" type="danger" link @click="removeIgnoredFieldGlobal(data)">删除全局</el-button>
          </span>
        </span>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>

    <el-dialog v-model="sampleXmlDialog.visible" width="70%" :title="sampleXmlDialog.title" @closed="initSampleDialog" draggable>
      <el-input v-model="sampleXmlDialog.sampleXml" type="textarea" :rows="20" style="white-space: nowrap;"/>
      <el-button type="primary" style="margin-left: 90%; margin-top: 1%" @click="updateSampleXml">保存修改</el-button>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  api_downloadSampleXmlFromRemote,
  api_getFieldTree,
  api_getSampleXml,
  api_getValueByCondition, api_updateSampleXmlContent,
  SampleXmlDto
} from "@/api/modules/service-esb-data/sample-xml";
import {onBeforeMount, ref} from "vue";
import {TreeFilterNode} from "@/api/interface";
import {ElMessage} from "element-plus";
import {Warning} from "@element-plus/icons-vue";
import {api_fetchInfoColData} from "@/api/modules/service-enr/record";
import {
  api_addIgnoredFieldGlobal, api_deleteIgnoredFieldGlobal,
  api_fetchIgnoredField,
  api_fetchIgnoredFieldGlobal,
  api_updateIgnoredField
} from "@/api/modules/service-enr/deduplicate";

interface FilterFormData {
  typeList: string[];
  versionNumberList: string[];
  nameList: string[];
}

interface FieldTree extends TreeFilterNode {
  isAdded?: boolean;
  isAddedGlobal?: boolean;
}

const interfaceIdOptions = ref<string[]>([]);
const searchCondition = ref<SampleXmlDto>({
  interfaceId: "",
  type: 'reqt',
  versionNumber: "",
  name: "",
  sampleXml: ""
});
const filterFormData = ref<FilterFormData>({
  typeList: [],
  versionNumberList: [],
  nameList: [],
});
const sampleXmlDialog = ref({
  visible: false,
  sampleXml: "",
  title: "样例报文"
});
const sampleXmlUpdateTime = ref<string>("");
const fieldTree = ref<FieldTree[]>([]);
const fieldTreeRef = ref();

const queryInterfaceIdOptions = function () {
  api_fetchInfoColData(["INTERFACE_ID"]).then((res) => {
    if (res.respCode === 2000) {
      interfaceIdOptions.value = res.respData.map(item => item.interfaceId!);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
};

const initVersionNumber = function () {
  searchCondition.value.versionNumber = "";
  filterFormData.value.versionNumberList = [];
  fieldTree.value = [];
};

const initName = function () {
  searchCondition.value.name = "";
  filterFormData.value.nameList = [];
  fieldTree.value = [];
};

const queryVersionNumberList = function () {
  initVersionNumber();
  initName();
  api_getValueByCondition(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.versionNumberList = [...new Set(res.respData.sampleXmlDtoList.map(item => item.versionNumber))];
      sampleXmlUpdateTime.value = res.respData.updateTime;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const queryNameList = function () {
  initName();
  api_getValueByCondition(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.nameList = res.respData.sampleXmlDtoList.map(item => item.name);
      sampleXmlUpdateTime.value = res.respData.updateTime;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const showSampleXml = function () {
  sampleXmlDialog.value.visible = true;
  querySampleXMl();
};

const initSampleDialog = function () {
  sampleXmlDialog.value.sampleXml = "";
  sampleXmlDialog.value.title = "";
};

const querySampleXMl = function () {
  api_getSampleXml(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      sampleXmlDialog.value.sampleXml = res.respData.sampleXml;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
};

const processDeduplicate = function () {
  // api_deduplicate(searchCondition.value.interfaceId, searchCondition.value.versionNumber).then(res => {
  //   if (res.respCode === 2000) {
  //     ElMessage.success("去重成功");
  //   }
  //   if (res.respCode === 5000) {
  //     ElMessage.error(res.respMsg);
  //   }
  // })
};

const queryFieldTree = async function () {
  await api_getFieldTree(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      fieldTree.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
  queryIgnoredField();
  queryIgnoredFieldGlobal();
};

const queryIgnoredField = function () {
  api_fetchIgnoredField(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      updateTreeWithIds(fieldTree.value[0], res.respData, false);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
};

const updateSampleXml = function () {
  api_updateSampleXmlContent({
    interfaceId: searchCondition.value.interfaceId,
    sampleXml: sampleXmlDialog.value.sampleXml,
    name: searchCondition.value.name,
    type: searchCondition.value.type,
    versionNumber: searchCondition.value.versionNumber
  }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("修改成功");
      querySampleXMl();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    sampleXmlDialog.value.visible = false;
  })
};

const addIgnoredField = function (data) {
  data.isAdded = true;
  updateIgnoredField();
};

const removeIgnoredField = function (data) {
  data.isAdded = false;
  updateIgnoredField();
};

const updateIgnoredField = function () {
  const tree = fieldTreeRef.value.data[0];
  const addedNodeList = getAddedNode(tree);
  api_updateIgnoredField({
    interfaceId: searchCondition.value.interfaceId,
    ignoredFieldList: addedNodeList
  }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("操作成功");
      queryIgnoredField();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
};

const queryIgnoredFieldGlobal = function () {
  api_fetchIgnoredFieldGlobal().then(res => {
    if (res.respCode === 2000) {
      updateTreeWithIds(fieldTree.value[0], res.respData, true);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const addIgnoredFieldGlobal = function (data: FieldTree) {
  api_addIgnoredFieldGlobal(data.id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("添加全局成功");
      queryIgnoredFieldGlobal();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const removeIgnoredFieldGlobal = function (data: FieldTree) {
  api_deleteIgnoredFieldGlobal(data.id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除全局成功");
      queryIgnoredFieldGlobal();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
};

const getAddedNode = function (tree: any) {
  let result: string[] = [];

  function traverse(node: any) {
    if (node.isAdded) {
      result.push(node.id);
    }

    if (node.children && node.children.length > 0) {
      node.children.forEach(child => traverse(child));
    }
  }

  traverse(tree);
  return result;
};

const updateTreeWithIds = function(tree: FieldTree, ids: string[], isGlobal: boolean) {
  // 如果当前节点的 id 在数组中，则将 isAdded 设置为 true
  if (isGlobal) {
    tree.isAddedGlobal = ids.includes(tree.id);
  } else {
    tree.isAdded = ids.includes(tree.id);
  }

  // 如果有子节点，递归调用函数处理每个子节点
  if (tree.children && tree.children.length > 0) {
    tree.children.forEach(child => updateTreeWithIds(child, ids, isGlobal));
  }
};

const getLatestSampleXml = async function () {
  initVersionNumber();
  initName();
  initSampleDialog();
  fieldTree.value = [];
  await api_downloadSampleXmlFromRemote(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("获取最新数据成功");
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
  queryVersionNumberList();
};

onBeforeMount(() => {
  queryInterfaceIdOptions();
});

</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
