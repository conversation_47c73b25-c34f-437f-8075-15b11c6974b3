import http from "@/api";
import { SERVICE_INTEGRATED_TESTER } from "@/api/config/servicePort";

export const api_upload = (uploadId: string, fileIndex: string) => {
  return (
    import.meta.env.VITE_API_URL +
    SERVICE_INTEGRATED_TESTER +
    "/financialStatement/upload?uploadId=" +
    uploadId +
    "&fileIndex=" +
    fileIndex
  );
};

export const api_cleanUpload = (uploadId: string, fileIndex: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/financialStatement/cleanUpload", { uploadId, fileIndex });
};

export const api_getCompareTypes = () => {
  return http.get<Record<string, string>>(SERVICE_INTEGRATED_TESTER + "/financialStatement/getCompareTypes");
};

export const api_compare = (fileType: string, uploadId: string, valueDiff: number) => {
  return http.get<string>(SERVICE_INTEGRATED_TESTER + "/financialStatement/compare", { fileType, uploadId, valueDiff });
};
