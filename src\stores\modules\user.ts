import { defineStore } from "pinia";
import { ref } from "vue";

export const useUserStore = defineStore("user", () => {
  // 用户token
  const token = ref<string>(localStorage.getItem("token") || "");
  // 用户信息
  const userInfo = ref<any>({});
  // 访问模式: normal(正常模式), guest(访客模式)
  const accessMode = ref<string>(localStorage.getItem("accessMode") || "guest");
  // 用户角色
  const userRole = ref<string[]>([]);

  // 设置token
  const setToken = (value: string) => {
    token.value = value;
    localStorage.setItem("token", value);
  };

  // 设置用户信息
  const setUserInfo = (info: any) => {
    userInfo.value = info;
  };

  // 设置访问模式
  const setAccessMode = (mode: string) => {
    accessMode.value = mode;
    localStorage.setItem("accessMode", mode);
  };

  // 设置用户角色
  const setUserRole = (roles: string[]) => {
    userRole.value = roles;
  };

  // 清除用户信息
  const clearUserInfo = () => {
    token.value = "";
    userInfo.value = {};
    accessMode.value = "guest";
    userRole.value = [];
    localStorage.removeItem("token");
    localStorage.setItem("accessMode", "guest");
  };

  // 判断是否登录
  const isLogin = () => {
    return !!token.value;
  };

  return {
    token,
    userInfo,
    accessMode,
    userRole,
    setToken,
    setUserInfo,
    setAccessMode,
    setUserRole,
    clearUserInfo,
    isLogin
  };
}); 