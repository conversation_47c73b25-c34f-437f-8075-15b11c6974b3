<template>
  <el-config-provider :size="'default'" :button="buttonConfig">
    <router-view></router-view>
  </el-config-provider>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { useTheme } from "@/hooks/useTheme";
import { ElConfigProvider } from "element-plus";

// init theme
const { initTheme } = useTheme();
initTheme();

// element button config
const buttonConfig = reactive({ autoInsertSpace: false });
</script>
