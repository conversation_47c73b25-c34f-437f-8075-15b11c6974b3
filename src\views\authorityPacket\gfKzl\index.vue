<template>
  <div v-loading="cardLoading">
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span class="title">高法-控制类</span>
        </div>
      </template>

      <el-collapse v-model="activeNames" class="query-form-collapse">
        <el-collapse-item title="kzqq" name="kzqq">
          <div class="form-toolbar">
            <el-select v-model="selectedKzqqInfoId" size="small" placeholder="选择常用信息模板" clearable style="width: 220px">
              <el-option v-for="item in kzqqInfoList" :key="item.id" :value="item.id!" :label="item.infoName" />
            </el-select>
            
            <el-button type="primary" size="small" v-if="selectedKzqqInfoId" @click="updateKzqqInfo" plain>
              <el-icon><RefreshRight /></el-icon>更新
            </el-button>
            <el-popconfirm 
              title="确定要删除这条常用信息吗?" 
              v-if="selectedKzqqInfoId" 
              @confirm="deleteKzqqInfo"
              confirm-button-text="删除"
              cancel-button-text="取消"
            >
              <template #reference>
                <el-button type="danger" size="small" plain>
                  <el-icon><Delete /></el-icon>删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>

          <el-form label-width="200" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="BDHM[请求单号]">
                  <el-input v-model="attrValues.kzqq.bdhm" style="width: 210px;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="XM[客户名称]">
                  <el-input v-model="attrValues.kzqq.xm" style="width: 260px;" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="ZJLX[证件类型]">
                  <el-select placeholder="请选择证件类型" clearable v-model="attrValues.kzqq.zjlx" filterable style="width: 200px;">
                    <el-option
                      v-for="item in idType2"
                      :key="item.key"
                      :value="item.key"
                      :label="item.key + '（' + item.label + '）'"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="DSRZJHM[证照号码(证件号)]">
                  <el-input v-model="attrValues.kzqq.dsrzjhm" style="width: 260px;" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          
          <div class="form-bottom-actions">
            <el-button type="success" size="small" @click="saveKzqqInfo" plain>
              <el-icon><Download /></el-icon>保存为常用信息
            </el-button>
          </div>
        </el-collapse-item>

        <el-collapse-item v-for="(kzzh, index) in attrValues.kzzhList" :key="index" :name="'kzzh' + index">
          <template #title>
            <div class="collapse-title">
              <span>kzzh</span>
            </div>
          </template>

          <div class="form-toolbar">
            <el-select
              v-model="selectedCommonInfoIdList[index]"
              @change="commonInfoChange(index)"
              size="small"
              placeholder="选择常用信息模板"
              clearable
              style="width: 220px"
            >
              <el-option v-for="item in commonInfoList" :key="item.id" :value="item.id!" :label="item.infoName" />
            </el-select>
            
            <el-button
              type="primary"
              size="small"
              v-if="selectedCommonInfoIdList[index]"
              @click="updateCommonInfo('kzzh', index)"
              plain
            >
              <el-icon><RefreshRight /></el-icon>更新
            </el-button>
            <el-popconfirm 
              title="确定要删除这条常用信息吗?" 
              v-if="selectedCommonInfoIdList[index]" 
              @confirm="deleteCommonInfo(index)"
              confirm-button-text="删除"
              cancel-button-text="取消"
            >
              <template #reference>
                <el-button type="danger" size="small" plain>
                  <el-icon><Delete /></el-icon>删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>

          <el-form label-width="180" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="KZLX[控制类型]">
                  <el-select v-model="kzzh.kzlx" clearable style="width: 200px;">
                    <el-option key="1" value="1" label="1-存款" />
                    <el-option key="2" value="2" label="2-理财" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="KZCS[控制措施]">
                  <el-select v-model="kzzh.kzcs" clearable style="width: 200px;">
                    <el-option key="01" value="01" label="01-冻结" />
                    <el-option key="02" value="02" label="02-续冻" />
                    <el-option key="04" value="04" label="04-解冻" />
                    <el-option key="06" value="06" label="06-扣划" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="YDJDH[原冻结单号]">
                  <el-input v-model="kzzh.ydjdh" style="width: 260px;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="KHZH[开户账号]">
                  <el-input v-model="kzzh.khzh" style="width: 260px;" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="KZNR[冻结方式]">
                  <el-select v-model="kzzh.kznr" clearable @change="kzzh.je = ''" style="width: 200px;">
                    <el-option key="1" value="1" label="1-金额冻结" />
                    <el-option key="2" value="2" label="2-账户冻结" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="JE[冻结金额]">
                  <el-input v-model="kzzh.je" style="width: 260px;" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="BZ[币种]">
                  <el-select placeholder="请选择币种" v-model="kzzh.bz" clearable filterable style="width: 200px;">
                    <el-option
                      v-for="item in currencyType2"
                      :key="item.key"
                      :label="item.key + '（' + item.label + '）'"
                      :value="item.key"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="KSRQ[控制开始日期]">
                  <el-date-picker
                    v-model="kzzh.ksrq"
                    type="date"
                    placeholder="选择开始时间"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    @change="onTimeChange(kzzh)"
                    style="width: 200px;"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="JSRQ[控制结束日期]">
                  <el-date-picker
                    v-model="kzzh.jsrq"
                    type="date"
                    placeholder="选择结束时间"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    @change="onTimeChange(kzzh)"
                    style="width: 200px;"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          
          <div class="form-bottom-actions">
            <el-button type="success" size="small" @click="saveCommonInfo('kzzh', kzzh)" plain>
              <el-icon><Download /></el-icon>保存为常用信息
            </el-button>
          </div>
        </el-collapse-item>
      </el-collapse>

      <div class="form-actions-bottom">
        <el-button type="info" plain size="small" @click="initRandomDigits">
          <el-icon><Refresh /></el-icon>刷新随机数
        </el-button>
        <el-button type="primary" size="small" @click="addKzzh" plain>
          <el-icon><Plus /></el-icon>添加kzzh
        </el-button>
        <el-button type="danger" size="small" @click="deleteKzzh" plain v-if="attrValues.kzzhList.length !== 1">
          <el-icon><Delete /></el-icon>删除kzzh
        </el-button>
      </div>
    </el-card>

    <el-card class="action-card">
      <template #header>
        <div class="card-header">
          <span class="title">操作</span>
        </div>
      </template>

      <div class="action-buttons">
        <el-button type="warning" @click="generatePacket">
          <el-icon><Document /></el-icon>生成报文
        </el-button>
        <el-button type="primary" @click="downloadZipPacket" :disabled="!packetZipName">
          <el-icon><Download /></el-icon>下载
        </el-button>
        <el-button type="success" @click="ftpUpload" :disabled="!packetZipName">
          <el-icon><Upload /></el-icon>上传至服务器
        </el-button>
      </div>

      <div v-if="packetZipName" class="file-info">
        <el-tag type="success">文件已生成: {{ packetZipName }}</el-tag>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  api_deleteCommonInfo,
  api_downloadZipPacket,
  api_editCommonInfo,
  api_ftpUpload,
  api_generatePacketGf,
  api_getCommonInfo,
  api_getXmlStructureTree,
  api_saveTemplate,
  EntryDto,
  PacketCommonInfo,
  XmlTreeDto
} from "@/api/modules/service-integrated-tester/authority-packet";
import { onMounted, ref, watch } from "vue";
import currencyType2 from "@/data/currencyType2.json";
import idType2 from "@/data/idType2.json";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete, Download, Refresh, RefreshRight, Document, Upload } from "@element-plus/icons-vue";

const packetType = "高法-控制类";
const templateFolderName = "KZQQF059H1013702010010000AVYHXD00036120210526067860";
const templateXmlName = "QAF059H1013702010010000AVYHXD00036120210526067860.xml";
const ftpRemotePath = "/最高人民法院/Download";
const templateKzzh = {
  kzlx: "",
  kzcs: "",
  ydjdh: "",
  khzh: "",
  kznr: "",
  je: "",
  bz: "",
  ksrq: "",
  jsrq: ""
};

const lastRandomDigits = ref();
const cardLoading = ref(false);
const activeNames = ref(["kzqq", "kzzh0"]);
const kzqqInfoList = ref<PacketCommonInfo[]>([]);
const selectedKzqqInfoId = ref<string>();
const selectedKzqqInfo = ref<PacketCommonInfo>();
const commonInfoList = ref<PacketCommonInfo[]>([]);
const selectedCommonInfoIdList = ref<(string | undefined)[]>([]);
const selectedCommonInfoList = ref<(PacketCommonInfo | undefined)[]>([]);
const packetXml = ref<XmlTreeDto>();
const attrValues = ref({
  kzqq: {
    bdhm: "",
    xm: "",
    dsrzjhm: "",
    zjlx: ""
  },
  kzzhList: [{ ...templateKzzh }]
});
const packetZipName = ref<string>();

const initRandomDigits = function () {
  lastRandomDigits.value =
    "0000AVYHXD" +
    Date.now().toString() +
    Math.floor(Math.random() * 10000000)
      .toString()
      .padStart(7, "0");
  attrValues.value.kzqq.bdhm = lastRandomDigits.value.substring(8);
};

const queryTemplateXml = function () {
  cardLoading.value = true;
  api_getXmlStructureTree({
    templateFolderName,
    templateXmlName
  })
    .then(res => {
      if (res.respCode === 2000) {
        packetXml.value = res.respData;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      cardLoading.value = false;
    });
};

const rulesCheck = function () {
  if (!attrValues.value.kzqq.bdhm) {
    ElMessage.error("BHDM[请求单号]为必填项");
    return false;
  }
  if (!attrValues.value.kzqq.xm) {
    ElMessage.error("XM[客户名称]为必填项");
    return false;
  }
  if (!attrValues.value.kzqq.dsrzjhm) {
    ElMessage.error("DJRZJHM[证照号码(证件号)]为必填项");
    return false;
  }
  for (let i = 0; i < attrValues.value.kzzhList.length; i++) {
    const currKzzh = attrValues.value.kzzhList[i];
    const errMsgPrefix = "第" + (i + 1) + "个元素 - ";
    if (!currKzzh.kzlx) {
      ElMessage.error(errMsgPrefix + "KZLX[控制类型]为必填项");
      return false;
    }
    if (!currKzzh.kzcs) {
      ElMessage.error(errMsgPrefix + "KZCS[控制措施]为必填项");
      return false;
    }
    if (!currKzzh.khzh) {
      ElMessage.error(errMsgPrefix + "KHZH[开户账号]为必填项");
      return false;
    }
    if (!currKzzh.kznr) {
      ElMessage.error(errMsgPrefix + "KZNR[冻结方式]为必填项");
      return false;
    }
    if (currKzzh.kznr === "1") {
      if (!currKzzh.je) {
        ElMessage.error(errMsgPrefix + "KZNR[冻结方式]为1-金额冻结时，JE[冻结金额]必填");
        return false;
      }
      const regex = /^\d+\.\d{2}$/;
      if (!regex.test(currKzzh.je)) {
        ElMessage.error(errMsgPrefix + "JE[冻结金额]小数点后须保留两位");
        return false;
      }
    }
    if (!currKzzh.bz) {
      ElMessage.error(errMsgPrefix + "BZ[币种]为必填项");
      return false;
    }
    if (!currKzzh.ksrq) {
      ElMessage.error(errMsgPrefix + "KSSQ[控制开始日期]为必填项");
      return false;
    }
    if (!currKzzh.jsrq) {
      ElMessage.error(errMsgPrefix + "JSRQ[控制结束日期]为必填项");
      return false;
    }
  }
  return true;
};

const assignValue = function () {
  const kzqq = packetXml.value?.children.find(item => item.tag === "kzqq");
  for (let key in attrValues.value.kzqq) {
    kzqq!.attributeList.find(item => item.key === key.toUpperCase())!.value = attrValues.value.kzqq[key];
  }
  const templateKzzhTree = JSON.parse(
    JSON.stringify(packetXml.value?.children[0].children.find(item => item.tag === "kzzhlist")?.children[0])
  );
  for (let i = 0; i < attrValues.value.kzzhList.length; i++) {
    const currKzzh = attrValues.value.kzzhList[i];
    const currTree = JSON.parse(JSON.stringify(templateKzzhTree));
    for (let key in currKzzh) {
      currTree!.attributeList.find((item: EntryDto) => item.key === key.toUpperCase())!.value = currKzzh[key];
    }
    packetXml.value!.children[0].children.find(item => item.tag === "kzzhlist")!.children[i] = currTree!;
  }
};

const generatePacket = function () {
  cardLoading.value = true;
  if (!rulesCheck()) {
    cardLoading.value = false;
    return;
  }
  assignValue();
  api_generatePacketGf({
    xmlTreeDto: packetXml.value!,
    templateFolderName,
    templateXmlName,
    lastRandomDigits: lastRandomDigits.value
  })
    .then(res => {
      if (res.respCode === 2000) {
        packetZipName.value = res.respData;
        ElMessage.success("报文包生成成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      cardLoading.value = false;
    });
};

const downloadZipPacket = function () {
  if (!packetZipName.value) {
    ElMessage.error({ message: "无文件下载，请先生成文件", showClose: true, duration: 10000 });
    return;
  }
  api_downloadZipPacket(packetZipName.value!);
};

const ftpUpload = function () {
  cardLoading.value = true;
  if (!packetZipName.value) {
    ElMessage.warning({ message: "无文件上传，请先生成文件", showClose: true, duration: 10000 });
    cardLoading.value = false;
    return;
  }
  api_ftpUpload(packetZipName.value, ftpRemotePath)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("上传成功");
      } else {
        ElMessage.error({ message: res.respMsg, showClose: true, duration: 10000 });
      }
    })
    .finally(() => {
      cardLoading.value = false;
    });
};

const queryKzqqInfo = function () {
  api_getCommonInfo({
    infoName: "",
    packetType,
    tagName: "kzqq",
    infoContent: ""
  }).then(res => {
    if (res.respCode === 2000) {
      kzqqInfoList.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const initKzqq = function () {
  const kzqq = attrValues.value.kzqq;
  kzqq.dsrzjhm = "";
  kzqq.xm = "";
  kzqq.zjlx = "";
};

const saveKzqqInfo = function () {
  let copyKzqq = JSON.parse(JSON.stringify(attrValues.value.kzqq));
  delete copyKzqq.bdhm;
  ElMessageBox.prompt("请输入名称", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(({ value }) => {
    cardLoading.value = true;
    api_saveTemplate({
      infoName: value,
      packetType,
      tagName: "kzqq",
      infoContent: JSON.stringify(copyKzqq)
    })
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("信息保存成功");
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        queryKzqqInfo();
        cardLoading.value = false;
      });
  });
};

const updateKzqqInfo = function () {
  let copyKzqq = JSON.parse(JSON.stringify(attrValues.value.kzqq));
  delete copyKzqq.bdhm;
  const newKzqqInfo: PacketCommonInfo = {
    id: selectedKzqqInfoId.value,
    infoName: selectedKzqqInfo.value!.infoName,
    packetType,
    tagName: "kzqq",
    infoContent: copyKzqq
  };
  api_editCommonInfo(newKzqqInfo)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("更新成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      queryKzqqInfo();
    });
};

const deleteKzqqInfo = function () {
  api_deleteCommonInfo(selectedKzqqInfoId.value!)
    .then(res => {
      if (res.respCode === 2000) {
        initKzqq();
        selectedKzqqInfoId.value = undefined;
        ElMessage.success("删除成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      queryKzqqInfo();
    });
};

const initKzzh = function (listIndex: number) {
  const kzzh = attrValues.value.kzzhList[listIndex];
  kzzh.kzlx = "";
  kzzh.kzcs = "";
  kzzh.ydjdh = "";
  kzzh.khzh = "";
  kzzh.kznr = "";
  kzzh.je = "";
  kzzh.bz = "";
  kzzh.ksrq = "";
  kzzh.jsrq = "";
};

const queryCommonInfo = function (tagName: string) {
  api_getCommonInfo({
    infoName: "",
    packetType,
    tagName,
    infoContent: ""
  }).then(res => {
    if (res.respCode === 2000) {
      commonInfoList.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const commonInfoChange = function (listIndex: number) {
  initKzzh(listIndex);
  if (!selectedCommonInfoIdList.value[listIndex]) {
    selectedCommonInfoList.value[listIndex] = undefined;
    return;
  }
  selectedCommonInfoList.value[listIndex] = commonInfoList.value.find(
    item => item.id === selectedCommonInfoIdList.value[listIndex]
  );
  const currKzzh = attrValues.value.kzzhList[listIndex];
  const commonInfoJson = JSON.parse(selectedCommonInfoList.value[listIndex]!.infoContent);
  for (let key in commonInfoJson) {
    currKzzh[key] = commonInfoJson[key];
  }
};

const saveCommonInfo = function (tagName: string, kzzh: any) {
  let copyKzzh = JSON.parse(JSON.stringify(kzzh));
  ElMessageBox.prompt("请输入名称", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(({ value }) => {
    cardLoading.value = true;
    api_saveTemplate({
      infoName: value,
      packetType,
      tagName,
      infoContent: JSON.stringify(copyKzzh)
    })
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("信息保存成功");
          queryCommonInfo(tagName);
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        cardLoading.value = false;
      });
  });
};

const updateCommonInfo = function (tagName: string, listIndex: number) {
  let copyKzzh = JSON.parse(JSON.stringify(attrValues.value.kzzhList[listIndex]));
  const newCommonInfo: PacketCommonInfo = {
    id: selectedCommonInfoIdList.value[listIndex],
    infoName: selectedCommonInfoList.value[listIndex]!.infoName,
    packetType,
    tagName,
    infoContent: JSON.stringify(copyKzzh)
  };
  api_editCommonInfo(newCommonInfo)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("更新成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      queryCommonInfo("kzzh");
    });
};

const deleteCommonInfo = function (listIndex: number) {
  api_deleteCommonInfo(selectedCommonInfoIdList.value[listIndex]!)
    .then(res => {
      if (res.respCode === 2000) {
        selectedCommonInfoIdList.value[listIndex] = undefined;
        commonInfoChange(listIndex);
        ElMessage.success("删除成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      queryCommonInfo("kzzh");
    });
};

const onTimeChange = function (kzzh: any) {
  if (!kzzh.ksrq) {
    kzzh.ksrq = "";
  }
  if (!kzzh.jsrq) {
    kzzh.jsrq = "";
  }
};

const addKzzh = function () {
  attrValues.value.kzzhList.push({ ...templateKzzh });
  initKzzh(attrValues.value.kzzhList.length - 1);
  selectedCommonInfoIdList.value.push(undefined);
  selectedCommonInfoList.value.push(undefined);
};

const deleteKzzh = function () {
  attrValues.value.kzzhList.pop();
  selectedCommonInfoIdList.value.pop();
  selectedCommonInfoList.value.pop();
};

watch(
  () => selectedKzqqInfoId.value,
  () => {
    initKzqq();
    if (!selectedKzqqInfoId.value) {
      selectedKzqqInfo.value = undefined;
      return;
    }
    selectedKzqqInfo.value = kzqqInfoList.value.find(item => item.id === selectedKzqqInfoId.value);
    const kzqq = attrValues.value.kzqq;
    const kzqqInfoJson = JSON.parse(selectedKzqqInfo.value!.infoContent);
    for (let key in kzqqInfoJson) {
      kzqq[key] = kzqqInfoJson[key];
    }
  }
);

onMounted(() => {
  queryTemplateXml();
  queryCommonInfo("kzzh");
  queryKzqqInfo();
  initRandomDigits();
  initKzzh(0);
});
</script>

<style scoped lang="scss">
.query-card,
.action-card {
  margin-bottom: 16px;
  border-radius: 8px;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  background: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  gap: 8px;
}

.query-form {
  margin-top: 10px;

  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-picker) {
    width: 100%;
  }
}

.query-form-collapse {
  :deep(.el-collapse-item__header) {
    padding: 0 8px;

    &.is-active {
      background-color: #ecf5ff;
      border-bottom-color: #d9ecff;
    }
  }

  :deep(.el-collapse-item__wrap) {
    padding: 16px;
    background-color: #fbfbfb;
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.file-info {
  margin-top: 16px;
}

.form-actions-bottom {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.form-bottom-actions {
  margin-top: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>
