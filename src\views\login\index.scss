.login-container {
  height: 100%;
  min-height: 550px;
  background-color: #eeeeee;
  background-image: url("@/assets/images/login_bg.svg");
  background-size: cover;
  background-position: center;
  
  .login-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 96%;
    height: 94%;
    padding: 0 4% 0 20px;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    
    .login-left {
      width: 750px;
      margin-right: 10px;
      
      .login-left-img {
        width: 100%;
        height: 100%;
      }
    }
    
    .login-form {
      width: 420px;
      padding: 40px 45px 25px;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 0.98);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      
      .login-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 40px;
        
        .login-icon {
          width: 60px;
          height: 60px;
        }
        
        .logo-text {
          padding-left: 15px;
          font-size: 42px;
          font-weight: bold;
          color: #34495e;
          white-space: nowrap;
        }
      }
      
      .login-btn {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 40px;
        white-space: nowrap;
        
        .el-button {
          width: 185px;
        }
      }

      .login-guest {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
    }
  }
}

@media screen and (max-width: 1250px) {
  .login-left {
    display: none;
  }
}

@media screen and (max-width: 550px) {
  .login-form {
    width: 100% !important;
  }
}
