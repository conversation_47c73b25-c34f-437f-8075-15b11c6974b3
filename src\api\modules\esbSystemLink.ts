import http from "@/api";
import { TreeFilterNode } from "@/api/interface";
import { PORT_ESL } from "@/api/config/servicePort";

/**
 * ESB系统调用链路模块
 */

// 获取配置项列表
export const queryConfigList = () => {
  return http.get<TreeFilterNode[]>(PORT_ESL + "/getConfigList");
};

// 获取应用系统列表
export const queryESLTreeListByConfigId = (machineName: string) => {
  return http.get<TreeFilterNode[]>(PORT_ESL + "/getESLTreeListByConfigId?configId=" + machineName);
};

// 获取ESB接口列表
export const queryESBInterfaceList = () => {
  return http.get<TreeFilterNode[]>(PORT_ESL + "/getEsbInterfaceList");
};

// 根据ESB接口获取调用系统
export const queryESLTreeListByInterfaceId = (interfaceId: string) => {
  return http.get<TreeFilterNode>(PORT_ESL + "/getESLTreeListByInterfaceId?interfaceId=" + interfaceId);
};

// 获取系统间调用关系
export const queryESLTreeListByServiceProvider = (serviceProvider: string) => {
  return http.get<TreeFilterNode[]>(PORT_ESL + "/getESLTreeListByServiceProvider?serviceProvider=" + serviceProvider);
};
