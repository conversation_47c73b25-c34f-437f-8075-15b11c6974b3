import http from "@/api";
import {SERVICE_INTEGRATED_TESTER} from "@/api/config/servicePort";

export interface DbConnectionInfo {
  id?: number;
  dbName?: string;
  dbType?: string;
  url?: string;
  username?: string;
  password?: string;
}

export const api_getDbConnectionInfo = (dbConnectionInfo?: DbConnectionInfo) => {
  return http.post<DbConnectionInfo[]>(SERVICE_INTEGRATED_TESTER + "/dbManagement/info/list", dbConnectionInfo);
};

export const api_addDbConnectionInfo = (dbConnectionInfo: DbConnectionInfo) => {
  return http.post(SERVICE_INTEGRATED_TESTER + "/dbManagement/info/add", dbConnectionInfo);
};

export const api_updateDbConnectionInfo = (dbConnectionInfo: DbConnectionInfo) => {
  return http.post(SERVICE_INTEGRATED_TESTER + "/dbManagement/info/update", dbConnectionInfo);
};

export const api_deleteDbConnectionInfo = (id: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/dbManagement/info/delete/" + id);
};

export const api_testDbConnectionById = (id: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/dbManagement/testDbConnection/" + id);
};

export const api_testDbConnection = (dbType: string, url: string, username: string, password: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/dbManagement/testDbConnection", {dbType, url, username, password});
};
