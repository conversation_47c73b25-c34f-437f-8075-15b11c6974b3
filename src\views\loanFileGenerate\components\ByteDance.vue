<template>
  <div>
    <!--loan.csv-->
    <el-card shadow="hover">
      <template #header>
        <div class="table-header">
          <span class="table-title">loan.csv</span>
          <el-button type="primary" size="small" @click="addLoanItem" icon="Plus">添加数据</el-button>
        </div>
      </template>

      <el-table
        :data="loanCsvDataList"
        border
        stripe
        highlight-current-row
        style="width: 100%"
        class="loan-table"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column type="index" width="60" label="序号" align="center" fixed />

        <el-table-column prop="scenario" label="场景" width="150" fixed>
          <template #default="scope">
            <el-select v-model="scope.row.scenario" clearable placeholder="请选择" :disabled="scope.row.disabled">
              <el-option v-for="option in scenarioOptions" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
          </template>
        </el-table-column>

        <!-- 动态字段列 -->
        <el-table-column
          v-for="field in loanFieldProps"
          :key="field.prop"
          :prop="field.prop"
          :label="field.label"
          :width="field.width || '150'"
        >
          <template #default="{ row }">
            <el-input
              v-if="field.prop === 'loan_id'"
              v-model="row.loan_id"
              :disabled="row.disabled"
              placeholder="请输入借据号"
              clearable
              @blur="fetchBusinessApply(row)"
            >
              <template #prefix>
                <el-icon>
                  <Document />
                </el-icon>
              </template>
            </el-input>
            <el-date-picker
              v-else-if="field.prop === 'cur_date'"
              v-model="row.cur_date"
              type="date"
              placeholder="请选择日期"
              format="YYYYMMDD"
              value-format="YYYYMMDD"
              style="width: 150px"
            />
            <el-date-picker
              v-else-if="field.prop === 'clear_date'"
              v-model="row.clear_date"
              type="date"
              placeholder="请选择日期"
              format="YYYYMMDD"
              value-format="YYYYMMDD"
              style="width: 150px"
            />
            <el-select v-else-if="field.prop === 'repay_mode'" v-model="row[field.prop]" clearable placeholder="请选择">
              <el-option
                v-for="option in repayModeOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-select v-else-if="field.prop === 'loan_status'" v-model="row.loan_status" clearable placeholder="请选择">
              <el-option
                v-for="option in loanStatusOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-select v-else-if="field.prop === 'loan_form'" v-model="row.loan_form" clearable placeholder="请选择">
              <el-option
                v-for="option in loanFormOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-select
              v-else-if="field.prop === 'interest_transfer_status'"
              v-model="row.interest_transfer_status"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="option in interestTransferStatusOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-input v-else v-model="row[field.prop]" :placeholder="`请输入`" clearable />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="scope">
            <el-button type="danger" size="small" @click="removeByLoanId(scope.row)" icon="Delete" circle plain />
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <span class="record-count">共 {{ loanCsvDataList.length }} 条记录</span>
        <el-button v-if="loanCsvDataList.length > 5" type="primary" size="small" @click="addLoanItem" icon="Plus" plain>
          添加数据
        </el-button>
      </div>
    </el-card>

    <!--repay_plan.csv-->
    <el-card shadow="hover" style="margin-top: 15px">
      <template #header>
        <div class="table-header">
          <span class="table-title">repay_plan.csv</span>
        </div>
      </template>

      <el-table
        :data="repayPlanCsvDataList"
        border
        stripe
        highlight-current-row
        style="width: 100%"
        class="loan-table"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column type="index" width="60" label="序号" align="center" fixed />

        <!-- 动态字段列 -->
        <el-table-column
          v-for="field in repayPlanFieldProps"
          :key="field.prop"
          :prop="field.prop"
          :label="field.label"
          :width="field.width || '150'"
        >
          <template #default="{ row }">
            <el-input v-if="field.prop === 'loan_id'" v-model="row.loan_id" placeholder="请输入借据号" clearable disabled>
              <template #prefix>
                <el-icon>
                  <Document />
                </el-icon>
              </template>
            </el-input>
            <el-select v-else-if="field.prop === 'term_status'" v-model="row.term_status" clearable placeholder="请选择">
              <el-option
                v-for="option in termStatusOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-input v-else v-model="row[field.prop]" :placeholder="`请输入`" clearable />
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <span class="record-count">共 {{ repayPlanCsvDataList.length }} 条记录</span>
      </div>
    </el-card>

    <!--repay_item.csv-->
    <el-card shadow="hover" style="margin-top: 15px">
      <template #header>
        <div class="table-header">
          <span class="table-title">repay_item.csv</span>
          <el-button type="primary" size="small" @click="addRepayItemData" icon="Plus">添加数据</el-button>
        </div>
      </template>

      <el-table
        :data="repayItemCsvDataList"
        border
        stripe
        highlight-current-row
        style="width: 100%"
        class="loan-table"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column type="index" width="60" label="序号" align="center" fixed />

        <!-- 动态字段列 -->
        <el-table-column
          v-for="field in repayItemFieldProps"
          :key="field.prop"
          :prop="field.prop"
          :label="field.label"
          :width="field.width || '150'"
        >
          <template #default="{ row }">
            <el-select
              v-if="field.prop === 'loan_id'"
              v-model="row.loan_id"
              placeholder="请选择借据号"
              filterable
              clearable
              @change="onRepayItemLoanIdChange(row)"
            >
              <el-option
                v-for="loanItem in loanCsvDataList"
                :key="loanItem.loan_id"
                :label="loanItem.loan_id"
                :value="loanItem.loan_id"
                :disabled="!loanItem.loan_id"
              />
            </el-select>
            <el-select v-else-if="field.prop === 'event'" v-model="row.event" clearable placeholder="请选择">
              <el-option
                v-for="option in eventOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-input v-else v-model="row[field.prop]" :placeholder="`请输入`" clearable />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="scope">
            <el-button type="danger" size="small" @click="removeRepayItem(scope.row)" icon="Delete" circle plain />
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <span class="record-count">共 {{ repayItemCsvDataList.length }} 条记录</span>
        <el-button v-if="repayItemCsvDataList.length > 5" type="primary" size="small" @click="addRepayItemData" icon="Plus" plain>
          添加数据
        </el-button>
      </div>
    </el-card>

    <!--repay.csv-->
    <el-card shadow="hover" style="margin-top: 15px">
      <template #header>
        <div class="table-header">
          <span class="table-title">repay.csv</span>
        </div>
      </template>

      <el-table
        :data="repayCsvDataList"
        border
        stripe
        highlight-current-row
        style="width: 100%"
        class="loan-table"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column type="index" width="60" label="序号" align="center" fixed />

        <!-- 动态字段列 -->
        <el-table-column
          v-for="field in repayFieldProps"
          :key="field.prop"
          :prop="field.prop"
          :label="field.label"
          :width="field.width || '150'"
        >
          <template #default="{ row }">
            <el-input v-if="field.prop === 'loan_id'" v-model="row.loan_id" placeholder="请输入借据号" clearable disabled>
              <template #prefix>
                <el-icon>
                  <Document />
                </el-icon>
              </template>
            </el-input>
            <el-select
              v-else-if="field.prop === 'interest_transfer_status'"
              v-model="row.interest_transfer_status"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="option in interestTransferStatusOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-select
              v-else-if="field.prop === 'repay_account_type'"
              v-model="row.repay_account_type"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="option in repayAccountTypeOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-input v-else v-model="row[field.prop]" :placeholder="`请输入`" clearable />
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <span class="record-count">共 {{ repayCsvDataList.length }} 条记录</span>
      </div>
    </el-card>

    <!--open.csv-->
    <el-card shadow="hover" style="margin-top: 15px">
      <template #header>
        <div class="table-header">
          <span class="table-title">open.csv</span>
        </div>
      </template>

      <el-table
        :data="openCsvDataList"
        border
        stripe
        highlight-current-row
        style="width: 100%"
        class="loan-table"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column type="index" width="60" label="序号" align="center" fixed />

        <!-- 动态字段列 -->
        <el-table-column
          v-for="field in openFieldProps"
          :key="field.prop"
          :prop="field.prop"
          :label="field.label"
          :width="field.width || '150'"
        >
          <template #default="{ row }">
            <el-input v-if="field.prop === 'loan_id'" v-model="row.loan_id" placeholder="请输入借据号" clearable disabled>
              <template #prefix>
                <el-icon>
                  <Document />
                </el-icon>
              </template>
            </el-input>
            <el-select v-else-if="field.prop === 'repay_mode'" v-model="row[field.prop]" clearable placeholder="请选择">
              <el-option
                v-for="option in repayModeOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-select v-else-if="field.prop === 'fund_status'" v-model="row[field.prop]" clearable placeholder="请选择">
              <el-option
                v-for="option in fundStatusOptions"
                :key="option.value"
                :label="`${option.value}-${option.label}`"
                :value="option.value"
              />
            </el-select>
            <el-select v-else-if="field.prop === 'usage'" v-model="row.usage" clearable placeholder="请选择">
              <el-option
                v-for="option in ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']"
                :key="option"
                :label="`${option}`"
                :value="option"
              />
            </el-select>
            <el-input v-else v-model="row[field.prop]" :placeholder="`请输入`" clearable />
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <span class="record-count">共 {{ openCsvDataList.length }} 条记录</span>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { Document } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import {
  api_createDestFolder,
  api_download,
  api_getBusinessApply,
  api_getCustomerInfo,
  api_writeCsv
} from "@/api/modules/service-integrated-tester/loan-file-generate";
import { getCurrentDateTime, getCurrentDateYYYYMMDD } from "@/tools/DateTool";

const scenarioOptions = [
  {
    label: "贷款",
    value: "loan"
  },
  {
    label: "结清",
    value: "settle"
  },
  {
    label: "部分还款",
    value: "partial"
  },
  {
    label: "账单还款",
    value: "bill"
  },
  {
    label: "逾期还款",
    value: "overdue"
  },
  {
    label: "自定义",
    value: "customized"
  }
];

const repayModeOptions = [
  {
    label: "等额本金",
    value: "01"
  },
  {
    label: "等额本息",
    value: "02"
  },
  {
    label: "利随本清",
    value: "03"
  },
  {
    label: "等额本息_按期还款",
    value: "04"
  }
];

const loanStatusOptions = [
  {
    label: "放款中",
    value: "01"
  },
  {
    label: "已放款",
    value: "02"
  },
  {
    label: "已冲正",
    value: "03"
  },
  {
    label: "已撤销",
    value: "04"
  },
  {
    label: "已还款",
    value: "05"
  },
  {
    label: "已结清",
    value: "06"
  }
];

const loanFormOptions = [
  {
    label: "正常",
    value: "1"
  },
  {
    label: "逾期",
    value: "2"
  }
];

const interestTransferStatusOptions = [
  {
    label: "应计",
    value: "1"
  },
  {
    label: "非应计",
    value: "2"
  }
];

const repayAccountTypeOptions = [
  {
    label: "个人账户",
    value: "1"
  },
  {
    label: "企业账户",
    value: "2"
  }
];

const eventOptions = [
  {
    label: "逾期还款",
    value: "11"
  },
  {
    label: "正常还款",
    value: "12"
  },
  {
    label: "提前还款",
    value: "13"
  },
  {
    label: "提前结清",
    value: "14"
  },
  {
    label: "延期还款",
    value: "15"
  }
];

const termStatusOptions = [
  {
    label: "正常",
    value: "01"
  },
  {
    label: "逾期",
    value: "02"
  },
  {
    label: "已冲正",
    value: "03"
  },
  {
    label: "已撤销",
    value: "04"
  },
  {
    label: "已结清",
    value: "05"
  },
  {
    label: "延期结清",
    value: "06"
  }
];

const fundStatusOptions = [
  {
    label: "放款中",
    value: "01"
  },
  {
    label: "已放款",
    value: "02"
  },
  {
    label: "已冲正",
    value: "03"
  },
  {
    label: "已撤销",
    value: "04"
  }
];

interface LoanCsvItem {
  name?: string;
  disabled?: boolean;
  scenario?: string;
  loan_id: string;
  cur_date: string;
  apply_date: string;
  start_date: string;
  end_date: string;
  clear_date: string;
  encash_amt: string;
  currency: string;
  repay_mode: string;
  repay_cycle: string;
  total_terms: string;
  cur_term: string;
  repay_day: string;
  grace_day: string;
  loan_status: string;
  loan_form: string;
  prin_total: string;
  prin_repay: string;
  prin_bal: string;
  ovd_prin_bal: string;
  int_plan: string;
  int_total: string;
  int_repay: string;
  int_discount: string;
  int_bal: string;
  ovd_int_bal: string;
  pnlt_int_total: string;
  pnlt_int_repay: string;
  pnlt_int_discount: string;
  pnlt_int_bal: string;
  pre_pmt_fee_repay: string;
  product_no: string;
  out_loan_channel_no: string;
  days_ovd: string;
  interest_transfer_status: string;
  loan_response_time: string;
  write_off_status: string;
  write_off_time: string;
}

interface RepayPlanCsvItem {
  name?: string;
  loan_id: string;
  cur_date: string;
  term_no: string;
  start_date: string;
  end_date: string;
  clear_date: string;
  term_status: string;
  prin_total: string;
  prin_repay: string;
  int_plan: string;
  int_total: string;
  int_repay: string;
  int_discount: string;
  int_bal: string;
  pnlt_int_total: string;
  pnlt_int_repay: string;
  pnlt_int_discount: string;
  pnlt_int_bal: string;
  pre_pmt_fee_repay: string;
  product_no: string;
  out_loan_channel_no: string;
  days_ovd: string;
}

interface RepayItemCsvItem {
  name?: string;
  loan_id: string;
  cur_date: string;
  tran_time: string;
  seq_no: string;
  term_no: string;
  event: string;
  total_amt: string;
  income_amt: string;
  prin_amt: string;
  int_amt: string;
  pnlt_int_amt: string;
  pre_pmt_fee_repay: string;
  product_no: string;
  out_loan_channel_no: string;
}

interface RepayCsvItem {
  name?: string;
  loan_id: string;
  cur_date: string;
  tran_time: string;
  seq_no: string;
  total_amt: string;
  income_amt: string;
  prin_amt: string;
  int_amt: string;
  pnlt_int_amt: string;
  pre_pmt_fee_repay: string;
  product_no: string;
  out_loan_channel_no: string;
  interest_transfer_status: string;
  repay_account_type: string;
  repay_account_name: string;
  repay_account_no: string;
}

interface OpenCsvItem {
  name?: string;
  cur_date: string;
  loan_id: string;
  leader: string;
  partner: string;
  cust_name: string;
  cert_type: string;
  cert_no: string;
  apply_date: string;
  start_date: string;
  end_date: string;
  encash_amt: string;
  currency: string;
  repay_mode: string;
  repay_cycle: string;
  total_terms: string;
  grace_day: string;
  fund_status: string;
  product_no: string;
  out_loan_channel_no: string;
  usage: string;
  loan_response_time: string;
}

// loan.csv字段
const loanFieldProps = [
  { prop: "cur_date", label: "cur_date(账务日期)", width: "180" },
  { prop: "loan_id", label: "loan_id(借据号)", width: "170" },
  { prop: "apply_date", label: "apply_date(申请日期)", width: "170" },
  { prop: "start_date", label: "start_date(开始日期)", width: "170" },
  { prop: "end_date", label: "end_date(到期日期)", width: "160" },
  { prop: "clear_date", label: "clear_date(结清日期)", width: "180" },
  { prop: "encash_amt", label: "encash_amt(借据金额)", width: "180" },
  { prop: "currency", label: "currency(币种)", width: "130" },
  { prop: "repay_mode", label: "repay_mode(还款方式)", width: "180" },
  { prop: "repay_cycle", label: "repay_cycle(还款周期)", width: "180" },
  { prop: "total_terms", label: "total_terms(总期数)", width: "160" },
  { prop: "cur_term", label: "cur_term(当前期数)", width: "160" },
  { prop: "repay_day", label: "repay_day(还款日)", width: "160" },
  { prop: "grace_day", label: "grace_day(宽限期)", width: "160" },
  { prop: "loan_status", label: "loan_status(贷款状态)", width: "180" },
  { prop: "loan_form", label: "loan_form(贷款形态)", width: "170" },
  { prop: "prin_total", label: "prin_total(应还本金)", width: "170" },
  { prop: "prin_repay", label: "prin_repay(已还本金)", width: "170" },
  { prop: "prin_bal", label: "prin_bal(正常本金余额)", width: "180" },
  { prop: "ovd_prin_bal", label: "ovd_prin_bal(逾期本金余额)", width: "220" },
  { prop: "int_plan", label: "int_plan(计划利息)", width: "160" },
  { prop: "int_total", label: "int_total(应还利息)", width: "160" },
  { prop: "int_repay", label: "int_repay(已还利息)", width: "160" },
  { prop: "int_discount", label: "int_discount(减免利息)", width: "180" },
  { prop: "int_bal", label: "int_bal(利息余额)", width: "160" },
  { prop: "ovd_int_bal", label: "ovd_int_bal(逾期利息余额)", width: "220" },
  { prop: "pnlt_int_total", label: "pnlt_int_total(应收罚息)", width: "200" },
  { prop: "pnlt_int_repay", label: "pnlt_int_repay(已还罚息)", width: "200" },
  { prop: "pnlt_int_discount", label: "pnlt_int_discount(减免罚息)", width: "220" },
  { prop: "pnlt_int_bal", label: "pnlt_int_bal(罚息余额)", width: "180" },
  { prop: "pre_pmt_fee_repay", label: "pre_pmt_fee_repay(已还提前还款手续费)", width: "300" },
  { prop: "product_no", label: "product_no(产品编号)", width: "180" },
  { prop: "out_loan_channel_no", label: "out_loan_channel_no(平台订单号)", width: "260" },
  { prop: "days_ovd", label: "days_ovd(逾期天数)", width: "160" },
  { prop: "interest_transfer_status", label: "interest_transfer_status(非应计状态)", width: "280" },
  { prop: "loan_response_time", label: "loan_response_time(支付返回成功时间)", width: "300" },
  { prop: "write_off_status", label: "write_off_status(核销状态)", width: "210" },
  { prop: "write_off_time", label: "write_off_time(核销时间)", width: "200" }
];

// repay.csv字段
const repayFieldProps = [
  { prop: "cur_date", label: "cur_date(账务日期)", width: "160" },
  { prop: "loan_id", label: "loan_id(借据号)", width: "170" },
  { prop: "tran_time", label: "tran_time(交易时间)", width: "180" },
  { prop: "seq_no", label: "seq_no(交易序号)", width: "160" },
  { prop: "total_amt", label: "total_amt(总金额)", width: "160" },
  { prop: "income_amt", label: "income_amt(收入金额)", width: "180" },
  { prop: "prin_amt", label: "prin_amt(本金)", width: "160" },
  { prop: "int_amt", label: "int_amt(利息)", width: "160" },
  { prop: "pnlt_int_amt", label: "pnlt_int_amt(罚息)", width: "180" },
  { prop: "pre_pmt_fee_repay", label: "pre_pmt_fee_repay(提前还款手续费)", width: "280" },
  { prop: "product_no", label: "product_no(产品编号)", width: "180" },
  { prop: "out_loan_channel_no", label: "out_loan_channel_no(平台订单号)", width: "260" },
  { prop: "interest_transfer_status", label: "interest_transfer_status(非应计状态)", width: "280" },
  { prop: "repay_account_type", label: "repay_account_type(还款账户类型)", width: "270" },
  { prop: "repay_account_name", label: "repay_account_name(还款账户名)", width: "260" },
  { prop: "repay_account_no", label: "repay_account_no(还款账号)", width: "240" }
];

// repay_item.csv字段
const repayItemFieldProps = [
  { prop: "cur_date", label: "cur_date(账务日期)", width: "160" },
  { prop: "loan_id", label: "loan_id(借据号)", width: "170" },
  { prop: "tran_time", label: "tran_time(交易时间)", width: "160" },
  { prop: "seq_no", label: "seq_no(交易序号)", width: "270" },
  { prop: "term_no", label: "term_no(期序)", width: "130" },
  { prop: "event", label: "event(事件)", width: "160" },
  { prop: "total_amt", label: "total_amt(总金额)", width: "160" },
  { prop: "income_amt", label: "income_amt(收入金额)", width: "180" },
  { prop: "prin_amt", label: "prin_amt(本金)", width: "160" },
  { prop: "int_amt", label: "int_amt(利息)", width: "160" },
  { prop: "pnlt_int_amt", label: "pnlt_int_amt(罚息)", width: "180" },
  { prop: "pre_pmt_fee_repay", label: "pre_pmt_fee_repay(提前还款手续费)", width: "280" },
  { prop: "product_no", label: "product_no(产品编号)", width: "180" },
  { prop: "out_loan_channel_no", label: "out_loan_channel_no(平台订单号)", width: "260" }
];

// repay_plan.csv字段
const repayPlanFieldProps = [
  { prop: "cur_date", label: "cur_date(账务日期)", width: "160" },
  { prop: "loan_id", label: "loan_id(借据号)", width: "170" },
  { prop: "term_no", label: "term_no(期序)", width: "130" },
  { prop: "start_date", label: "start_date(开始日期)", width: "170" },
  { prop: "end_date", label: "end_date(到期日期)", width: "160" },
  { prop: "clear_date", label: "clear_date(结清日期)", width: "170" },
  { prop: "term_status", label: "term_status(本期状态)", width: "180" },
  { prop: "prin_total", label: "prin_total(应还本金)", width: "160" },
  { prop: "prin_repay", label: "prin_repay(已还本金)", width: "170" },
  { prop: "int_plan", label: "int_plan(计划利息)", width: "160" },
  { prop: "int_total", label: "int_total(应还利息)", width: "160" },
  { prop: "int_repay", label: "int_repay(已还利息)", width: "160" },
  { prop: "int_discount", label: "int_discount(减免利息)", width: "180" },
  { prop: "int_bal", label: "int_bal(利息余额)", width: "160" },
  { prop: "pnlt_int_total", label: "pnlt_int_total(应还罚息)", width: "200" },
  { prop: "pnlt_int_repay", label: "pnlt_int_repay(已还罚息)", width: "200" },
  { prop: "pnlt_int_discount", label: "pnlt_int_discount(减免罚息)", width: "220" },
  { prop: "pnlt_int_bal", label: "pnlt_int_bal(罚息余额)", width: "180" },
  { prop: "pre_pmt_fee_repay", label: "pre_pmt_fee_repay(已还提前还款手续费)", width: "300" },
  { prop: "product_no", label: "product_no(产品编号)", width: "180" },
  { prop: "out_loan_channel_no", label: "out_loan_channel_no(平台订单号)", width: "260" },
  { prop: "days_ovd", label: "days_ovd(逾期天数)", width: "160" }
];

const openFieldProps = [
  { prop: "cur_date", label: "cur_date(账务日期)", width: "160" },
  { prop: "loan_id", label: "loan_id(借据号)", width: "200" },
  { prop: "leader", label: "leader(牵头方)", width: "130" },
  { prop: "partner", label: "partner(合作方)", width: "140" },
  { prop: "cust_name", label: "cust_name(客户姓名)", width: "180" },
  { prop: "cert_type", label: "cert_type(证件类型)", width: "170" },
  { prop: "cert_no", label: "cert_no(证件号)", width: "150" },
  { prop: "apply_date", label: "apply_date(申请日期)", width: "170" },
  { prop: "start_date", label: "start_date(确认日期)", width: "170" },
  { prop: "end_date", label: "end_date(到期日期)", width: "170" },
  { prop: "encash_amt", label: "encash_amt(借据金额)", width: "180" },
  { prop: "currency", label: "currency(币种)", width: "130" },
  { prop: "repay_mode", label: "repay_mode(还款方式)", width: "180" },
  { prop: "repay_cycle", label: "repay_cycle(还款周期)", width: "180" },
  { prop: "total_terms", label: "total_terms(总期数)", width: "160" },
  { prop: "grace_day", label: "grace_day(宽限期)", width: "150" },
  { prop: "fund_status", label: "fund_status(放款状态)", width: "180" },
  { prop: "product_no", label: "product_no(产品编号)", width: "180" },
  { prop: "out_loan_channel_no", label: "out_loan_channel_no(平台订单号)", width: "260" },
  { prop: "usage", label: "usage(借款用途)", width: "140" },
  { prop: "loan_response_time", label: "loan_response_time(支付返回成功时间)", width: "300" }
];

const templateDir = "byte-dance";
const destFolderName = ref();
const props = defineProps<{
  selectedDbConnectionInfoId: string | undefined;
}>();

const createLoanCsvItem = function (): LoanCsvItem {
  return {
    name: "loan",
    disabled: false,
    scenario: "",
    cur_date: "",
    loan_id: "",
    apply_date: "",
    start_date: "",
    end_date: "",
    clear_date: "",
    encash_amt: "",
    currency: "",
    repay_mode: "",
    repay_cycle: "",
    total_terms: "",
    cur_term: "",
    repay_day: "",
    grace_day: "",
    loan_status: "",
    loan_form: "",
    prin_total: "",
    prin_repay: "",
    prin_bal: "",
    ovd_prin_bal: "",
    int_plan: "",
    int_total: "",
    int_repay: "",
    int_discount: "",
    int_bal: "",
    ovd_int_bal: "",
    pnlt_int_total: "",
    pnlt_int_repay: "",
    pnlt_int_discount: "",
    pnlt_int_bal: "",
    pre_pmt_fee_repay: "",
    product_no: "",
    out_loan_channel_no: "",
    days_ovd: "",
    interest_transfer_status: "",
    loan_response_time: "",
    write_off_status: "",
    write_off_time: ""
  };
};

// 添加创建空的RepayItemCsvItem项函数
const createRepayItemCsvItem = function (): RepayItemCsvItem {
  return {
    name: "repayItem",
    cur_date: "",
    loan_id: "",
    tran_time: "",
    seq_no: "",
    term_no: "",
    event: "",
    total_amt: "",
    income_amt: "",
    prin_amt: "",
    int_amt: "",
    pnlt_int_amt: "",
    pre_pmt_fee_repay: "0",
    product_no: "Z028801",
    out_loan_channel_no: "PF202302280004788557"
  };
};

const createRepayPlanCsvItem = function (): RepayPlanCsvItem {
  return {
    name: "repayPlan",
    loan_id: "",
    cur_date: "",
    term_no: "",
    start_date: "",
    end_date: "",
    clear_date: "",
    term_status: "",
    prin_total: "",
    prin_repay: "",
    int_plan: "",
    int_total: "",
    int_repay: "",
    int_discount: "",
    int_bal: "",
    pnlt_int_total: "",
    pnlt_int_repay: "",
    pnlt_int_discount: "",
    pnlt_int_bal: "",
    pre_pmt_fee_repay: "",
    product_no: "",
    out_loan_channel_no: "",
    days_ovd: ""
  };
};

const createRepayCsvItem = function (): RepayCsvItem {
  return {
    name: "repay",
    loan_id: "",
    cur_date: "",
    tran_time: "",
    seq_no: "",
    total_amt: "",
    income_amt: "",
    prin_amt: "",
    int_amt: "",
    pnlt_int_amt: "",
    pre_pmt_fee_repay: "",
    product_no: "",
    out_loan_channel_no: "",
    interest_transfer_status: "",
    repay_account_type: "",
    repay_account_name: "",
    repay_account_no: ""
  };
};

const createOpenCsvItem = function (): OpenCsvItem {
  return {
    name: "open",
    cur_date: "",
    loan_id: "",
    leader: "",
    partner: "",
    cust_name: "",
    cert_type: "",
    cert_no: "",
    apply_date: "",
    start_date: "",
    end_date: "",
    encash_amt: "",
    currency: "",
    repay_mode: "",
    repay_cycle: "",
    total_terms: "",
    grace_day: "",
    fund_status: "",
    product_no: "",
    out_loan_channel_no: "",
    usage: "",
    loan_response_time: ""
  };
};

const loanCsvDataList = ref<LoanCsvItem[]>([createLoanCsvItem()]);
const repayCsvDataList = ref<RepayCsvItem[]>([]);
const repayItemCsvDataList = ref<RepayItemCsvItem[]>([]);
const repayPlanCsvDataList = ref<RepayPlanCsvItem[]>([]);
const openCsvDataList = ref<OpenCsvItem[]>([]);

// 添加借据项
const addLoanItem = () => {
  let loanCsvItem = createLoanCsvItem();
  if (loanCsvDataList.value.length !== 0) {
    loanCsvItem.cur_date = loanCsvDataList.value[0].cur_date;
  }
  loanCsvDataList.value.push(loanCsvItem);
};

// 删除loan.csv项
const removeByLoanId = (row: LoanCsvItem) => {
  if (!row.loan_id) {
    loanCsvDataList.value = loanCsvDataList.value.filter(item => item !== row);
    return;
  }
  // 从loanCsvData中删除所有匹配的项
  loanCsvDataList.value = loanCsvDataList.value.filter(item => item.loan_id !== row.loan_id);

  // 从其他表格中删除相关数据
  repayCsvDataList.value = repayCsvDataList.value.filter(item => item.loan_id !== row.loan_id);
  repayItemCsvDataList.value = repayItemCsvDataList.value.filter(item => item.loan_id !== row.loan_id);
  repayPlanCsvDataList.value = repayPlanCsvDataList.value.filter(item => item.loan_id !== row.loan_id);
  openCsvDataList.value = openCsvDataList.value.filter(item => item.loan_id !== row.loan_id);
};

// 当loan_id变化时，获取数据并更新其他表格数据
const fetchBusinessApply = async (row: LoanCsvItem) => {
  // 如果loan_id为空，则不进行任何操作
  if (!row.loan_id) {
    return;
  }

  // 检查是否存在与当前行相同loan_id的其他记录
  const existingLoan = loanCsvDataList.value.find(item => item.loan_id === row.loan_id && item !== row);
  // 如果存在重复的loan_id，提示警告并清空当前输入的loan_id
  if (existingLoan) {
    ElMessage.warning(`loan_id: 【${row.loan_id}】已存在，请勿重复添加`);
    row.loan_id = "";
    return;
  }

  // 如果未选择场景，提示用户选择场景
  if (!row.scenario) {
    ElMessage.warning("请选择场景");
    return;
  }

  // 如果未选择数据库连接信息，提示用户选择数据库
  if (!props.selectedDbConnectionInfoId) {
    ElMessage.warning("请选择数据库");
    return;
  }

  row.disabled = true;

  const outLoanChannelNo = `PF${getCurrentDateYYYYMMDD()}${Math.random().toString().substring(2, 12)}`;

  // 调用API获取贷款CSV数据，并根据响应结果更新行数据
  await api_getBusinessApply(row.loan_id, props.selectedDbConnectionInfoId!)
    .then(async res => {
      if (res.respCode === 2000) {
        let businessApply = res.respData;

        if (loanCsvDataList.value.length > 1 && loanCsvDataList.value[0].cur_date) {
          if (row.cur_date !== loanCsvDataList.value[0].cur_date) {
            ElMessage.warning(`loan_id：【${row.loan_id}】日期与其它记录不一致`);
            row.loan_id = "";
            row.disabled = false;
            return;
          }
        }

        // 设置loan.csv当前行的值
        row.apply_date = businessApply.occurdate;
        row.start_date = businessApply.occurdate;
        row.end_date = businessApply.maturitydate;
        row.encash_amt = (parseInt(businessApply.businesssum) * 100).toString();
        row.currency = businessApply.businesscurrency === "CNY" ? "156" : businessApply.businesscurrency;
        row.repay_mode = "02";
        row.repay_cycle = "M";
        row.total_terms = businessApply.businesstermmonth;
        row.cur_term = calculateCurrentTerm(
          row.start_date,
          row.end_date,
          row.cur_date!,
          parseInt(row.total_terms, 10)
        ).toString();
        row.repay_day = calculateRepayDay(businessApply.occurdate);
        row.grace_day = "3";
        row.prin_total = row.encash_amt;
        row.int_plan = "1200";
        row.int_discount = "0";
        row.int_bal = "0";
        row.ovd_int_bal = "0";
        row.pnlt_int_total = "0";
        row.pnlt_int_repay = "0";
        row.pnlt_int_discount = "0";
        row.pnlt_int_bal = "0";
        row.pre_pmt_fee_repay = "0";
        row.product_no = "Z029972";
        row.out_loan_channel_no = outLoanChannelNo;
        row.days_ovd = "0";
        row.interest_transfer_status = "1";
        row.loan_response_time = `${row.cur_date}${getCurrentDateTime().substring(8)}`;
        row.write_off_status = "";
        row.write_off_time = "";

        applyScenario(row.scenario!, row);

        // 创建repay_plan.csv记录
        let lastEndDate = businessApply.occurdate;
        for (let termNo = 1; termNo <= Number(businessApply.businesstermmonth); termNo++) {
          let currEndDate = calculateNextRepayDay(lastEndDate);

          // 创建repay_plan.csv记录
          const repayPlanCsvItem = createRepayPlanCsvItem();
          repayPlanCsvItem.name = "repayPlan";
          repayPlanCsvItem.cur_date = row.cur_date;
          repayPlanCsvItem.loan_id = row.loan_id;
          repayPlanCsvItem.term_no = termNo.toString();
          repayPlanCsvItem.start_date = lastEndDate;
          repayPlanCsvItem.end_date = currEndDate;
          repayPlanCsvItem.clear_date = row.clear_date;
          repayPlanCsvItem.term_status = "05";
          repayPlanCsvItem.prin_total = divideNumber(row.encash_amt, businessApply.businesstermmonth)[termNo - 1];
          repayPlanCsvItem.prin_repay = row.encash_amt;
          repayPlanCsvItem.int_plan = divideNumber(row.int_plan, businessApply.businesstermmonth)[termNo - 1];
          repayPlanCsvItem.int_total = divideNumber(row.int_total, businessApply.businesstermmonth)[termNo - 1];
          repayPlanCsvItem.int_repay = divideNumber(row.int_total, businessApply.businesstermmonth)[termNo - 1];
          repayPlanCsvItem.int_discount = "0";
          repayPlanCsvItem.int_bal = "0";
          repayPlanCsvItem.pnlt_int_total = "0";
          repayPlanCsvItem.pnlt_int_repay = "0";
          repayPlanCsvItem.pnlt_int_discount = "0";
          repayPlanCsvItem.pnlt_int_bal = "0";
          repayPlanCsvItem.pre_pmt_fee_repay = "0";
          repayPlanCsvItem.product_no = "Z028801";
          repayPlanCsvItem.out_loan_channel_no = outLoanChannelNo;
          repayPlanCsvItem.days_ovd = "0";

          applyScenario(row.scenario!, repayPlanCsvItem);
          repayPlanCsvDataList.value.push(repayPlanCsvItem);
          lastEndDate = currEndDate;
        }

        const seqNo = `RP${row.cur_date}${getCurrentDateTime().substring(8)}${Math.random().toString().slice(2, 12)}`;

        // 放款场景
        if (row.scenario === scenarioOptions[0].value) {
          let certid = "";
          await api_getCustomerInfo(businessApply.customerid, props.selectedDbConnectionInfoId!).then(res => {
            if (res.respCode === 2000) {
              certid = res.respData.certid;
            } else {
              ElMessage.error(res.respMsg);
            }
          });

          // 创建open.csv记录
          const openCsvItem = createOpenCsvItem();
          openCsvItem.cur_date = row.cur_date;
          openCsvItem.loan_id = businessApply.thirdapplyno;
          openCsvItem.leader = "BYTEDANCE";
          openCsvItem.partner = "pinganyinhang1";
          openCsvItem.cust_name = businessApply.customername;
          openCsvItem.cert_type = "01";
          openCsvItem.cert_no = certid;
          openCsvItem.apply_date = row.cur_date;
          openCsvItem.start_date = row.cur_date;
          openCsvItem.end_date = businessApply.maturitydate;
          openCsvItem.encash_amt = row.encash_amt;
          openCsvItem.currency = businessApply.businesscurrency === "CNY" ? "156" : businessApply.businesscurrency;
          openCsvItem.repay_mode = "02";
          openCsvItem.repay_cycle = "M";
          openCsvItem.total_terms = businessApply.businesstermmonth;
          openCsvItem.grace_day = "3";
          openCsvItem.fund_status = "02";
          openCsvItem.product_no = "Z029972";
          openCsvItem.out_loan_channel_no = outLoanChannelNo;
          openCsvItem.usage = "1";
          openCsvItem.loan_response_time = getCurrentDateTime();

          applyScenario(row.scenario!, openCsvItem);
          openCsvDataList.value.push(openCsvItem);
        }
        // 还款场景
        else {
          // 创建repay_item.csv记录
          const repayItemCsvItem = createRepayItemCsvItem();
          repayItemCsvItem.name = "repayItem";
          repayItemCsvItem.cur_date = row.cur_date;
          repayItemCsvItem.loan_id = businessApply.thirdapplyno;
          repayItemCsvItem.tran_time = `${row.cur_date}${getCurrentDateTime().substring(8)}`;
          repayItemCsvItem.seq_no = seqNo;
          repayItemCsvItem.term_no = "";
          repayItemCsvItem.event = "";
          repayItemCsvItem.total_amt = "";
          repayItemCsvItem.income_amt = "";
          repayItemCsvItem.prin_amt = "";
          repayItemCsvItem.int_amt = "";
          repayItemCsvItem.pnlt_int_amt = "";
          repayItemCsvItem.pre_pmt_fee_repay = "0";
          repayItemCsvItem.product_no = "Z028801";
          repayItemCsvItem.out_loan_channel_no = outLoanChannelNo;

          applyScenario(row.scenario!, repayItemCsvItem);
          repayItemCsvDataList.value.push(repayItemCsvItem);

          // 创建repay.csv记录
          const repayCsvItem = createRepayCsvItem();
          repayCsvItem.name = "repay";
          repayCsvItem.cur_date = row.cur_date;
          repayCsvItem.loan_id = businessApply.thirdapplyno;
          repayCsvItem.tran_time = `${row.cur_date}${getCurrentDateTime().substring(8)}`;
          repayCsvItem.seq_no = seqNo;
          repayCsvItem.total_amt = "";
          repayCsvItem.income_amt = "";
          repayCsvItem.prin_amt = "";
          repayCsvItem.int_amt = "";
          repayCsvItem.pnlt_int_amt = "";
          repayCsvItem.pre_pmt_fee_repay = "0";
          repayCsvItem.product_no = "Z028801";
          repayCsvItem.out_loan_channel_no = outLoanChannelNo;
          repayCsvItem.interest_transfer_status = "1";
          repayCsvItem.repay_account_type = "1";
          repayCsvItem.repay_account_name = "6231700180005861107";
          repayCsvItem.repay_account_no = "****************";

          applyScenario(row.scenario!, repayCsvItem);
          repayCsvDataList.value.push(repayCsvItem);
        }

        ElMessage.success(`loan_id: 【${row.loan_id}】数据加载成功`);
      } else {
        // 如果API调用失败，显示错误消息，并解除行的禁用状态
        ElMessage.error(res.respMsg);
        row.disabled = false;
      }
    })
    .catch(() => {
      row.disabled = false;
    });
};

// 根据场景选择不同的处理函数
const applyScenario = function (scenario: string, row: any) {
  switch (scenario) {
    // 场景-放款
    case scenarioOptions[0].value:
      onLoan(row);
      break;
    // 场景-结清
    case scenarioOptions[1].value:
      onSettleRepay(row);
      break;
    // 场景-部分还款
    case scenarioOptions[2].value:
      onPartialRepay(row);
      break;
    // 场景-账单还款
    case scenarioOptions[3].value:
      onBillRepay(row);
      break;
    // 场景-逾期还款
    case scenarioOptions[4].value:
      onOverdueRepay(row);
      break;
    default:
  }
};

// 场景-放款
const onLoan = function (row: any) {
  // loan.csv
  if (row.name === "loan") {
    row.clear_date = "";
    row.loan_status = "02";
    row.loan_form = "1";
    row.prin_repay = "0";
    row.prin_bal = row.prin_total;
    row.ovd_prin_bal = "0";
    row.int_plan = "300";
    row.int_total = "300";
    row.int_repay = "0";
    row.int_discount = "0";
  }

  // repayPlan.csv
  if (row.name === "repayPlan") {
    row.term_status = "01";
  }
};

// 场景-结清
const onSettleRepay = function (row: any) {
  // loan.csv
  if (row.name === "loan") {
    row.clear_date = row.cur_date;
    row.loan_status = "06";
    row.loan_form = "1";
    row.prin_repay = row.prin_total;
    row.prin_bal = "0";
    row.ovd_prin_bal = "0";
    row.days_ovd = "0";
    return;
  }

  // repayPlan.csv
  if (row.name === "repayPlan") {
    row.clear_date = row.cur_date;
    row.term_status = "05";
    row.prin_repay = row.prin_total;
    return;
  }

  // repayItem.csv
  if (row.name === "repayItem") {
    row.event = "14";
  }
};

// 场景-部分还款
const onPartialRepay = function (row: any) {
  // loan.csv
  if (row.name === "loan") {
    row.apply_date = row.cur_date;
    row.start_date = row.cur_date;
    row.clear_date = "";
    row.loan_status = "05";
    row.loan_form = "1";
    row.prin_repay = "";
    row.ovd_prin_bal = "0";
  }

  // repayPlan.csv
  if (row.name === "repayPlan") {
    row.clear_date = "";
    row.term_status = "01";
    row.prin_repay = "";
    row.pnlt_int_total = "0";
    row.pnlt_int_repay = "0";
  }

  // repayItem.csv
  if (row.name === "repayItem") {
    row.event = "13";
  }
};

// 场景-账单还款
const onBillRepay = function (row: any) {
  // loan.csv
  if (row.name === "loan") {
    row.clear_date = "";
    row.loan_status = "05";
    row.loan_form = "1";
  }

  // repayPlan.csv
  if (row.name === "repayPlan") {
    row.clear_date = "";
    row.term_status = "01";
    row.prin_repay = "0";
    if (row.term_no === "1") {
      row.clear_date = row.cur_date;
      row.term_status = "05";
      row.prin_repay = row.prin_total;
    }
  }

  // repayItem.csv
  if (row.name === "repayItem") {
    row.event = "12";
  }
};

// 场景-逾期还款
const onOverdueRepay = function (row: any) {
  // loan.csv
  if (row.name === "loan") {
    row.clear_date = "";
    row.loan_status = "05";
    row.loan_form = "2";
    row.prin_repay = "";
    row.ovd_prin_bal = "0";
  }

  // repayPlan.csv
  if (row.name === "repayPlan") {
    row.clear_date = "";
    row.term_status = "01";
    row.prin_repay = "";
    row.pnlt_int_total = "0";
    if (row.term_no === "1") {
      row.clear_date = row.cur_date;
    }
  }
};

const writeCsv = async function () {
  if (loanCsvDataList.value.length === 0) {
    ElMessage.warning("请先添加数据");
    return;
  }
  const randomNo = Math.random().toString(36).substring(2, 9);
  destFolderName.value = `${loanCsvDataList.value[0].cur_date}_${randomNo}`;
  await api_createDestFolder(templateDir, destFolderName.value).then(res => {
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
  const submitLoanCsvData: LoanCsvItem[] = JSON.parse(JSON.stringify(loanCsvDataList.value)).map(item => {
    delete item.scenario;
    delete item.disabled;
    delete item.name;
    return item;
  });
  const submitRepayPlanCsvData: RepayPlanCsvItem[] = JSON.parse(JSON.stringify(repayPlanCsvDataList.value)).map(item => {
    delete item.name;
    return item;
  });
  const submitRepayItemCsvData: RepayItemCsvItem[] = JSON.parse(JSON.stringify(repayItemCsvDataList.value)).map(item => {
    delete item.name;
    return item;
  });
  const submitRepayCsvData: RepayCsvItem[] = JSON.parse(JSON.stringify(repayCsvDataList.value)).map(item => {
    delete item.name;
    return item;
  });
  const submitOpenCsvData: OpenCsvItem[] = JSON.parse(JSON.stringify(openCsvDataList.value)).map(item => {
    delete item.name;
    return item;
  });
  Promise.all([
    submitLoanCsvData.length > 0
      ? api_writeCsv<LoanCsvItem>({
          param1: destFolderName.value,
          param2: "loan.csv",
          listParam1: submitLoanCsvData
        }).then(res => {
          if (res.respCode === 5000) {
            ElMessage.error(res.respMsg);
          }
        })
      : Promise.resolve(undefined),
    submitRepayPlanCsvData.length > 0
      ? api_writeCsv<RepayPlanCsvItem>({
          param1: destFolderName.value,
          param2: "repay_plan.csv",
          listParam1: submitRepayPlanCsvData
        }).then(res => {
          if (res.respCode === 5000) {
            ElMessage.error(res.respMsg);
          }
        })
      : Promise.resolve(undefined),
    submitRepayItemCsvData.length > 0
      ? api_writeCsv<RepayItemCsvItem>({
          param1: destFolderName.value,
          param2: "repay_item.csv",
          listParam1: submitRepayItemCsvData
        }).then(res => {
          if (res.respCode === 5000) {
            ElMessage.error(res.respMsg);
          }
        })
      : Promise.resolve(undefined),
    submitRepayCsvData.length > 0
      ? api_writeCsv<RepayCsvItem>({
          param1: destFolderName.value,
          param2: "repay.csv",
          listParam1: submitRepayCsvData
        }).then(res => {
          if (res.respCode === 5000) {
            ElMessage.error(res.respMsg);
          }
        })
      : Promise.resolve(undefined),
    submitOpenCsvData.length > 0
      ? api_writeCsv<OpenCsvItem>({
          param1: destFolderName.value,
          param2: "open.csv",
          listParam1: submitOpenCsvData
        }).then(res => {
          if (res.respCode === 5000) {
            ElMessage.error(res.respMsg);
          }
        })
      : Promise.resolve(undefined)
  ]).then(() => {
    ElMessage.success("文件生成成功");
  });
};

const download = function () {
  if (!destFolderName.value) {
    ElMessage.warning("无文件下载，请先生成文件");
    return;
  }
  api_download(destFolderName.value);
};

// 设置loan.csv的prin_repay
const setLoanPrinRepay = function (row: RepayPlanCsvItem) {
  const loan = loanCsvDataList.value.find(item => item.loan_id === row.loan_id && item.cur_term === row.term_no);
  if (loan && loan.scenario === scenarioOptions[2].value) {
    loan.prin_repay = row.prin_repay;
  }
};

// 计算repay_plan.csv的prin_total  应还本金=借据金额/期数
const calculateRepayPlanPrinTotal = function (row: LoanCsvItem) {
  let repayPlanList = repayPlanCsvDataList.value.filter(item => item.loan_id === row.loan_id);
  repayPlanList.forEach(item => {
    item.prin_total = divideNumber(row.encash_amt, repayPlanList.length)[parseInt(item.term_no) - 1];
  });
};

// 计算repay.csv的prin_bal  正常本金余额=应还-已还
const calculatePrinBal = function (row: LoanCsvItem) {
  row.prin_bal = (Number(row.prin_total) - Number(row.prin_repay)).toString();
};

// 计算repay_plan.csv的int_plan  本期应还利息=应还利息/期数
const calculateRepayPlanIntPlan = function (row: LoanCsvItem) {
  let repayPlanList = repayPlanCsvDataList.value.filter(item => item.loan_id === row.loan_id);
  repayPlanList.forEach(item => {
    item.int_plan = divideNumber(row.int_plan, repayPlanList.length)[parseInt(item.term_no) - 1];
  });
};

// 计算repay_plan.csv的int_bal  本期利息余额=本期应还利息-本期已还
const calculateRepayPlanIntBal = function (row: RepayPlanCsvItem) {
  row.int_bal = (Number(row.int_total) - Number(row.int_repay)).toString();
};

// 计算repay_plan.csv的pnlt_int_bal  本期罚息余额=本期应还罚息-本期已还罚息
const calculateRepayPlanPnltIntBal = function (row: RepayPlanCsvItem) {
  row.pnlt_int_bal = (Number(row.pnlt_int_total) - Number(row.pnlt_int_repay)).toString();
};

// 计算repay_item.csv的total_amt和income_amt  总还款金额=本金+利息+罚息
const calculateRepayItemAmt = function (row: RepayItemCsvItem) {
  row.total_amt = (Number(row.prin_amt) + Number(row.int_amt) + Number(row.pnlt_int_amt)).toString();
  row.income_amt = (Number(row.prin_amt) + Number(row.int_amt) + Number(row.pnlt_int_amt)).toString();
};

// 计算repay.csv的total_amt、income_amt、prin_amt、int_amt
const calculateRepayAmt = function (row: RepayItemCsvItem) {
  // 查找对应的repay记录
  const repay = repayCsvDataList.value.find(item => item.loan_id === row.loan_id);
  if (!repay) return;

  repay.total_amt = "0";
  repay.income_amt = "0";
  repay.prin_amt = "0";
  repay.int_amt = "0";
  repay.pnlt_int_amt = "0";

  // 查找所有repayItemCsvItem数据
  repayItemCsvDataList.value
    .filter(item => item.loan_id === row.loan_id)
    .forEach(item => {
      // 确保total_amt有值且是有效数字
      const itemTotal = Number(item.total_amt || 0);
      const itemPrin = Number(item.prin_amt || 0);
      const itemInt = Number(item.int_amt || 0);
      const itemPnltInt = Number(item.pnlt_int_amt || 0);
      if (!isNaN(itemTotal)) {
        repay.total_amt = (Number(repay.total_amt) + itemTotal).toString();
        // 确保income_amt与total_amt相同
        repay.income_amt = repay.total_amt;
        repay.prin_amt = (Number(repay.prin_amt) + itemPrin).toString();
        repay.int_amt = (Number(repay.int_amt) + itemInt).toString();
        repay.pnlt_int_amt = (Number(repay.pnlt_int_amt) + itemPnltInt).toString();
      }
    });
};

// 添加repay_item.csv数据项
const addRepayItemData = function () {
  // 检查是否已有贷款数据
  if (loanCsvDataList.value.length === 0 || !loanCsvDataList.value.some(item => item.loan_id)) {
    ElMessage.warning("请先添加loan.csv数据");
    return;
  }

  repayItemCsvDataList.value.push(createRepayItemCsvItem());
};

const onRepayItemLoanIdChange = function (row: RepayItemCsvItem) {
  const loan = loanCsvDataList.value.find(item => item.loan_id === row.loan_id)!;
  const repay = repayCsvDataList.value.find(item => item.loan_id === row.loan_id)!;
  row.cur_date = loan.cur_date;
  row.tran_time = `${loan.cur_date}${getCurrentDateTime().substring(8)}`;
  row.seq_no = repay.seq_no;
  applyScenario(loan.scenario!, row);
};

// 添加删除repay_item.csv项的函数
const removeRepayItem = function (row: RepayItemCsvItem) {
  repayItemCsvDataList.value = repayItemCsvDataList.value.filter(item => item !== row);

  // 更新相关的repay数据
  const affectedLoans = new Set(repayItemCsvDataList.value.map(item => item.loan_id));
  repayCsvDataList.value.forEach(repay => {
    if (affectedLoans.has(repay.loan_id)) {
      calculateRepayAmt(repayItemCsvDataList.value.find(item => item.loan_id === repay.loan_id)!);
    }
  });
};

/**
 * 计算还款日
 * @param dateStr 日期字符串，格式为YYYYMMDD
 * @returns 还款日，如果是1-20日为次月对日，21-31日为下下月1-11日
 */
const calculateRepayDay = function (dateStr: string): string {
  if (!dateStr || dateStr.length !== 8) return "";

  const day = parseInt(dateStr.substring(6, 8), 10);

  if (day >= 1 && day <= 20) {
    // 1-20日：次月对日
    return day.toString();
  } else if (day >= 21 && day <= 31) {
    // 21-31日：下下月1-11日，对应关系为21日->1日，22日->2日...
    return (day - 20).toString();
  }

  return "";
};

/**
 * 计算当前期数
 * @param startDate 开始日期，格式为YYYYMMDD
 * @param endDate 结束日期，格式为YYYYMMDD
 * @param curDate 当前日期，格式为YYYYMMDD
 * @param totalTerms 总期数
 * @returns 当前期数，从1开始
 */
const calculateCurrentTerm = function (startDate: string, endDate: string, curDate: string, totalTerms: number): number {
  if (!startDate || !curDate || totalTerms <= 0) return 1;

  // 如果当前日期早于或等于开始日期，返回第1期
  if (curDate <= startDate) return 1;

  // 如果当前日期晚于或等于结束日期，返回最后一期
  if (endDate && curDate >= endDate) return totalTerms;

  // 计算各期到期日
  const termDueDates: string[] = [];
  let currentDueDate = startDate;

  // 计算所有期数的还款日
  for (let i = 0; i < totalTerms; i++) {
    currentDueDate = calculateNextRepayDay(currentDueDate);
    termDueDates.push(currentDueDate);
  }

  // 确定当前日期属于哪一期
  for (let i = 0; i < termDueDates.length; i++) {
    if (curDate < termDueDates[i]) {
      return i + 1; // 当前日期小于第i+1期到期日，说明在第i+1期内
    }
  }

  // 如果当前日期大于所有期的到期日，返回最后一期
  return totalTerms;
};

const calculateNextRepayDay = function (dateStr: string): string {
  // 解析传入的日期字符串
  const year = parseInt(dateStr.substring(0, 4), 10);
  const month = parseInt(dateStr.substring(4, 6), 10) - 1; // 月份从0开始
  const day = parseInt(dateStr.substring(6, 8), 10);

  // 创建日期对象
  const date = new Date(year, month, day);

  if (day >= 1 && day <= 20) {
    // 1-20日：次月对日
    date.setMonth(date.getMonth() + 1);
  } else if (day >= 21 && day <= 31) {
    // 21-31日：下下月1-11日
    date.setMonth(date.getMonth() + 2);
    date.setDate(1 + (day - 21)); // 1-11日
  } else {
    throw new Error("Invalid date");
  }

  // 格式化输出为YYYYMMDD
  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, "0");
  const dd = String(date.getDate()).padStart(2, "0");

  return `${yyyy}${mm}${dd}`;
};

const divideNumber = function (a: any, b: any) {
  // 确保a和b都是数值类型
  const numA = Number(a);
  const numB = Number(b);

  // 检查是否能整除
  if (numA % numB === 0) {
    // 如果能整除，生成长度为numB且每个元素都是numA/numB的数组
    return Array(numB).fill(numA / numB);
  } else {
    // 如果不能整除，计算基础值和余数
    const baseValue = Math.floor(numA / numB);
    const remainder = numA % numB;
    // 创建数组，除了最后一个元素之外，所有元素都等于baseValue
    let result = Array(numB).fill(baseValue);
    // 将余数添加到最后一个元素上
    result[numB - 1] += remainder;
    return result;
  }
};

watch(
  loanCsvDataList,
  () => {
    loanCsvDataList.value.forEach(loanItem => {
      // 场景-放款
      if (loanItem.scenario === scenarioOptions[0].value) {
        loanItem.apply_date = loanItem.cur_date;
        loanItem.start_date = loanItem.cur_date;

        openCsvDataList.value
          .filter(openItem => openItem.loan_id === loanItem.loan_id)
          .forEach(openItem => {
            openItem.cur_date = loanItem.cur_date;
            openItem.apply_date = loanItem.cur_date;
            openItem.start_date = loanItem.cur_date;
          });

        repayPlanCsvDataList.value
          .filter(repayPlanItem => repayPlanItem.loan_id === loanItem.loan_id)
          .forEach(repayPlanItem => {
            repayPlanItem.cur_date = loanItem.cur_date;
            repayPlanItem.start_date = loanItem.cur_date;
          });
      }
      calculatePrinBal(loanItem);
      calculateRepayPlanPrinTotal(loanItem);
      calculateRepayPlanIntPlan(loanItem);
    });
  },
  { deep: true }
);

// watch(openCsvDataList, () => {
//   openCsvDataList.value.forEach(item => {
//   });
// });

watch(
  repayPlanCsvDataList,
  () => {
    repayPlanCsvDataList.value.forEach(item => {
      calculateRepayPlanIntBal(item);
      calculateRepayPlanPnltIntBal(item);
      setLoanPrinRepay(item);
    });
  },
  { deep: true }
);

// 添加对repayItemCsvData的监听
watch(
  repayItemCsvDataList,
  () => {
    // 当repayItemCsvData变化时，更新所有repayCsvData中的总金额
    repayItemCsvDataList.value.forEach(repayItem => {
      calculateRepayItemAmt(repayItem);
      calculateRepayAmt(repayItem);
    });
  },
  { deep: true }
);

defineExpose({ writeCsv, download });
</script>

<style scoped lang="scss">
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table-title {
    font-size: 15px;
    font-weight: 500;
  }
}

.loan-table {
  margin-top: 10px;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;

  .record-count {
    font-size: 14px;
    color: #909399;
  }
}
</style>
