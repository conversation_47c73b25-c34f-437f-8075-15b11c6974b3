import http from "@/api";
import {SERVICE_ENR} from "@/api/config/servicePort";
import {UpdateIgnoredFieldDto} from "@/api/modules/service-esb-data/service-model";
export const api_getIgnoredField = function (interfaceId: string, versionNumber: string) {
  return http.get<string[]>(SERVICE_ENR + "/deduplicate/getIgnoredField", {interfaceId, versionNumber});
}

export const api_updateIgnoredField = function (updateIgnoredFieldDto: UpdateIgnoredFieldDto) {
  return http.post(SERVICE_ENR + "/deduplicate/updateIgnoredField", updateIgnoredFieldDto);
}

export const api_getIgnoredFieldGlobal = () => {
  return http.get<string[]>(SERVICE_ENR + "/deduplicate/getIgnoredFieldGlobal");
}

export const api_addIgnoredFieldGlobal = function (ignoredField: string) {
  return http.get(SERVICE_ENR + "/deduplicate/addIgnoredFieldGlobal", {ignoredField});
}

export const api_deleteIgnoredFieldGlobal = (ignoredField: string) => {
  return http.get(SERVICE_ENR + "/deduplicate/deleteIgnoredFieldGlobal", {ignoredField});
}

export const api_deduplicate = (interfaceId: string, versionNumber: string) => {
  return http.get(SERVICE_ENR + "/deduplicate/deduplicate", {interfaceId, versionNumber});
}

export const api_internalDeduplicate = (interfaceId: string, versionNumber: string) => {
  return http.get(SERVICE_ENR + "/deduplicate/internalDeduplicate", {interfaceId, versionNumber});
}
