<template>
  <div class="main-box">
    <div class="tree-filter-div">
      <TreeFilter
        ref="configTree"
        class="tree-filter1-style"
        :title="'配置项'"
        :request-api="queryConfigList"
        @node-click="clickConfigId"
      />
      <TreeFilter
        ref="interfaceIdTree"
        class="tree-filter2-style"
        :title="'接口号'"
        :request-api="queryESBInterfaceList"
        @node-click="clickInterfaceId"
      />
    </div>
    <TreeFilter :title="'应用系统'" @node-change="changeServiceProvider" :data="serviceProviderList" />
    <Tabs
      ref="tabs"
      :graph-data="graphData"
      :table-data="tableData"
      :config-id="configId"
      :interface-id="interfaceId"
      :service-provider="serviceProvider"
    />
  </div>
</template>

<script setup lang="ts">
import TreeFilter from "@/components/TreeFilter/index.vue";
import Tabs from "@/views/esbSystemLink/components/tabs/index.vue";
import { ref } from "vue";
import {
  queryESBInterfaceList,
  queryESLTreeListByConfigId,
  queryESLTreeListByInterfaceId,
  queryConfigList,
  queryESLTreeListByServiceProvider
} from "@/api/modules/esbSystemLink";
import { TreeFilterNode } from "@/api/interface";

// 获取组件实例
const configTree = ref<InstanceType<typeof TreeFilter>>();
const interfaceIdTree = ref<InstanceType<typeof TreeFilter>>();
const tabs = ref<InstanceType<typeof Tabs>>();

const graphData = ref<TreeFilterNode>({ id: "", label: "", children: [] });
const tableData = ref<TreeFilterNode[]>([]);

const configId = ref<string>();
// 点击配置项的回调函数
function clickConfigId(obj: { id: string; label: string }) {
  tabs.value!.activeName = "first";
  configId.value = obj.id;
  // 接口id取消选择
  interfaceIdTree.value!.setCurrentKey(undefined);
  // tabs取消展示服务提供方的相关信息
  serviceProvider.value = "";
  // 根据配置项id获取服务提供方列表
  getServiceProviderListByConfigId(obj);
}

// 根据配置项id获取服务提供方列表
const serviceProviderList = ref();
function getServiceProviderListByConfigId(obj: { id: string; label: string }) {
  queryESLTreeListByConfigId(obj.id).then(res => {
    serviceProviderList.value = getFirstLevelNodesWithNullChildren(res.respData);
    graphData.value = {
      id: "id",
      label: obj.label,
      children: res.respData
    };
    tableData.value = res.respData;
  });
}

// 点击接口id的回调函数
const interfaceId = ref<string>();
function clickInterfaceId(obj: { id: string; label: string }) {
  tabs.value!.activeName = "first";
  // 配置项取消选择
  configTree.value!.setCurrentKey(undefined);
  // tabs取消展示服务提供方的相关信息
  serviceProvider.value = "";
  // 修改当前选中的接口号
  interfaceId.value = obj.id;
  // 根据选中的ESB接口ID获取服务提供方列表
  getServiceProviderListByInterfaceId(obj);
}

// 根据选中的ESB接口ID获取服务提供方列表
function getServiceProviderListByInterfaceId(obj: { id: string; label: string }) {
  queryESLTreeListByInterfaceId(obj.id).then(res => {
    serviceProviderList.value = getFirstLevelNodesWithNullChildren(res.respData.children!);
    // serviceProviderList.value = res.respData.children;
    graphData.value = {
      id: res.respData.id,
      label: res.respData.label,
      children: [
        {
          id: "id",
          label: obj.label,
          children: res.respData.children
        }
      ]
    };
    tableData.value = res.respData.children!;
  });
}

// 获取TreeFilterNode列表的所有第一层节点，并将子节点设置为null
function getFirstLevelNodesWithNullChildren(tree: TreeFilterNode[]): any {
  return tree.map(node => ({
    id: node.id,
    label: node.label,
    children: null
  }));
}

// 修改选择服务提供方的回调函数
const serviceProvider = ref();
function changeServiceProvider(obj: { id: string; label: string }) {
  tabs.value!.activeName = "first";
  queryESLTreeListByServiceProvider(obj.id).then(res => {
    graphData.value = {
      id: "id",
      label: obj.label,
      children: res.respData
    };
    tableData.value = res.respData;
  });
}
</script>

<style>
@import "./index.scss";
</style>
