import http from "@/api";
import {SERVICE_ENR} from "@/api/config/servicePort";

export interface EsbReplaceField {
  id?: string;
  interfaceId: string;
  tag: string;
  value: string;
}

export interface EsbReplaceVariable {
  id?: string;
  name: string;
  value: string;
}

export const api_getReplaceField = (interfaceId: string) => {
  return http.get<EsbReplaceField[]>(SERVICE_ENR + "/replace/getReplaceField", {interfaceId});
}

export const api_deleteReplaceField = (id: string) => {
  return http.get(SERVICE_ENR + "/replace/deleteReplaceField", {id});
}

export const api_addReplaceField = (replaceField: EsbReplaceField) => {
  return http.post(SERVICE_ENR + "/replace/addReplaceField", replaceField);
}

export const api_getReplaceVariable = () => {
  return http.get<EsbReplaceVariable[]>(SERVICE_ENR + "/replace/getReplaceVariable");
}

export const api_deleteReplaceVariable = (id: string) => {
  return http.get(SERVICE_ENR + "/replace/deleteReplaceVariable", {id});
}

export const api_addReplaceVariable = (replaceVariable: EsbReplaceVariable) => {
  return http.post(SERVICE_ENR + "/replace/addReplaceVariable", replaceVariable);
}
