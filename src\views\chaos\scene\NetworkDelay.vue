<template>
  <div class="network-delay-injection">
    <div v-loading="injectionLoading">
      <div class="card-header">
        <div class="title-area">
          <el-icon class="icon"><Connection /></el-icon>
          <span class="title">网络延迟故障注入</span>
        </div>
      </div>
      
      <el-divider class="divider" />
      
      <div class="description">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title>此操作将增加网络数据包传输延迟，可能影响系统通信性能和响应时间</template>
        </el-alert>
      </div>
      
      <el-form :model="networkDelayInfo" :rules="rules" ref="formRef" label-position="top" class="injection-form">
        <!-- 通用参数区域 -->
        <div class="common-params">
          <el-form-item label="网络延迟时间" prop="delayTime">
            <div class="delay-time-input">
              <el-slider 
                v-model="networkDelayInfo.delayTime" 
                :min="1" 
                :max="5000" 
                :format-tooltip="value => `${value}ms`"
                show-input
              />
            </div>
            <div class="severity-indicator">
              <div class="severity-label">延迟影响:</div>
              <el-tag :type="getDelayLevelType" size="small">{{ getDelayLevelText }}</el-tag>
            </div>
          </el-form-item>
          
          <el-form-item label="持续时长" prop="timeout">
            <div class="duration-input">
              <el-input-number 
                v-model="networkDelayInfo.timeout" 
                :min="10" 
                :max="3600" 
                controls-position="right"
              />
              <span class="unit">秒</span>
            </div>
          </el-form-item>
          
          <el-form-item label="网卡名称" prop="nicName">
            <el-input v-model="networkDelayInfo.nicName" placeholder="eth0">
              <template #prefix>
                <el-icon><Monitor /></el-icon>
              </template>
            </el-input>
            <div class="nic-hint" v-if="networkDelayInfo.nicName">
              将在 {{ networkDelayInfo.nicName }} 网卡上应用延迟
            </div>
          </el-form-item>
        </div>
        
        <!-- 场景选择标签页 -->
        <div class="scenario-tabs">
          <el-tabs v-model="activeScenario" @tab-click="handleScenarioChange">
            <el-tab-pane label="指定IP及端口延迟" name="remote">
              <div class="scenario-content">
                <el-form-item label="目标服务器IP" prop="remoteIp">
                  <el-input 
                    v-model="networkDelayInfo.remoteIp" 
                    placeholder="支持多个IP，用英文逗号分隔"
                  >
                    <template #prefix>
                      <el-icon><LocationInformation /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                
                <el-form-item label="目标端口号" prop="remotePort">
                  <el-input 
                    v-model="networkDelayInfo.remotePort" 
                    placeholder="支持多个端口，用英文逗号分隔"
                  >
                    <template #prefix>
                      <el-icon><Share /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                
                <div class="scenario-description">
                  此场景将对指定的目标IP和端口的通信增加网络延迟
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="本机端口延迟" name="local">
              <div class="scenario-content">
                <el-form-item label="本机端口号" prop="localPort">
                  <el-input 
                    v-model="networkDelayInfo.localPort" 
                    placeholder="支持多个端口，用英文逗号分隔"
                  >
                    <template #prefix>
                      <el-icon><Share /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                
                <div class="scenario-description">
                  此场景将对本机指定端口的所有流量增加网络延迟
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="网卡全局延迟" name="nic">
              <div class="scenario-content">
                <el-form-item label="排除IP" prop="excludeIp">
                  <el-input 
                    v-model="networkDelayInfo.excludeIp" 
                    placeholder="排除的IP，用英文逗号分隔"
                  >
                    <template #prefix>
                      <el-icon><LocationInformation /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                
                <el-form-item label="排除端口号" prop="excludePort">
                  <el-input 
                    v-model="networkDelayInfo.excludePort" 
                    placeholder="排除的端口，默认排除32917"
                  >
                    <template #prefix>
                      <el-icon><Share /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                
                <div class="scenario-description">
                  此场景将对指定网卡的所有流量增加网络延迟（可排除指定IP和端口）
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <div class="form-actions">
          <el-button @click="closeDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleInjection" 
            :loading="injectionLoading" 
            :disabled="injectionLoading"
          >
            <el-icon><Warning /></el-icon>
            注入故障
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import {
  api_getDefaultNicName,
  api_injection,
  ChaosReqtDto,
} from "@/api/modules/service-server-operation/chaos";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { Connection, Warning, Monitor, LocationInformation, Share } from "@element-plus/icons-vue";

// 定义 props：接收父组件传入的服务器 ID
const props = defineProps<{ serverId: string }>();
const emit = defineEmits(["injectSuccess", "closeDialog"]);
const chaosType = "network-delay";
const formRef = ref<FormInstance>();

// 场景选择
const activeScenario = ref("remote");

// 注入表单数据和状态
const networkDelayInfo = ref({
  serverInfoId: props.serverId,
  delayTime: 100,
  timeout: 180,
  nicName: "",
  remoteIp: "",
  remotePort: "",
  localPort: "",
  excludePort: "",
  excludeIp: ""
});

// 计算延迟级别
const getDelayLevelType = computed(() => {
  const delay = networkDelayInfo.value.delayTime;
  if (delay <= 100) return "success";
  if (delay <= 500) return "warning";
  return "danger";
});

const getDelayLevelText = computed(() => {
  const delay = networkDelayInfo.value.delayTime;
  if (delay <= 100) return "轻微延迟";
  if (delay <= 500) return "中度延迟";
  return "严重延迟";
});

// 验证规则
const rules = ref<FormRules>({
  delayTime: [
    { required: true, message: "请设置延迟时间", trigger: "change" },
    { type: "number", min: 1, max: 5000, message: "延迟时间必须在1-5000ms之间", trigger: "change" }
  ],
  timeout: [
    { required: true, message: "请设置持续时长", trigger: "change" },
    { type: "number", min: 10, max: 3600, message: "时长必须在10-3600秒之间", trigger: "change" }
  ],
  nicName: [
    { required: true, message: "请输入网卡名称", trigger: "blur" }
  ],
  remoteIp: [
    { 
      required: true, 
      message: "请输入目标服务器IP", 
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (activeScenario.value === "remote" && !value) {
          callback(new Error("请输入目标服务器IP"));
        } else {
          callback();
        }
      }
    }
  ],
  localPort: [
    { 
      required: true, 
      message: "请输入本机端口号", 
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (activeScenario.value === "local" && !value) {
          callback(new Error("请输入本机端口号"));
        } else {
          callback();
        }
      }
    }
  ]
});

const injectionLoading = ref(false);

// 关闭对话框
const closeDialog = () => {
  emit("closeDialog");
};

// 切换场景时清空相关字段
const handleScenarioChange = () => {
  if (activeScenario.value === "remote") {
    networkDelayInfo.value.localPort = "";
    networkDelayInfo.value.excludeIp = "";
    networkDelayInfo.value.excludePort = "";
  } else if (activeScenario.value === "local") {
    networkDelayInfo.value.remoteIp = "";
    networkDelayInfo.value.remotePort = "";
    networkDelayInfo.value.excludeIp = "";
    networkDelayInfo.value.excludePort = "";
  } else if (activeScenario.value === "nic") {
    networkDelayInfo.value.remoteIp = "";
    networkDelayInfo.value.remotePort = "";
    networkDelayInfo.value.localPort = "";
  }
};

// 查询默认网卡名称
const queryNicName = function () {
  if (!networkDelayInfo.value.serverInfoId) {
    networkDelayInfo.value.nicName = "";
    return;
  }
  injectionLoading.value = true;
  api_getDefaultNicName(networkDelayInfo.value.serverInfoId)
    .then(res => {
      if (res.respCode === 2000) {
        networkDelayInfo.value.nicName = res.respData;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      injectionLoading.value = false;
    });
};

// 根据当前场景构建目标信息描述
const getTargetInfoForCurrentScenario = () => {
  if (activeScenario.value === "remote") {
    return `目标IP${networkDelayInfo.value.remoteIp}${
      networkDelayInfo.value.remotePort ? `:${networkDelayInfo.value.remotePort}` : ""
    }延迟${networkDelayInfo.value.delayTime}ms`;
  } else if (activeScenario.value === "local") {
    return `本机端口${networkDelayInfo.value.localPort}延迟${networkDelayInfo.value.delayTime}ms`;
  } else {
    return `网卡${networkDelayInfo.value.nicName}全局延迟${networkDelayInfo.value.delayTime}ms`;
  }
};

// 执行注入
const handleInjection = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async valid => {
    if (!valid) {
      return;
    }
    
    // 根据当前选择的场景进行校验
    if (activeScenario.value === "remote" && !networkDelayInfo.value.remoteIp) {
      ElMessage.warning("请输入目标服务器IP");
      return;
    } else if (activeScenario.value === "local" && !networkDelayInfo.value.localPort) {
      ElMessage.warning("请输入本机端口号");
      return;
    } else if (!networkDelayInfo.value.nicName) {
      ElMessage.warning("请输入网卡名称");
      return;
    }
    
    injectionLoading.value = true;
    
    // 构建目标信息描述
    const targetInfo = getTargetInfoForCurrentScenario();
    
    const chaosReqtDto: ChaosReqtDto = {
      serverInfoId: networkDelayInfo.value.serverInfoId,
      chaosType,
      chaosCommandPrefix: "create network delay",
      paramMap: {
        "--destination-ip": networkDelayInfo.value.remoteIp,
        "--exclude-port": networkDelayInfo.value.excludePort,
        "--exclude-ip": networkDelayInfo.value.excludeIp,
        "--interface": networkDelayInfo.value.nicName,
        "--local-port": networkDelayInfo.value.localPort,
        "--remote-port": networkDelayInfo.value.remotePort,
        "--time": networkDelayInfo.value.delayTime,
        "--timeout": networkDelayInfo.value.timeout
      },
      targetInfo
    };
    
    api_injection(chaosReqtDto)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("网络延迟故障注入成功");
          emit("injectSuccess");
          closeDialog();
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        injectionLoading.value = false;
      });
  });
};

// 组件挂载时查询网卡名称
watch(() => props.serverId, () => {
  networkDelayInfo.value.serverInfoId = props.serverId;
  queryNicName();
}, { immediate: true });
</script>

<style scoped lang="scss">
.network-delay-injection {
  padding: 0 16px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    
    .title-area {
      display: flex;
      align-items: center;
      
      .icon {
        color: #409eff;
        font-size: 20px;
        margin-right: 8px;
      }
      
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .divider {
    margin: 12px 0;
  }
  
  .description {
    margin-bottom: 20px;
  }
  
  .injection-form {
    padding: 16px 0;
    
    .common-params {
      margin-bottom: 24px;
    }
    
    .severity-indicator {
      display: flex;
      align-items: center;
      margin-top: 8px;
      
      .severity-label {
        font-size: 12px;
        color: #909399;
        margin-right: 8px;
      }
    }
    
    .nic-hint {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
    
    .duration-input {
      display: flex;
      align-items: center;
      
      .unit {
        margin-left: 8px;
        color: #606266;
      }
    }
    
    .scenario-tabs {
      margin-bottom: 24px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 16px;
      background-color: #f9fafc;
      
      .scenario-content {
        padding: 8px 0;
      }
      
      .scenario-description {
        font-size: 12px;
        color: #909399;
        margin-top: 12px;
        padding: 8px;
        background-color: #ecf5ff;
        border-radius: 4px;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 30px;
    }
  }
}
</style>
