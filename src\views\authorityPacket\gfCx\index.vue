<template>
  <div v-loading="cardLoading">
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span class="title">高法-查询</span>
        </div>
      </template>
      
      <el-collapse v-model="activeNames" class="query-form-collapse">
        <el-collapse-item 
          v-for="(cxqq, index) in attrValues.cxqqList" 
          :key="index"
          :name="'cxqq' + index"
        >
          <template #title>
            <div class="collapse-title">
              <span>cxqq</span>
            </div>
          </template>
          
          <div class="form-toolbar">
            <div class="template-controls">
              <el-select
                v-model="selectedCommonInfoIdList[index]"
                @change="commonInfoChange(index)"
                size="small"
                placeholder="选择常用信息模板"
                clearable
                style="width: 220px"
              >
                <el-option v-for="item in commonInfoList" :key="item.id" :value="item.id!" :label="item.infoName" />
              </el-select>
              
              <el-button 
                type="primary" 
                size="small" 
                v-if="selectedCommonInfoIdList[index]" 
                @click="updateCommonInfo('cxqq', index)" 
                plain
              >
                <el-icon><RefreshRight /></el-icon>更新信息
              </el-button>
              
              <el-popconfirm 
                title="确定要删除这条常用信息吗?" 
                v-if="selectedCommonInfoIdList[index]" 
                @confirm="deleteCommonInfo(index)"
                confirm-button-text="删除"
                cancel-button-text="取消"
              >
                <template #reference>
                  <el-button type="danger" size="small" plain>
                    <el-icon><Delete /></el-icon>删除信息
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>
          
          <el-form label-width="180" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="WSBH[文书编号]">
                  <el-input v-model="cxqq.wsbh" style="width: 260px;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="BDHM[表单号码]">
                  <el-input v-model="cxqq.bdhm" style="width: 260px;" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="XM[姓名]">
                  <el-input v-model="cxqq.xm" clearable style="width: 260px;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="ZJLX[证件类型]">
                  <el-select v-model="cxqq.zjlx" clearable filterable style="width: 200px;">
                    <el-option v-for="item in idType2" :key="item.key" :value="item.key" :label="item.key + '（' + item.label + '）'" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="DSRZJHM[证件号码]">
                  <el-input v-model="cxqq.dsrzjhm" clearable style="width: 260px;" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <div class="bottom-actions">
              <el-button type="success" size="small" @click="saveCommonInfo('cxqq', cxqq)" plain>
                <el-icon><Download /></el-icon>保存为常用信息
              </el-button>
            </div>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      
      <div class="card-footer-actions">
        <el-button type="info" plain size="small" @click="initRandomDigits">
          <el-icon><Refresh /></el-icon>刷新随机数
        </el-button>
        <el-button type="primary" size="small" @click="addCxqq" plain>
          <el-icon><Plus /></el-icon>添加cxqq
        </el-button>
        <el-button type="danger" size="small" @click="deleteCxqq" plain v-if="attrValues.cxqqList.length !== 1">
          <el-icon><Delete /></el-icon>删除cxqq
        </el-button>
      </div>
    </el-card>

    <el-card class="action-card">
      <template #header>
        <div class="card-header">
          <span class="title">操作</span>
        </div>
      </template>
      
      <div class="action-buttons">
        <el-button type="warning" @click="generatePacketGf">
          <el-icon><Document /></el-icon>生成报文
        </el-button>
        <el-button type="primary" @click="downloadZipPacket" :disabled="!packetZipName">
          <el-icon><Download /></el-icon>下载
        </el-button>
        <el-button type="success" @click="ftpUpload" :disabled="!packetZipName">
          <el-icon><Upload /></el-icon>上传至服务器
        </el-button>
      </div>
      
      <div v-if="packetZipName" class="file-info">
        <el-tag type="success">文件已生成: {{ packetZipName }}</el-tag>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  api_deleteCommonInfo,
  api_downloadZipPacket,
  api_editCommonInfo,
  api_ftpUpload,
  api_generatePacketGf,
  api_getCommonInfo,
  api_getXmlStructureTree,
  api_saveTemplate,
  EntryDto,
  PacketCommonInfo,
  XmlTreeDto
} from "@/api/modules/service-integrated-tester/authority-packet";
import { onMounted, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import idType2 from "@/data/idType2.json";
import { Plus, Delete, Download, Refresh, RefreshRight, Document, Upload } from '@element-plus/icons-vue';

const packetType = "高法-查询";
const templateFolderName = "CXQQF059H1013702010010000A6YHYH00036120210514001885";
const templateXmlName = "QAF059H1013702010010000A6YHYH00036120210514001885.xml";
const ftpRemotePath = "/最高人民法院/Download";
const templateCxqq = {
  wsbh: "",
  bdhm: "",
  xm: "",
  zjlx: "",
  dsrzjhm: ""
};

const lastRandomDigits = ref();
const cardLoading = ref(false);
const activeNames = ref(["cxqq0"]);
const commonInfoList = ref<PacketCommonInfo[]>([]);
const selectedCommonInfoIdList = ref<(string | undefined)[]>([]);
const selectedCommonInfoList = ref<(PacketCommonInfo | undefined)[]>([]);
const packetXml = ref<XmlTreeDto>();
const attrValues = ref({
  cxqqList: [{ ...templateCxqq }]
});
const packetZipName = ref<string>();

const initRandomDigits = function () {
  lastRandomDigits.value =
    "0000AVYHYH" +
    Date.now().toString() +
    Math.floor(Math.random() * ********)
      .toString()
      .padStart(7, "0");
  for (let i = 0; i < attrValues.value.cxqqList.length; i++) {
    const currFreezeAccount = attrValues.value.cxqqList[i];
    currFreezeAccount.wsbh = lastRandomDigits.value.substring(4);
    currFreezeAccount.bdhm =
      "A" +
      Date.now().toString() +
      Math.floor(Math.random() * *********)
        .toString()
        .padStart(8, "0");
  }
};

const queryTemplateXml = function () {
  cardLoading.value = true;
  api_getXmlStructureTree({
    templateFolderName,
    templateXmlName
  })
    .then(res => {
      if (res.respCode === 2000) {
        packetXml.value = res.respData;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      cardLoading.value = false;
    });
};

const rulesCheck = function () {
  for (let i = 0; i < attrValues.value.cxqqList.length; i++) {
    const currCxqq = attrValues.value.cxqqList[i];
    const errMsgPrefix = "第" + (i + 1) + "个元素 - ";
    if (!currCxqq.wsbh) {
      ElMessage.error(errMsgPrefix + "WSBH[文书编号]为必填项");
      return false;
    }
    if (!currCxqq.bdhm) {
      ElMessage.error(errMsgPrefix + "BDHM[表单号码]为必填项");
      return false;
    }
    if (!currCxqq.xm) {
      ElMessage.error(errMsgPrefix + "xm[姓名]为必填项");
      return false;
    }
    if (!currCxqq.zjlx) {
      ElMessage.error(errMsgPrefix + "ZJLX[证件类型]为必填项");
      return false;
    }
    if (!currCxqq.dsrzjhm) {
      ElMessage.error(errMsgPrefix + "DSRZJHM[证件号码]为必填项");
      return false;
    }
  }
  return true;
};

const assignValue = function () {
  const templateCxqqTree = JSON.parse(JSON.stringify(packetXml.value?.children.find(item => item.tag === "cxqq")));
  for (let i = 0; i < attrValues.value.cxqqList.length; i++) {
    const currCxqq = attrValues.value.cxqqList[i];
    const currTree = JSON.parse(JSON.stringify(templateCxqqTree));
    for (let key in currCxqq) {
      currTree!.attributeList.find((item: EntryDto) => item.key === key.toUpperCase())!.value = currCxqq[key];
    }
    packetXml.value!.children[i] = currTree!;
  }
};

const generatePacketGf = function () {
  cardLoading.value = true;
  if (!rulesCheck()) {
    cardLoading.value = false;
    return;
  }
  assignValue();
  api_generatePacketGf({
    xmlTreeDto: packetXml.value!,
    templateFolderName,
    templateXmlName,
    lastRandomDigits: lastRandomDigits.value
  })
    .then(res => {
      if (res.respCode === 2000) {
        packetZipName.value = res.respData;
        ElMessage.success("报文包生成成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      cardLoading.value = false;
    });
};

const downloadZipPacket = function () {
  if (!packetZipName.value) {
    ElMessage.error({ message: "无文件下载，请先生成文件", showClose: true, duration: 10000 });
    return;
  }
  api_downloadZipPacket(packetZipName.value!);
};

const ftpUpload = function () {
  cardLoading.value = true;
  if (!packetZipName.value) {
    ElMessage.warning({ message: "无文件上传，请先生成文件", showClose: true, duration: 10000 });
    cardLoading.value = false;
    return;
  }
  api_ftpUpload(packetZipName.value, ftpRemotePath)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("上传成功");
      } else {
        ElMessage.error({ message: res.respMsg, showClose: true, duration: 10000 });
      }
    })
    .finally(() => {
      cardLoading.value = false;
    });
};

const initCxqq = function (listIndex: number) {
  const cxqq = attrValues.value.cxqqList[listIndex];
  cxqq.wsbh = lastRandomDigits.value.substring(4);
  cxqq.bdhm =
    "A" +
    Math.floor(Math.random() * *********)
      .toString()
      .padStart(8, "0") +
    Date.now().toString();
  cxqq.xm = "";
  cxqq.zjlx = "";
  cxqq.dsrzjhm = "";
};

const queryCommonInfo = function (tagName: string) {
  api_getCommonInfo({
    infoName: "",
    packetType,
    tagName,
    infoContent: ""
  }).then(res => {
    if (res.respCode === 2000) {
      commonInfoList.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const commonInfoChange = function (listIndex: number) {
  initCxqq(listIndex);
  if (!selectedCommonInfoIdList.value[listIndex]) {
    selectedCommonInfoList!.value[listIndex] = undefined;
    return;
  }
  selectedCommonInfoList.value[listIndex] = commonInfoList.value.find(
    item => item.id === selectedCommonInfoIdList.value[listIndex]
  );
  const currCxqq = attrValues.value.cxqqList[listIndex];
  const commonInfoJson = JSON.parse(selectedCommonInfoList.value[listIndex]!.infoContent);
  for (let key in commonInfoJson) {
    currCxqq[key] = commonInfoJson[key];
  }
};

const saveCommonInfo = function (tagName: string, infoContent: Object) {
  let copy = JSON.parse(JSON.stringify(infoContent));
  delete copy.wsbh;
  delete copy.bdhm;
  ElMessageBox.prompt("请输入名称", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(({ value }) => {
    cardLoading.value = true;
    api_saveTemplate({
      infoName: value,
      packetType,
      tagName,
      infoContent: JSON.stringify(copy)
    })
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("信息保存成功");
          queryCommonInfo(tagName);
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        cardLoading.value = false;
      });
  });
};

const updateCommonInfo = function (tagName: string, listIndex: number) {
  let copyCxqq = JSON.parse(JSON.stringify(attrValues.value.cxqqList[listIndex]));
  delete copyCxqq.wsbh;
  delete copyCxqq.bdhm;
  const newCommonInfo: PacketCommonInfo = {
    id: selectedCommonInfoIdList.value[listIndex],
    infoName: selectedCommonInfoList.value[listIndex]!.infoName,
    packetType,
    tagName,
    infoContent: JSON.stringify(copyCxqq)
  };
  api_editCommonInfo(newCommonInfo)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("更新成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      queryCommonInfo("cxqq");
    });
};

const deleteCommonInfo = function (listIndex: number) {
  api_deleteCommonInfo(selectedCommonInfoIdList.value[listIndex]!)
    .then(res => {
      if (res.respCode === 2000) {
        selectedCommonInfoIdList.value[listIndex] = undefined;
        commonInfoChange(listIndex);
        ElMessage.success("删除成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      queryCommonInfo("cxqq");
    });
};

const addCxqq = function () {
  attrValues.value.cxqqList.push({ ...templateCxqq });
  initCxqq(attrValues.value.cxqqList.length - 1);
  selectedCommonInfoIdList.value.push(undefined);
  selectedCommonInfoList.value.push(undefined);
};

const deleteCxqq = function () {
  attrValues.value.cxqqList.pop();
  selectedCommonInfoIdList.value.pop();
  selectedCommonInfoList.value.pop();
};

onMounted(() => {
  queryTemplateXml();
  queryCommonInfo("cxqq");
  initRandomDigits();
  initCxqq(0);
});
</script>

<style scoped lang="scss">
.query-card, .action-card {
  margin-bottom: 16px;
  border-radius: 8px;
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .form-info {
    color: #606266;
    font-size: 13px;
    background-color: #f0f2f5;
    padding: 2px 8px;
    border-radius: 4px;
    
    small {
      color: #909399;
      margin-left: 4px;
    }
  }
}

.form-toolbar {
  display: flex;
  margin-bottom: 16px;
  background: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  
  .template-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.query-form {
  margin-top: 10px;
  
  :deep(.el-input),
  :deep(.el-select) {
    width: 100%;
  }
}

.bottom-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-start;
}

.card-footer-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.query-form-collapse {
  :deep(.el-collapse-item__header) {
    padding: 0 8px;
    
    &.is-active {
      background-color: #ecf5ff;
      border-bottom-color: #d9ecff;
    }
  }
  
  :deep(.el-collapse-item__wrap) {
    padding: 16px;
    background-color: #fbfbfb;
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.file-info {
  margin-top: 16px;
}
</style>
