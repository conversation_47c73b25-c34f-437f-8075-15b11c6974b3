<template>
  <div class="container">

    <div class="card">
      <el-button type="info" plain size="small" @click="handleReturn">返回</el-button>
    </div>

    <div class="card" style="margin-top: 0.5%">
      <el-table :data="replayInfoTable.data" border highlight-current-row>
        <el-table-column label="回放ID" prop="id" width="300" fixed="left" />
        <el-table-column label="URL" prop="url" width="350" />
        <el-table-column label="接口ID" prop="interfaceId" width="100" />
        <el-table-column label="请求时间" prop="requestTime" width="180" />
        <el-table-column label="版本号" prop="versionNumber" width="100" />
        <el-table-column label="操作" min-width="320" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="showReqt(scope.row.id)">请求报文</el-button>
            <el-button type="primary" size="small" link @click="showRawResp(scope.row.id)">原响应报文</el-button>
            <el-button type="primary" size="small" link @click="showReplayResp(scope.row.id)">回放响应报文</el-button>
            <el-button type="primary" size="small" link disabled>比对信息</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="dialogData.visible" :title="dialogData.title">
        <pre v-text="dialogData.content"/>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onBeforeMount, ref} from "vue";
import {api_getReplayDetailFieldValue, api_getReplayInfo, EsbReplayInfo} from "@/api/modules/esbNetworkReplay/replay";
import {TransParams} from "@/views/esbNetworkReplay/replayManagement/replayPlan/index.vue";
import vkbeautify from "vkbeautify";

interface ReplayInfoTable {
  data: EsbReplayInfo[];
}

const props = defineProps<{ transParams: TransParams }>();
const emits = defineEmits(['changeComponent']);

const replayInfoTable = ref<ReplayInfoTable>({
  data: []
});

const dialogData = ref({
  visible: false,
  title: '',
  content: ""
});

const queryReplayInfo = function () {
  api_getReplayInfo(props.transParams.selectedReplayPlanId).then(res => {
    if (res.respCode === 2000) {
      replayInfoTable.value.data = res.respData;
    }
  })
};

const showReqt = function (infoId: string) {
  dialogData.value.title = "请求报文";
  dialogData.value.visible = true;
  queryReqtBody(infoId, 'REPLAY_REQUEST_BODY');
};

const showRawResp = function (infoId: string) {
  dialogData.value.title = "原响应报文";
  dialogData.value.visible = true;
  queryRawResp(infoId, 'RAW_RESPONSE_BODY');
};

const showReplayResp = function (infoId: string) {
  dialogData.value.title = "回放响应报文";
  dialogData.value.visible = true;
  queryReplayResp(infoId, 'REPLAY_RESPONSE_BODY');
};

const queryReqtBody = function (infoId: string, field: string) {
  api_getReplayDetailFieldValue(infoId, field).then(res => {
    if (res.respCode === 2000) {
      dialogData.value.content = vkbeautify.xml(res.respData.replayRequestBody);
    }
  });
};

const queryRawResp = function (infoId: string, field: string) {
  api_getReplayDetailFieldValue(infoId, field).then(res => {
    if (res.respCode === 2000) {
      dialogData.value.content = vkbeautify.xml(res.respData.rawResponseBody);
    }
  });
};

const queryReplayResp = function (infoId: string, field: string) {
  api_getReplayDetailFieldValue(infoId, field).then(res => {
    if (res.respCode === 2000) {
      dialogData.value.content = vkbeautify.xml(res.respData.replayResponseBody);
    }
  });
}

const handleReturn = function () {
  emits('changeComponent', 'ReplayPlan');
}

onBeforeMount(() => {
  queryReplayInfo();
})
</script>

<style scoped lang="scss">

</style>
