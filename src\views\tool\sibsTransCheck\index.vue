<!--新核心 ESB报文XML JSON互转校验-->

<template>
  <div class="container" v-loading="viewLoading" :element-loading-text="viewLoadingText">
    <div class="card">
      <!--上传-->
      <el-upload
        v-model:file-list="fileList"
        :action="uploadUrl"
        multiple
        :before-upload="handleBeforeUpload"
        accept=".zip, .json"
        :show-file-list="true"
        style="width: 30%"
        :on-success="queryUploadFileList"
      >
        <template #trigger>
          <el-button type="primary">文件上传</el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip">
            请上传.zip或.json文件
          </div>
        </template>
      </el-upload>
    </div>

    <!--已上传的文件列表-->
    <div class="card non-top-card" v-loading="fileListLoading" element-loading-text="数据加载中...">
      <el-table :data="uploadFileList" border>
        <el-table-column prop="uploadFileName" label="文件名" width="300" fixed="left" />
        <el-table-column prop="fileType" label="文件类型" width="200" />
        <el-table-column prop="uploadTime" label="上传时间" />
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-popconfirm title="确定要解析吗" @confirm="parseToDB(scope.row.id)">
              <template #reference>
                <el-button type="primary" plain size="small">解析入库</el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm title="确定要删除吗" @confirm="removeFile(scope.row.id)">
              <template #reference>
                <el-button type="danger" plain size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!--解析的报文列表-->
    <div class="card non-top-card" v-loading="httpDataLoading" element-loading-text="数据加载中...">
      <div class="seqNo-search">
        <el-input placeholder="seqNo查询" v-model="seqNoInput" clearable />
        <el-input placeholder="transTimestamp" v-model="transTimestampInput" clearable />
        <el-button type="info" plain @click="search">查询</el-button>
      </div>
      <el-table :data="pagedHttpDataInfo.pageData" border>
        <el-table-column prop="seqNo" label="seqNo" width="300" />
        <el-table-column prop="transTimestamp" label="transTimestamp" width="200" />
        <el-table-column prop="contentType" label="数据类型" width="200" />
        <el-table-column prop="httpType" label="分类" width="150" />
        <el-table-column prop="interfaceId" label="接口ID" width="150" />
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button type="primary" plain size="small" @click="showHttpData(scope.row.id)">查看报文</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!--报文列表表格分页栏-->
      <el-pagination
        :current-page="pagedHttpDataInfo.pageNo"
        :page-size="pagedHttpDataInfo.pageSize"
        :layout="'prev, pager, next'"
        :total="pagedHttpDataInfo.totalCount"
        @current-change="handleHttpDataInfoPageChange"
      />

      <!--报文详情弹窗-->
      <el-dialog v-model="httpDataDialogVisible" title="报文详情">
        <span v-text="httpData"></span>
      </el-dialog>
    </div>

    <el-divider />

    <!--校验相关按钮-->
    <div class="card">
      <el-popconfirm title="确定清空报文数据？" @confirm="clearHttpDataDB">
        <template #reference>
          <el-button class="clear-db-button" type="danger" plain>清空报文数据</el-button>
        </template>
      </el-popconfirm>
      <el-popconfirm title="确定清空校验结果？" @confirm="clearCheckResultDB">
        <template #reference>
          <el-button class="clear-db-button" type="danger" plain>清空校验结果</el-button>
        </template>
      </el-popconfirm>
      <el-popconfirm title="此操作将清空报文数据及校验结果" @confirm="clearDB">
        <template #reference>
          <el-button class="clear-db-button" type="danger" plain>清空所有数据</el-button>
        </template>
      </el-popconfirm>
      <el-popconfirm title="确定要开始校验吗" @confirm="transCheck">
        <template #reference>
          <el-button type="primary">开始校验</el-button>
        </template>
      </el-popconfirm>
      <el-button type="primary" plain @click="showCheckConfigDrawer">校验配置</el-button>

      <!--校验配置抽屉-->
      <el-drawer v-model="checkConfigDrawerVisible" :title="'校验配置'" :size="'30%'">
        <el-select v-model="selectedConfigType" style="width: 50%" @change="queryCheckConfigList(selectedConfigType)">
          <el-option
            v-for="item in configTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button style="margin-left: 2%" type="primary" plain @click="addCheckConfig">添加</el-button>
        <el-table :data="checkConfigList" border v-loading="checkConfigTableLoading" style="margin-top: 3%">
          <el-table-column prop="content" label="标签" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-popconfirm title="确定要删除吗" @confirm="deleteCheckConfig(scope.row.id)">
                <template #reference>
                  <el-button type="danger" plain size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-drawer>

    </div>

    <!--校验结果列表-->
    <div class="card non-top-card" v-loading="transCheckResultTableLoading" element-loading-text="数据加载中...">
      <el-table :data="pagedTransCheckResult.pageData" border>
        <el-table-column prop="seqNo" label="seqNo" width="300" />
        <el-table-column prop="transTimestamp" label="transTimestamp" width="200" />
        <el-table-column prop="interfaceId" label="接口ID" width="180" />
        <el-table-column prop="checkResult" label="校验结果" width="180" >
          <template #default="scope">
            <el-tag v-if="scope.row.checkResult === '0'" type="danger">不通过</el-tag>
            <el-tag v-else type="success">通过</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button type="primary" plain size="small" v-if="scope.row.checkResult === '0'" @click="showFailedInfo(scope.row.id)">查看原因</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!--校验结果表格分页栏-->
      <el-pagination
        :current-page="pagedTransCheckResult.pageNo"
        :page-size="pagedTransCheckResult.pageSize"
        :layout="'prev, pager, next'"
        :total="pagedTransCheckResult.totalCount"
        @current-change="handleTransCheckResultPageChange"
      />

      <!--失败详情弹窗-->
      <el-dialog v-model="failedInfoDialogVisible" title="失败详情" @closed="failedInfoDialogClose">
        <span v-html="failedInfo?.failedInfo"></span>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import {
  api_addCheckConfig,
  api_clearCheckResultDB,
  api_clearDB,
  api_clearHttpDataDB, api_deleteCheckConfigById, api_getCheckConfigListByType,
  api_getFailedInfoByResultId,
  api_getHttpDataByInfoId,
  api_getPagedHttpDataInfo,
  api_getPagedTransCheckResult,
  api_getUploadFileList,
  api_parseToDB,
  api_removeUploadFile,
  api_sibsTransCheck, SibsFileUpload, SibsHttpDataInfo, SibsTransCheckConfig, SibsTransCheckFailed, SibsTransCheckResult
} from "@/api/modules/tool/sibsTransCheck";
import { ElMessage, ElMessageBox, UploadRawFile } from "element-plus";
import { Page } from "@/api/interface";

const uploadUrl = import.meta.env.VITE_API_URL + '/api/tool/sibsTransCheck/upload';
const fileList = ref([]);
const uploadFileList = ref<SibsFileUpload[]>([]);
const viewLoading = ref(false);
const fileListLoading = ref(false);
const httpDataLoading = ref(false);
const pagedHttpDataInfo = ref<Page<SibsHttpDataInfo[]>>({
  pageNo: 1,
  pageSize: 10,
  pageCount: 0,
  totalCount: 0,
  pageData: []
});
const httpDataDialogVisible = ref(false);
const httpData = ref<string>();
const seqNoInput = ref("");
const transTimestampInput = ref("");
const pagedTransCheckResult = ref<Page<SibsTransCheckResult[]>>({
  pageNo: 1,
  pageSize: 10,
  pageCount: 0,
  totalCount: 0,
  pageData: []
})
const transCheckResultTableLoading = ref(false);
const failedInfo = ref<SibsTransCheckFailed>({
  id: '',
  failedInfo: ''
});
const failedInfoDialogVisible = ref(false);
const viewLoadingText = ref('');
const checkConfigDrawerVisible = ref(false);
const selectedConfigType = ref('1');
const configTypeOptions = [
  {
    label: 'json对象转array标签',
    value: '1'
  },
  {
    label: 'json body比对忽略标签',
    value: '2'
  },
  {
    label: '响应sysHead比对忽略',
    value: '3'
  },
  {
    label: '请求sysHead比对忽略',
    value: '4'
  }
];
const checkConfigList = ref<SibsTransCheckConfig[]>([]);
const checkConfigTableLoading = ref(false);

// 上传前拦截处理，判断文件类型
const handleBeforeUpload = function (rawFile: UploadRawFile) {
  if (rawFile.name.endsWith('.zip') || rawFile.name.endsWith('.json')) {
    return true;
  }
  ElMessage.error('请上传.zip或.json文件');
  return false;
}

// 获取已上传的文件列表
const queryUploadFileList = async function () {
  fileListLoading.value = true;
  await api_getUploadFileList().then(res => {
    if (res.respCode === 2000){
      uploadFileList.value = res.respData;
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).catch(() => {
    fileListLoading.value = false;
  });
  fileListLoading.value = false;
}

// 将上传的文件解析到数据库
const parseToDB = async function (id: string) {
  viewLoading.value = true;
  viewLoadingText.value = "解析入库中...";
  await api_parseToDB(id).then(async res => {
    if (res.respCode === 2000) {
      ElMessage.success("解析入库成功");
      await queryPagedHttpData(1, 10);
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    viewLoading.value = false;
    viewLoadingText.value = "";
  });
}

// 删除已上传的文件
const removeFile = async function (id: string) {
  fileListLoading.value = true;
  await api_removeUploadFile(id).then(async res => {
    if (res.respCode === 2000){
      ElMessage.success('删除成功');
      await queryUploadFileList();
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  });
  fileListLoading.value = false;
}

// 请求解析的报文数据，分页
const queryPagedHttpData = async function (pageNo: number, pageSize: number) {
  httpDataLoading.value = true;
  await api_getPagedHttpDataInfo(pageNo, pageSize, seqNoInput.value, transTimestampInput.value).then(res => {
    if (res.respCode === 2000){
      pagedHttpDataInfo.value = res.respData;
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).catch(() => {
    httpDataLoading.value = false;
  })
  httpDataLoading.value = false;
}

// 解析的报文数据表格分页切换
const handleHttpDataInfoPageChange = function (pageNo: number) {
  queryPagedHttpData(pageNo, 10);
}

// 根据id查看解析报文详情
const showHttpData = function (infoId: string) {
  api_getHttpDataByInfoId(infoId).then(res => {
    if (res.respCode === 2000){
      httpDataDialogVisible.value = true;
      httpData.value = res.respData.data;
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  })
}

// 根据seqNo搜索解析报文
const search = function () {
  api_getPagedHttpDataInfo(1, 10, seqNoInput.value, transTimestampInput.value).then(res => {
    if (res.respCode === 2000){
      pagedHttpDataInfo.value = res.respData;
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  })
}

// 清除已解析的报文数据库
const clearHttpDataDB = function () {
  api_clearHttpDataDB().then(async res => {
    httpDataLoading.value = true;
    if (res.respCode === 2000){
      ElMessage.success('清空成功');
      await queryPagedHttpData(1, 10);
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    httpDataLoading.value = false;
  })
}

// 清除校验结果数据库
const clearCheckResultDB = function () {
  transCheckResultTableLoading.value = true;
  api_clearCheckResultDB().then(async res => {
    if (res.respCode === 2000){
      ElMessage.success('清空成功');
      await queryPagedTransCheckResult(1, 10);
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    transCheckResultTableLoading.value = false;
  })
}

// 同时清除已解析的报文和校验结果数据库
const clearDB = function () {
  viewLoading.value = true;
  viewLoadingText.value = "清空数据中...";
  api_clearDB().then(async res => {
    if (res.respCode === 2000){
      ElMessage.success('清空成功');
      await queryUploadFileList();
      await queryPagedHttpData(1, 10);
      await queryPagedTransCheckResult(1, 10);
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    viewLoading.value = false;
    viewLoadingText.value = "";
  })
}

// 显示校验配置抽屉
const showCheckConfigDrawer = function () {
  checkConfigDrawerVisible.value = true;
  queryCheckConfigList(selectedConfigType.value);
}

// 请求校验配置列表
const queryCheckConfigList = async function (type: string) {
  checkConfigTableLoading.value = true;
  await api_getCheckConfigListByType(type).then(res => {
    if (res.respCode === 2000){
      checkConfigList.value = res.respData;
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    checkConfigTableLoading.value = false;
  });
}

// 添加校验配置
const addCheckConfig = function () {
  ElMessageBox.prompt("请输入内容", "添加配置", {
    confirmButtonText: "添加",
    cancelButtonText: "取消"
  }).then(({ value }) => {
    api_addCheckConfig({
      type: selectedConfigType.value,
      content: value
    }).then(res => {
      if (res.respCode === 2000){
        ElMessage.success("添加成功");
        queryCheckConfigList(selectedConfigType.value);
      }
      if (res.respCode === 5000){
        ElMessage.error(res.respMsg);
      }
    })
  })
}

// 删除校验配置
const deleteCheckConfig = function (id: string) {
  api_deleteCheckConfigById(id).then(async res => {
    if (res.respCode === 2000){
      ElMessage.success("删除成功");
      await queryCheckConfigList(selectedConfigType.value);
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  })
}

// 报文xml-json互转校验
const transCheck = function () {
  transCheckResultTableLoading.value = true;
  api_sibsTransCheck().then(async res => {
    if (res.respCode === 2000){
      ElMessage.success("校验完成");
      await queryPagedTransCheckResult(1, 10);
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    transCheckResultTableLoading.value = false;
  });
}

// 请求校验结果，分页
const queryPagedTransCheckResult = async function (pageNo: number, pageSize: number) {
  transCheckResultTableLoading.value = true;
  await api_getPagedTransCheckResult(pageNo, pageSize).then(res => {
    if (res.respCode === 2000){
      pagedTransCheckResult.value = res.respData;
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    transCheckResultTableLoading.value = false;
  })
}

// 校验结果分页切换
const handleTransCheckResultPageChange = (pageNo: number) => {
  queryPagedTransCheckResult(pageNo, 10);
}

// 根据resultId查询失败信息
const queryFailedInfoByResultId = function (resultId: string) {
  api_getFailedInfoByResultId(resultId).then(res => {
    if (res.respCode === 2000){
      failedInfo.value = res.respData;
    }
    if (res.respCode === 5000){
      ElMessage.error(res.respMsg);
    }
  })
}

// 展示失败信息
const showFailedInfo = function (resultId: string) {
  failedInfoDialogVisible.value = true;
  queryFailedInfoByResultId(resultId);
}

// 失败信息弹窗关闭
const failedInfoDialogClose = function () {
  failedInfo.value.failedInfo = '';
}

onMounted(() => {
  queryUploadFileList();
  queryPagedHttpData(0, 10);
  queryPagedTransCheckResult(0, 10);
})
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
