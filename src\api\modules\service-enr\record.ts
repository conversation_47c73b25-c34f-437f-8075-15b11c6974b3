import http from "@/api";
import {SERVICE_ENR} from "@/api/config/servicePort";
import {Page} from "@/api/interface";

export interface RecordInfo {
  id?: string;
  host?: string;
  interfaceId?: string;
  requestTime?: number;
  responseTime?: number;
  svcCorrId?: string;
  channelId?: string;
  transCde?: string;
  csmrId?: string;
  versionNumber?: string;
  esbRespMsg?: string;
  esbRespCode?: string;
  providerId?: string
}

export interface RecordInfoDto extends RecordInfo {
  reqtStartTime?: number;
  reqtEndTime?: number;
}

export interface RecordDetail {
  recordId: string;
  requestBody: string;
  responseBody: string;
}

export const api_fetchInfoByCondition = (pageNo: number, pageSize: number, recordInfoDto: RecordInfoDto) => {
  return http.post<Page<RecordInfo[]>>(SERVICE_ENR + "/record/info/fetchByCondition?pageNo=" + pageNo + "&pageSize=" + pageSize, (recordInfoDto));
}

export const api_fetchInfoColData = (colNameList: string[]) => {
  return http.post<RecordInfo[]>(SERVICE_ENR + "/record/info/fetchColData", colNameList);
}

export const api_fetchDetailColData = (recordId: string, colNameList: string[]) => {
  return http.post<RecordDetail>(SERVICE_ENR + "/record/detail/fetchColData?recordId=" + recordId, colNameList);
}

export const api_deleteRecord = (interfaceId: string, reqtStartTime: number, reqtEndTime: number) => {
  return http.get(SERVICE_ENR + "/record/deleteRecord", {interfaceId, reqtStartTime, reqtEndTime})
}
