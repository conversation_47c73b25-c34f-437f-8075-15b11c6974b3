import http from "@/api";
import { SERVICE_INTEGRATED_TESTER } from "@/api/config/servicePort";

export interface EntryDto {
  key: string;
  value: string;
}

export interface XmlTreeDto {
  tag: string;
  fullPath: string;
  attributeList: EntryDto[];
  children: XmlTreeDto[];
}

export interface XmlStructureQueryDto {
  templateFolderName: string;
  templateXmlName: string;
}

export interface GeneratePacketDto {
  xmlTreeDto: XmlTreeDto;
  templateFolderName: string;
  templateXmlName: string;
  lastRandomDigits: string;
}

export interface PacketCommonInfo {
  id?: string;
  infoName: string;
  packetType: string;
  tagName: string;
  infoContent: string;
}

export const api_getXmlStructureTree = (xmlStructureQueryDto: XmlStructureQueryDto) => {
  return http.post<XmlTreeDto>(SERVICE_INTEGRATED_TESTER + "/authorityPacket/getXmlStructureTree", xmlStructureQueryDto);
};

export const api_generatePacket = (generatePacketDto: GeneratePacketDto) => {
  return http.post<string>(SERVICE_INTEGRATED_TESTER + "/authorityPacket/generatePacket", generatePacketDto);
};

export const api_generatePacketGf = (generatePacketDto: GeneratePacketDto) => {
  return http.post<string>(SERVICE_INTEGRATED_TESTER + "/authorityPacket/generatePacketGf", generatePacketDto);
};

export const api_downloadZipPacket = (packetZipName: string) => {
  window.open(
    import.meta.env.VITE_API_URL + SERVICE_INTEGRATED_TESTER + "/authorityPacket/downloadZipPacket?packetZipName=" + packetZipName
  );
};

export const api_ftpUpload = (packetZipName: string, remotePath: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/authorityPacket/ftpUpload", { packetZipName, remotePath });
};

export const api_saveTemplate = (packetCommonInfo: PacketCommonInfo) => {
  return http.post(SERVICE_INTEGRATED_TESTER + "/authorityPacket/saveCommonInfo", packetCommonInfo);
};

export const api_getCommonInfo = (packetCommonInfo: PacketCommonInfo) => {
  return http.post<PacketCommonInfo[]>(SERVICE_INTEGRATED_TESTER + "/authorityPacket/getCommonInfo", packetCommonInfo);
};

export const api_editCommonInfo = (packetCommonInfo: PacketCommonInfo) => {
  return http.post<PacketCommonInfo[]>(SERVICE_INTEGRATED_TESTER + "/authorityPacket/editCommonInfo", packetCommonInfo);
};

export const api_deleteCommonInfo = (id: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/authorityPacket/deleteCommonInfo", { id });
};
