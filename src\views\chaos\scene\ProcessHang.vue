<template>
  <div class="process-hang-injection">
    <div v-loading="injectionLoading">
      <div class="card-header">
        <div class="title-area">
          <el-icon class="icon"><Timer /></el-icon>
          <span class="title">进程挂起故障注入</span>
        </div>
      </div>
      
      <el-divider class="divider" />
      
      <div class="description">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title>此操作将暂停指定进程的运行，可能导致相关服务停止响应</template>
        </el-alert>
      </div>
      
      <el-form :model="processHangInfo" :rules="rules" ref="formRef" label-position="top" class="injection-form">
        <div class="form-row">
          <el-form-item label="目标进程名称" prop="processName">
            <el-input 
              v-model="processHangInfo.processName" 
              placeholder="输入进程名称，例如: java、nginx"
            >
              <template #prefix>
                <el-icon><Monitor /></el-icon>
              </template>
            </el-input>
            <div class="process-hint">
              进程名称通常是可执行文件的名称，不包括路径
            </div>
          </el-form-item>
        </div>
        
        <div class="form-row">
          <el-form-item label="持续时长" prop="timeout">
            <div class="duration-input">
              <el-input-number 
                v-model="processHangInfo.timeout" 
                :min="10" 
                :max="3600" 
                controls-position="right"
              />
              <span class="unit">秒</span>
            </div>
            <div class="timeout-hint">
              注意：较长的挂起时间可能导致不可恢复的服务中断
            </div>
          </el-form-item>
        </div>
        
        <div class="form-actions">
          <el-button @click="closeDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleProcessHang" 
            :loading="injectionLoading" 
            :disabled="injectionLoading"
          >
            <el-icon><Warning /></el-icon>
            注入故障
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  api_injection,
  ChaosReqtDto,
} from "@/api/modules/service-server-operation/chaos";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { Timer, Monitor, Warning } from "@element-plus/icons-vue";

// 定义 props：接收父组件传入的服务器 ID
const props = defineProps<{ serverId: string }>();
const emit = defineEmits(["injectSuccess", "closeDialog"]);
const chaosType = "process-hang";
const formRef = ref<FormInstance>();

// 注入表单数据和状态
const processHangInfo = ref({
  serverInfoId: props.serverId,
  processName: "",
  timeout: 180
});

// 表单验证规则
const rules = ref<FormRules>({
  processName: [
    { required: true, message: "请输入目标进程名称", trigger: "blur" },
    { min: 1, max: 100, message: "进程名称长度应在1-100个字符之间", trigger: "blur" }
  ],
  timeout: [
    { required: true, message: "请设置持续时长", trigger: "change" },
    { type: "number", min: 10, max: 3600, message: "时长必须在10-3600秒之间", trigger: "change" }
  ]
});

const injectionLoading = ref(false);

// 关闭对话框
const closeDialog = () => {
  emit("closeDialog");
};

// 执行进程挂起注入
const handleProcessHang = async function () {
  if (!formRef.value) return;
  
  await formRef.value.validate(async valid => {
    if (!valid) {
      return;
    }
    
    // 对敏感操作给予特别警告
    const confirmed = await ElMessageBox.confirm(
      `您确定要暂停 "${processHangInfo.value.processName}" 进程吗？这可能导致相关服务不可用。`,
      '操作确认',
      {
        confirmButtonText: '确定注入',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => false);
    
    if (!confirmed) return;
    
    injectionLoading.value = true;
    
    const chaosReqtDto: ChaosReqtDto = {
      serverInfoId: processHangInfo.value.serverInfoId,
      chaosType,
      chaosCommandPrefix: "create process stop",
      paramMap: {
        "--process": processHangInfo.value.processName,
        "--timeout": processHangInfo.value.timeout
      },
      targetInfo: `进程暂停: ${processHangInfo.value.processName}`
    };
    
    api_injection(chaosReqtDto)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("进程挂起故障注入成功");
          emit("injectSuccess");
          closeDialog();
        } else {
          ElMessage.error(res.respMsg);
        }
      })
      .finally(() => {
        injectionLoading.value = false;
      });
  });
};

// 初始化组件
onMounted(() => {
  if (props.serverId) {
    processHangInfo.value.serverInfoId = props.serverId;
  }
});
</script>

<style scoped lang="scss">
.process-hang-injection {
  padding: 0 16px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    
    .title-area {
      display: flex;
      align-items: center;
      
      .icon {
        color: #e6a23c;
        font-size: 20px;
        margin-right: 8px;
      }
      
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .divider {
    margin: 12px 0;
  }
  
  .description {
    margin-bottom: 20px;
  }
  
  .injection-form {
    padding: 16px 0;
    
    .form-row {
      margin-bottom: 20px;
    }
    
    .process-hint, .timeout-hint {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
    
    .duration-input {
      display: flex;
      align-items: center;
      
      .unit {
        margin-left: 8px;
        color: #606266;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 30px;
    }
  }
}
</style>
