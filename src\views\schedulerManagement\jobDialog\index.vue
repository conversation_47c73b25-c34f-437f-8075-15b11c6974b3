<template>
  <el-dialog v-model="visible" :title="mode === 'add' ? '新增任务' : '编辑任务'" width="600px">
    <el-form ref="jobForm" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="触发器Key" prop="triggerName">
        <el-tag type="primary"> {{ formData.triggerGroup + "." + formData.triggerName }} </el-tag>
      </el-form-item>

      <el-form-item label="任务Key" prop="jobName">
        <el-tag type="primary"> {{ formData.jobGroup + "." + formData.jobName }}</el-tag>
      </el-form-item>

      <el-form-item label="任务类" prop="className">
        <el-input v-model="formData.className" placeholder="请输入完整类名" disabled />
      </el-form-item>

      <el-form-item label="Cron表达式" prop="cronExpression">
        <el-input v-model="formData.cronExpression" placeholder="请输入Cron表达式">
          <template #append>
            <el-button @click="showCronDialog = true">生成</el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="任务状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="NORMAL">正常</el-radio>
          <el-radio value="PAUSED">暂停</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="任务描述" prop="description">
        <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入任务描述" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确认</el-button>
    </template>

    <!-- Cron表达式生成器对话框 -->
    <el-dialog v-model="showCronDialog" title="Cron表达式生成器" width="700px" append-to-body>
      <div class="cron-generator">
        <!-- 这里可以集成一个Cron表达式生成器组件 -->
        <p>这里可以放置Cron表达式生成器UI</p>
        <el-button type="primary" @click="insertCron('0 0/1 * * * ?')">每分钟</el-button>
        <el-button type="primary" @click="insertCron('0 0 * * * ?')">每小时</el-button>
        <el-button type="primary" @click="insertCron('0 0 0 * * ?')">每天</el-button>
      </div>
      <template #footer>
        <el-button @click="showCronDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { api_updateTrigger, TriggerInfoDto } from "@/api/modules/scheduler/jobs";

const props = defineProps({
  modelValue: Boolean,
  currentJob: Object,
  mode: String // 'add' or 'edit'
});

const emit = defineEmits(["update:modelValue", "refresh"]);

const visible = ref(false);
const showCronDialog = ref(false);
const jobForm = ref(null);

const formData = ref<TriggerInfoDto>({
  triggerName: "",
  triggerGroup: "",
  jobName: "",
  jobGroup: "DEFAULT",
  className: "",
  cronExpression: "",
  status: "NORMAL",
  description: ""
});

const rules = {
  cronExpression: [{ required: true, message: "请输入Cron表达式", trigger: "blur" }]
};

// 监听props变化
watch(
  () => props.modelValue,
  val => {
    visible.value = val;
    if (val && props.mode === "edit" && props.currentJob) {
      formData.value = { ...props.currentJob };
    } else if (val && props.mode === "add") {
      formData.value = {
        jobName: "",
        jobGroup: "DEFAULT",
        className: "",
        cronExpression: "",
        status: "NORMAL",
        description: ""
      };
    }
  }
);

// 监听visible变化
watch(visible, val => {
  emit("update:modelValue", val);
});

// 提交表单
const submitForm = async () => {
  const triggerInfoDto: TriggerInfoDto = {
    triggerName: formData.value.triggerName,
    triggerGroup: formData.value.triggerGroup,
    jobName: formData.value.jobName,
    jobGroup: formData.value.jobGroup,
    className: formData.value.className,
    cronExpression: formData.value.cronExpression,
    status: formData.value.status,
    description: formData.value.description
  };
  api_updateTrigger(triggerInfoDto)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("信息更新成功");
        visible.value = false;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      emit("refresh");
    });
};

// 插入Cron表达式
const insertCron = cron => {
  formData.value.cronExpression = cron;
  showCronDialog.value = false;
};
</script>

<style scoped>
.cron-generator {
  padding: 10px;
}
</style>
