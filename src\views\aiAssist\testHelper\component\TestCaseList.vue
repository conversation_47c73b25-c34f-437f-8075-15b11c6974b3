<template>
  <div class="content-container">
    <!--内容头-->
    <div class="content-header fixed">
      <h3 class="content-title">测试案例-{{ props.selectedNode!.title }}</h3>
      <div class="action-buttons">
        <el-button :disabled="selectedTestCases.length === 0" type="danger" @click="handleBatchDelete" :loading="batchDeleting">
          批量删除({{ selectedTestCases.length }})
        </el-button>
        <el-button type="primary" @click="openGenerateDialog" :loading="generating">生成测试案例</el-button>
        <el-button @click="switchComponent">查看功能点</el-button>
      </div>
    </div>

    <!--内容体-->
    <div class="content-body">
      <el-table
        v-if="testCaseDetailList.length > 0"
        :data="testCaseDetailList"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="case_id" label="案例ID" width="180" />
        <el-table-column prop="case_name" label="案例名称" min-width="200" />
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">{{ row.priority }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="test_type" label="测试类型" width="120" />
        <el-table-column prop="pre_condition" label="前置条件" min-width="250" show-overflow-tooltip />
        <el-table-column label="锁定状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isLocked ? 'success' : 'danger'">
              {{ row.isLocked ? "已锁定" : "未锁定" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">查看详情</el-button>
            <el-button
              :type="row.isLocked === 1 ? 'warning' : 'success'"
              link
              @click="handleToggleLock(row)"
              :loading="lockingCaseId === row.id"
            >
              {{ row.isLocked === 1 ? "解锁" : "锁定" }}
            </el-button>
            <el-button type="danger" link @click="handleSingleDelete(row)" :loading="deletingIds.includes(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else description="暂无测试案例" />
    </div>

    <!-- 详情弹窗 -->
    <el-dialog v-model="dialogVisible" :title="currentCase?.case_name" width="60%" destroy-on-close>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="案例ID">{{ currentCase?.case_id }}</el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityType(currentCase?.priority)">{{ currentCase?.priority }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="测试类型">{{ currentCase?.test_type }}</el-descriptions-item>
        <el-descriptions-item label="前置条件">{{ currentCase?.pre_condition }}</el-descriptions-item>
        <el-descriptions-item label="测试步骤">
          <el-timeline>
            <el-timeline-item
              v-for="(step, index) in currentCase?.steps"
              :key="index"
              :timestamp="`步骤 ${index + 1}`"
              placement="top"
            >
              {{ step }}
            </el-timeline-item>
          </el-timeline>
        </el-descriptions-item>
        <el-descriptions-item label="预期结果">{{ currentCase?.expected_result }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 生成测试案例弹窗 -->
    <el-dialog v-model="generateDialogVisible" title="生成测试案例" width="50%" destroy-on-close>
      <el-form :model="generateForm" label-position="top">
        <el-form-item label="提示词（可选）">
          <el-input
            v-model="generateForm.prompt"
            type="textarea"
            :rows="4"
            placeholder="请输入提示词，用于指导测试案例生成（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleGenerateTestCase" :loading="generating">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import type { TestCaseDetail, TreeNode } from "@/api/modules/service-llm/demand-doc";
import {
  api_getTestCaseByFunctionId,
  api_changeTestCaseLock,
  api_deleteTestCase,
  api_generateTestCaseOne
} from "@/api/modules/service-llm/demand-doc";
import { onMounted, ref, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

interface TestCaseDetailTable extends TestCaseDetail {
  id: string;
  functionId: string;
  isLocked: number;
}

const props = defineProps<{
  selectedNode: TreeNode | null;
}>();

const emit = defineEmits(["switchComponent"]);
const testCaseDetailList = ref<TestCaseDetailTable[]>([]);
const dialogVisible = ref(false);
const currentCase = ref<TestCaseDetailTable | null>(null);
const lockingCaseId = ref<string>("");
const selectedTestCases = ref<TestCaseDetailTable[]>([]);
const deletingIds = ref<string[]>([]);
const batchDeleting = ref(false);
const isLoading = ref(false);
const generateDialogVisible = ref(false);
const generating = ref(false);
const generateForm = ref({
  prompt: ""
});

const switchComponent = () => {
  emit("switchComponent", "DemandDetail");
};

const getPriorityType = (priority: string | undefined): "danger" | "warning" | "info" | undefined => {
  if (!priority) return undefined;
  if (priority === "高") {
    return "danger";
  }
  if (priority === "中") {
    return "warning";
  }
  if (priority === "低") {
    return "info";
  }
};

const handleViewDetail = (row: TestCaseDetailTable) => {
  currentCase.value = row;
  dialogVisible.value = true;
};

const handleToggleLock = (row: TestCaseDetailTable) => {
  // 设置正在处理的ID
  lockingCaseId.value = row.id;

  // 调用API更改锁定状态
  api_changeTestCaseLock(row.id)
    .then(res => {
      if (res.respCode === 2000) {
        row.isLocked = row.isLocked === 1 ? 0 : 1;
        fetchTestCases();
        ElMessage.success(`${row.isLocked === 1 ? "锁定" : "解锁"}成功`);
      } else {
        ElMessage.error(`${row.isLocked === 1 ? "解锁" : "锁定"}失败: ${res.respMsg || "未知错误"}`);
      }
    })
    .catch(err => {
      ElMessage.error(`操作失败: ${err.message}`);
    })
    .finally(() => {
      // 清除正在处理的ID
      lockingCaseId.value = "";
    });
};

const fetchTestCases = async () => {
  if (!props.selectedNode?.function_id) {
    ElMessage.error("无法获取测试案例：未找到功能点ID");
    return;
  }

  isLoading.value = true;

  try {
    const res = await api_getTestCaseByFunctionId(props.selectedNode.function_id);
    if (res.respCode === 2000 && res.respData.data) {
      // 处理API响应格式
      const apiTestCases = res.respData.data;

      // 将每个测试案例的statusMeta解析为TestCase对象
      testCaseDetailList.value = apiTestCases.map(item => {
        const statusMeta = JSON.parse(item.statusMeta as string);
        return {
          id: item.id,
          functionId: item.functionId,
          isLocked: item.isLocked,
          case_id: statusMeta.case_id || "",
          case_name: statusMeta.case_name || "",
          priority: statusMeta.priority || "",
          test_type: statusMeta.test_type || "",
          pre_condition: statusMeta.pre_condition || "",
          steps: statusMeta.steps || [],
          expected_result: statusMeta.expected_result || "",
          actual_result: statusMeta.actual_result || ""
        } as TestCaseDetailTable;
      });
    } else {
      ElMessage.warning(res.respData?.status || "暂无测试案例");
      testCaseDetailList.value = [];
    }
  } catch (error: any) {
    ElMessage.error(`获取测试案例失败: ${error.message || "未知错误"}`);
    testCaseDetailList.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 处理选择变化
const handleSelectionChange = (selection: TestCaseDetailTable[]) => {
  selectedTestCases.value = selection;
};

// 处理单个删除
const handleSingleDelete = (row: TestCaseDetailTable) => {
  // 检查测试案例是否锁定
  if (row.isLocked === 1) {
    ElMessage.warning("无法删除已锁定的测试案例，请先解锁后再删除");
    return;
  }

  ElMessageBox.confirm(`确定要删除测试案例"${row.case_name}"吗？此操作不可逆。`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deletingIds.value.push(row.id);
    api_deleteTestCase([row.id])
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("删除成功");
          fetchTestCases();
        } else {
          ElMessage.error(`删除失败: ${res.respMsg || "未知错误"}`);
        }
      })
      .catch(err => {
        ElMessage.error(`操作失败: ${err.message}`);
      })
      .finally(() => {
        const index = deletingIds.value.indexOf(row.id);
        if (index !== -1) {
          deletingIds.value.splice(index, 1);
        }
      });
  });
};

// 处理批量删除
const handleBatchDelete = () => {
  // 检查是否有锁定的测试案例
  const lockedCases = selectedTestCases.value.filter(item => item.isLocked === 1);
  if (lockedCases.length > 0) {
    const lockedCaseIds = lockedCases.map(item => item.case_id).join("、");
    ElMessage.warning(`无法删除已锁定的测试案例，请先解锁以下案例后再删除：\n${lockedCaseIds}`);
    return;
  }

  const selectedIds = selectedTestCases.value.map(item => item.id);
  const selectedCaseIds = selectedTestCases.value.map(item => item.case_id).join("、");

  ElMessageBox.confirm(
    `确定要删除选定的${selectedTestCases.value.length}个测试案例吗？此操作不可逆。\n${selectedCaseIds}`,
    "批量删除警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    batchDeleting.value = true;
    api_deleteTestCase(selectedIds)
      .then(res => {
        if (res.respCode === 2000) {
          ElMessage.success(`成功删除${selectedTestCases.value.length}个测试案例`);
          // 清空选择
          selectedTestCases.value = [];
        } else {
          ElMessage.error(`批量删除失败: ${res.respMsg || "未知错误"}`);
        }
      })
      .catch(err => {
        ElMessage.error(`操作失败: ${err.message}`);
      })
      .finally(() => {
        fetchTestCases();
        batchDeleting.value = false;
      });
  });
};

// 打开生成测试案例弹窗
const openGenerateDialog = () => {
  if (!props.selectedNode?.function_id) {
    ElMessage.error("无法生成测试案例：未找到功能点ID");
    return;
  }
  generateForm.value.prompt = "";
  generateDialogVisible.value = true;
};

// 处理生成测试案例
const handleGenerateTestCase = () => {
  if (!props.selectedNode?.function_id) {
    ElMessage.error("无法生成测试案例：未找到功能点ID");
    return;
  }

  generating.value = true;
  api_generateTestCaseOne(props.selectedNode.function_id, generateForm.value.prompt || "")
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("测试案例生成成功");
        generateDialogVisible.value = false;
        fetchTestCases(); // 刷新测试案例列表
      } else {
        ElMessage.error(`生成测试案例失败: ${res.respMsg || "未知错误"}`);
      }
    })
    .catch(err => {
      ElMessage.error(`操作失败: ${err.message}`);
    })
    .finally(() => {
      generating.value = false;
    });
};

// 添加对 selectedNode 的监听

watch(
  () => props.selectedNode,
  (newNode, oldNode) => {
    // 检查是否是真正的节点变更（避免相同节点的重复请求）
    if (newNode?.id !== oldNode?.id && newNode?.function_id) {
      fetchTestCases();
    }
  },
  { immediate: true } // 立即执行一次
);

onMounted(() => {
  // 由于我们添加了 immediate: true 的 watcher，这里可以不需要再调用 fetchTestCases
  // 但为了保持兼容性，可以保留这个调用
  // fetchTestCases();
});
</script>

<style lang="scss" scoped>
.content-container {
  flex: 1;
  padding-left: 20px;
  overflow-y: auto;
  height: 100%;
  position: relative;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    background-color: #fff;

    &.fixed {
      position: sticky;
      top: 0;
      z-index: 999;
      padding: 15px 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      background: linear-gradient(to right, #ffffff, #f8f9fa);
    }

    .content-title {
      font-size: 20px;
      color: #303133;
      margin: 0;
      font-weight: 600;
      position: relative;
      padding-left: 12px;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #409eff, #67c23a);
        border-radius: 2px;
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;

      .el-button {
        padding: 8px 20px;
        font-weight: 500;
      }
    }
  }

  .content-body {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    :deep(.el-table) {
      --el-table-border-color: #ebeef5;
      --el-table-header-bg-color: #f5f7fa;
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
