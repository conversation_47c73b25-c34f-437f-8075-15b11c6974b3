<template>
  <div class="card filter">
    <h4 class="title sle">
      {{ title }}
    </h4>
    <el-input v-model="filterText" placeholder="输入关键字进行过滤" clearable />
    <el-scrollbar :style="{ height: title ? `calc(100% - 95px)` : `calc(100% - 56px)` }">
      <el-tree
        ref="treeRef"
        default-expand-all
        :node-key="id"
        :data="treeData"
        :show-checkbox="false"
        :check-strictly="false"
        :current-node-key="defaultSelected"
        :highlight-current="true"
        :expand-on-click-node="false"
        :check-on-click-node="false"
        :props="defaultProps"
        :filter-node-method="filterNode"
        @current-change="handleNodeChange"
        @node-click="handleNodeClick"
      >
        <template #default="scope">
          <el-tooltip placement="top-start" :content="scope.node.label" :show-after="500">
            <span class="el-tree-node__label">
              <slot :row="scope">{{ scope.node.label }}</slot>
            </span>
          </el-tooltip>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, watch } from "vue";
import { ElTree } from "element-plus";
import { ResultData } from "@/api/interface";

// 从父组件接收参数
interface TreeFilterProps {
  requestApi?: (data?: any) => Promise<ResultData>; // 请求分类数据的 api ==> 非必传
  data?: { [key: string]: any }[]; // 分类数据，如果有分类数据，则不会执行 api 请求 ==> 非必传
  title?: string; // treeFilter 标题 ==> 非必传
  id?: string; // 选择的id ==> 非必传，默认为 “id”
  label?: string; // 显示的label ==> 非必传，默认为 “label”
  defaultValue?: any; // 默认选中的值 ==> 非必传
}
const props = withDefaults(defineProps<TreeFilterProps>(), {
  id: "id",
  label: "label"
});

// 设置初始选中的节点的函数
const defaultSelected = ref();
const setDefaultSelected = () => {
  defaultSelected.value = typeof props.defaultValue === "string" ? props.defaultValue : "";
};

const treeData = ref<{ [key: string]: any }[]>([]);
// 组件挂载前钩子函数
onBeforeMount(async () => {
  setDefaultSelected();
  if (props.requestApi) {
    props.requestApi().then(res => {
      treeData.value = res.respData;
    });
  }
});

// 监听filterText，filterText有修改则触发watch，调用filter方法;
const treeRef = ref<InstanceType<typeof ElTree>>(); //与el-tree绑定，对应el-tree属性ref="treeRef"
const filterText = ref("");
watch(filterText, val => {
  treeRef.value!.filter(val);
});
// filter实际调用该函数
const filterNode = (value: string, data: { [key: string]: any }, node: any) => {
  if (!value) return true;
  let parentNode = node.parent,
    labels = [node.label],
    level = 1;
  while (level < node.level) {
    labels = [...labels, parentNode.label];
    parentNode = parentNode.parent;
    level++;
  }
  return labels.some(label => label.indexOf(value) !== -1);
};

// 设置选中的节点
function setCurrentKey(key: string | undefined) {
  treeRef.value!.setCurrentKey(key);
}
defineExpose({
  setCurrentKey
});

// 设置tree的配置
const defaultProps = {
  children: "children",
  label: props.label
};

// 监听传入的data
watch(
  () => props.data,
  () => {
    if (props.data) {
      treeData.value = props.data;
    }
  },
  { deep: true, immediate: true }
);

// emit，调用父类函数
const emit = defineEmits<{
  nodeChange: [value: any];
  nodeClick: [value: any];
}>();
// 点击切换节点的回调函数
const handleNodeChange = (value: { [key: string]: any } | null) => {
  if (value === null) {
    return;
  }
  emit("nodeChange", value);
};

const handleNodeClick = (value: { [key: string]: any }) => {
  emit("nodeClick", value);
};
</script>

<style lang="scss" scoped>
@import "./index";
</style>
