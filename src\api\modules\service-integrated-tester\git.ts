import http from "@/api";
import {SERVICE_INTEGRATED_TESTER} from "@/api/config/servicePort";

export interface GitRepoInfo {
  id: string;
  remoteUrl: string;
  localPath: string;
  repositoryName: string;
  branchName: string;
  scheduled: string;
  updateTime: string;
  repositoryComment: string;
  credentialId: string;
  updateStatus: string;
}

export interface GitCredential {
  id: string;
  name: string;
  description: string;
  username: string;
  password: string;
}

export const api_getRepositories = (gitRepoInfo?: GitRepoInfo) => {
  return http.post<GitRepoInfo[]>(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/repositories", gitRepoInfo);
}

export const api_updateGitRepoInfo = (gitRepoInfo: GitRepoInfo) => {
  return http.post(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/repo/update", gitRepoInfo);
}

export const api_addGitRepoInfo = (gitRepoInfo: GitRepoInfo) => {
  return http.post(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/repo/add", gitRepoInfo);
}

export const api_deleteGitRepo = (id: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/repo/delete/" + id);
}

export const api_pullOrClone = (id: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/pullOrClone/" + id);
}

export const api_getCredentials = (gitCredential?: GitCredential) => {
  return http.post<GitCredential[]>(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/credentials", gitCredential);
}

export const api_addCredential = (gitCredential: GitCredential) => {
  return http.post(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/credential/add", gitCredential);
}

export const api_deleteCredential = (id: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/credential/delete/" + id);
}

export const api_updateCredential = (gitCredential: GitCredential) => {
  return http.post(SERVICE_INTEGRATED_TESTER + "/projectManagement/git/credential/update", gitCredential);
}
