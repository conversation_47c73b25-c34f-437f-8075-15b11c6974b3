<template>
  <div class="card tabs">
    <h4 class="title sle">调用信息</h4>
    <el-tabs v-model="activeName">
      <el-tab-pane label="图像" name="first">
        <GraphTab :graph-data="props.graphData" />
      </el-tab-pane>
      <el-tab-pane label="表格" name="second">
        <TableTab :table-data="props.tableData" @open-drawer="openDrawer" />
      </el-tab-pane>
    </el-tabs>

    <KeyMethodsDrawer ref="keyMethodsDrawer" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import GraphTab from "@/views/esbSystemLink/components/graphTab/index.vue";
import TableTab from "@/views/esbSystemLink/components/tableTab/index.vue";
import KeyMethodsDrawer from "@/views/esbSystemLink/components/keyMethodsDrawer/index.vue";
import { TreeFilterNode } from "@/api/interface";

const keyMethodsDrawer = ref<InstanceType<typeof KeyMethodsDrawer>>();
function openDrawer(title: string, cardData: string[]) {
  keyMethodsDrawer.value!.setDrawerParams({
    title,
    cardData
  });
}
// 当前选择
const activeName = ref("first");

// 传入参数
const props = defineProps<{
  configId: string | undefined;
  interfaceId: string | undefined;
  serviceProvider: string | undefined;
  graphData: TreeFilterNode;
  tableData: TreeFilterNode[];
}>();

// const tableData = ref<TreeFilterNode | undefined>();

// 暴露变量
defineExpose({ activeName });
</script>

<style scoped lang="scss">
@import "./index";
</style>
