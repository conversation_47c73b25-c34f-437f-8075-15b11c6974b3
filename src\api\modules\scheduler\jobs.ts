import http from "@/api";
import { SCHEDULER } from "@/api/config/servicePort";

export interface TriggerInfoDto {
  triggerName?: string;
  triggerGroup?: string;
  jobName?: string;
  jobGroup?: string;
  className?: string;
  cronExpression?: string;
  status?: string;
  description?: string;
  prevFireTime?: string;
  nextFireTime?: string;
}

export const api_getJobsByCondition = (triggerInfoDto: TriggerInfoDto) => {
  return http.post<TriggerInfoDto[]>(SCHEDULER + "/jobs/list", triggerInfoDto);
};

export const api_updateTrigger = (triggerInfoDto: TriggerInfoDto) => {
  return http.post(SCHEDULER + "/jobs/update", triggerInfoDto);
};

export const api_updateTriggerStatus = (triggerKey: string, status: string) => {
  return http.get(SCHEDULER + "/jobs/status", { triggerKey, status });
};
