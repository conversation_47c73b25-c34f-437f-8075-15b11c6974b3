<template>
  <div class="container">
    <div class="card">
      <el-table :data="replayPlanTable.data" border>
        <el-table-column label="回放计划ID" prop="id" />
        <el-table-column label="回放计划名称" prop="planName" />
        <el-table-column label="创建时间" prop="createTime" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="primary" size="small" @click="changeToInfo(scope.row.id)">查看</el-button>
            <el-button type="primary" size="small" @click="deletePlan(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onBeforeMount, ref} from "vue";
import {api_deleteReplayPlan, api_getReplayPlan, EsbReplayPlan} from "@/api/modules/esbNetworkReplay/replay";
import {ElMessage} from "element-plus";

interface ReplayPlanTable {
  data: EsbReplayPlan[]
}

export interface TransParams {
  selectedReplayPlanId: string;
}

const emits = defineEmits(['changeComponent']);

const replayPlanTable = ref<ReplayPlanTable>({
  data: []
});

const queryReplayPlan = () => {
  api_getReplayPlan().then(res => {
    if (res.respCode === 2000) {
      replayPlanTable.value.data = res.respData;
    }
  });
};

const changeToInfo = function (planId: string) {
  changeComponent('ReplayInfo', {selectedReplayPlanId: planId});
};

const deletePlan = function (planId: string) {
  api_deleteReplayPlan(planId).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success('删除成功');
      queryReplayPlan();
    }
  });
};

const changeComponent = function (componentName: string, transParams: TransParams) {
  emits('changeComponent', componentName, transParams);
}

onBeforeMount(() => {
  queryReplayPlan();
})
</script>

<style scoped lang="scss">

</style>
