<template>
  <div class="chaos-server-management">
    <!-- 顶部统计卡片 -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ serverCount }}</div>
          <div class="stat-label">目标主机</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon warning">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ executingTasks.length }}</div>
          <div class="stat-label">正在执行故障</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon success">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ tableData.filter(item => item.onlineStatus === 1).length || 0 }}</div>
          <div class="stat-label">在线主机</div>
        </div>
      </div>
    </div>

    <!-- 操作区域 -->
    <div class="action-header">
      <div class="section-title">
        <h2>目标主机管理</h2>
        <span class="subtitle">管理故障注入的目标服务器</span>
      </div>
      <div class="actions">
        <el-button
          type="primary"
          class="add-server-btn"
          @click="
            () => {
              editServerDialog.visible = true;
              editServerDialog.title = '新增目标主机';
              resetEditServerDialog();
            }
          "
        >
          <el-icon><Plus /></el-icon>
          新增目标主机
        </el-button>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索主机名称"
          class="search-input"
          clearable
          @clear="fetchChaosServerList"
          @keyup.enter="fetchChaosServerList"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 主机表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        row-class-name="table-row"
        :highlight-current-row="true"
        style="width: 100%"
      >
        <el-table-column label="主机名称" prop="serverName" min-width="160">
          <template #default="scope">
            <div class="server-name-cell">
              <el-icon><Monitor /></el-icon>
              <span>{{ scope.row.serverName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="环境" prop="environment" min-width="100">
          <template #default="scope">
            <el-tag size="small" effect="plain">{{ scope.row.environment }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="IP" prop="serverIp" min-width="140"></el-table-column>
        <el-table-column label="用户名" prop="username" min-width="120"></el-table-column>
        <el-table-column label="状态" min-width="120" width="">
          <template #default="scope">
            <div class="status-cell">
              <el-tag :type="getServerStatusType(scope.row)" effect="light" size="small" class="status-tag">
                {{ getServerStatusText(scope.row) }}
              </el-tag>
              <el-button type="primary" link size="small" @click="checkServerStatus(scope.row)" :loading="scope.row.checking">
                <el-icon><RefreshRight /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button
                size="small"
                type="success"
                text
                @click="deployScript(scope.row.id)"
                :loading="scope.row.id === deployingServerId"
              >
                <el-icon><Upload /></el-icon>
                脚本部署
              </el-button>
              <el-popconfirm
                title="确定要删除该主机吗？"
                confirm-button-text="删除"
                cancel-button-text="取消"
                @confirm="deleteServerInfo(scope.row.id)"
              >
                <template #reference>
                  <el-button size="small" type="danger" text>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="故障注入" width="220">
          <template #default="scope">
            <el-dropdown @command="command => handleChaosCommand(command, scope.row.id)">
              <el-button size="small" type="warning" plain class="chaos-btn">
                注入故障<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="cpu-full">
                    <div class="dropdown-item-content">
                      <el-icon><Cpu /></el-icon>
                      <span>CPU满载</span>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="disk-burn">
                    <div class="dropdown-item-content">
                      <el-icon><Files /></el-icon>
                      <span>磁盘IO</span>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="disk-fill">
                    <div class="dropdown-item-content">
                      <el-icon><Folder /></el-icon>
                      <span>磁盘填充</span>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="network-delay">
                    <div class="dropdown-item-content">
                      <el-icon><Connection /></el-icon>
                      <span>网络延迟</span>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="network-loss">
                    <div class="dropdown-item-content">
                      <el-icon><Close /></el-icon>
                      <span>网络丢包</span>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="process-hang">
                    <div class="dropdown-item-content">
                      <el-icon><Timer /></el-icon>
                      <span>进程挂起</span>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pageInfo.pageNo"
          v-model:page-size="pageInfo.pageSize"
          layout="prev, pager, next"
          :total="pageInfo.pageCount"
          @size-change="fetchChaosServerList"
          @current-change="fetchChaosServerList"
          background
        >
        </el-pagination>
      </div>
    </div>

    <!-- 正在执行的故障表格 -->
    <div class="active-faults-section">
      <div class="section-header">
        <div class="section-title">
          <h2>当前执行中的混沌故障</h2>
          <span class="subtitle">实时监控正在执行的故障注入任务</span>
        </div>
        <el-button type="primary" @click="refreshExecutingTasks" :loading="executingTasksLoading" plain>
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>

      <div class="table-container">
        <el-table :data="executingTasks" v-loading="executingTasksLoading" row-class-name="table-row" style="width: 100%">
          <el-table-column label="服务器IP" prop="serverIp" min-width="140"></el-table-column>
          <el-table-column label="故障类型" prop="desc" min-width="130">
            <template #default="scope">
              <el-tag :type="getFaultTypeTag(scope.row.chaosType) || 'info'" effect="dark">
                {{ scope.row.chaosType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="故障信息" prop="targetInfo" min-width="180"></el-table-column>
          <el-table-column label="开始时间" min-width="170">
            <template #default="scope">
              <div class="time-cell">
                <el-icon><Calendar /></el-icon>
                <span>{{ scope.row.startTimeString }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="预计结束时间" min-width="170">
            <template #default="scope">
              <div class="time-cell">
                <el-icon><Timer /></el-icon>
                <span>{{ scope.row.estEndTimeString }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="持续时长" min-width="110">
            <template #default="scope">
              <el-tag type="info" effect="plain">{{ scope.row.duration }} 秒</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="剩余时间" min-width="110">
            <template #default="scope">
              <div class="remaining-time">
                <el-progress
                  :percentage="getTimeProgressPercentage(scope.row)"
                  :status="getTimeProgressStatus(scope.row)"
                  :stroke-width="5"
                ></el-progress>
                <span>{{ scope.row.secondsRemain }} 秒</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-popconfirm
                title="确定要终止该故障注入任务吗?"
                confirm-button-text="终止"
                cancel-button-text="取消"
                confirm-button-type="danger"
                @confirm="terminateTask(scope.row.id)"
              >
                <template #reference>
                  <el-button type="danger" size="small" plain>
                    <el-icon><CircleClose /></el-icon>
                    终止
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 目标主机编辑对话框 -->
    <el-drawer
      v-model="editServerDialog.visible"
      :title="editServerDialog.title"
      size="55%"
      direction="rtl"
      :show-close="true"
      destroy-on-close
    >
      <div class="drawer-content">
        <div>
          <div class="server-select-header">
            <el-input
              v-model="editServerDialog.searchKeyword"
              placeholder="搜索服务器"
              clearable
              prefix-icon="Search"
              @keyup.enter="searchAvailableServers"
              @clear="searchAvailableServers"
            ></el-input>
          </div>
          <div class="server-select-list" v-loading="editServerDialog.loading">
            <el-table :data="availableServers" style="width: 100%" highlight-current-row>
              <el-table-column prop="serverName" label="服务器名称" min-width="140">
                <template #default="scope">
                  <div class="server-name-cell">
                    <el-icon><Monitor /></el-icon>
                    <span>{{ scope.row.serverName }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="serverIp" label="IP地址" min-width="120"></el-table-column>
              <el-table-column prop="username" label="用户名" min-width="100"></el-table-column>
              <el-table-column prop="environment" label="环境" min-width="80">
                <template #default="scope">
                  <el-tag size="small" effect="plain">{{ scope.row.environment || "未设置" }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column width="100">
                <template #default="scope">
                  <el-button type="primary" link @click="addServer(scope.row)" :loading="scope.row.adding"> 添加 </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="editServerDialog.pageInfo.pageNo"
                v-model:page-size="editServerDialog.pageInfo.pageSize"
                layout="prev, pager, next"
                :total="editServerDialog.pageInfo.total"
                @size-change="searchAvailableServers"
                @current-change="searchAvailableServers"
                background
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 故障注入对话框 -->
    <el-dialog v-model="chaosDialog.visible" :title="chaosDialog.title" width="65%" destroy-on-close top="5vh">
      <component
        :is="chaosDialog.component"
        v-if="chaosDialog.visible"
        :server-id="chaosDialog.serverId"
        @inject-success="handleInjectSuccess"
        @close-dialog="chaosDialog.visible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { api_getByPageInfo, ServerInfo } from "@/api/modules/service-server-operation/server-management";
import {
  api_addChaosServer,
  api_checkStatus,
  api_deleteById,
  api_destroy,
  api_getChaosServerList,
  api_getExecutingList,
  api_setup,
  ChaosServerDto
} from "@/api/modules/service-server-operation/chaos";
import { onMounted, ref, shallowRef } from "vue";
import { ElMessage } from "element-plus";
import {
  ArrowDown,
  Calendar,
  CircleClose,
  Close,
  Connection,
  Cpu,
  Delete,
  Files,
  Folder,
  Monitor,
  Plus,
  Refresh,
  RefreshRight,
  Search,
  Timer,
  Upload,
  Warning
} from "@element-plus/icons-vue";
// 新增故障场景组件导入
import CpuFull from "./scene/CpuFull.vue";
import DiskBurn from "./scene/DiskBurn.vue";
import DiskFill from "./scene/DiskFill.vue";
import NetworkDelay from "./scene/NetworkDelay.vue";
import NetworkLoss from "./scene/NetworkLoss.vue";
import ProcessHang from "./scene/ProcessHang.vue";

const searchKeyword = ref("");
const serverCount = ref(0);

// 扩展 ServerInfo 类型
interface ServerInfoExtended extends ServerInfo {
  onlineStatus?: number;
  checking?: boolean;
}

interface ServerInfoTable extends ServerInfo {
  adding: boolean;
}

const tableData = ref<ServerInfoExtended[]>([]);
const tableLoading = ref(false);
const deployingServerId = ref<string | null>(null);
const pageInfo = ref({
  pageNo: 1,
  pageSize: 10,
  pageCount: 0
});

const editServerDialog = ref({
  visible: false,
  title: "",
  id: "",
  serverName: "",
  environment: "",
  serverIp: "",
  username: "",
  pw: "",
  selectMode: true,
  searchKeyword: "",
  loading: false,
  pageInfo: {
    pageNo: 1,
    pageSize: 10,
    total: 0
  }
});

const chaosDialog = ref({
  visible: false,
  title: "",
  component: shallowRef(),
  serverId: ""
});

const executingTasks = ref<any[]>([]);
const executingTasksLoading = ref(false);

const availableServers = ref<ServerInfoTable[]>([]);

const fetchChaosServerList = function () {
  tableLoading.value = true;
  const params: ChaosServerDto = {
    serverName: searchKeyword.value
  };

  api_getChaosServerList(pageInfo.value.pageNo, pageInfo.value.pageSize, params).then(res => {
    if (res.respCode === 2000) {
      // 为每一行添加状态属性
      tableData.value = res.respData.pageData.map((server: ChaosServerDto) => {
        return {
          ...server,
          onlineStatus: server.agentStatus,
          checking: false
        } as ServerInfoExtended;
      });
      pageInfo.value.pageCount = res.respData.totalCount;
      serverCount.value = res.respData.totalCount;
    } else {
      ElMessage.error(res.respMsg);
    }
    tableLoading.value = false;
  });
};

const resetEditServerDialog = function () {
  editServerDialog.value.id = "";
  editServerDialog.value.serverName = "";
  editServerDialog.value.environment = "Q2";
  editServerDialog.value.serverIp = "";
  editServerDialog.value.username = "";
  editServerDialog.value.pw = "";
  editServerDialog.value.selectMode = true;
  editServerDialog.value.searchKeyword = "";
  editServerDialog.value.pageInfo.pageNo = 1;
  searchAvailableServers();
};

const searchAvailableServers = function () {
  editServerDialog.value.loading = true;
  api_getByPageInfo(editServerDialog.value.pageInfo.pageNo, editServerDialog.value.pageInfo.pageSize, {
    serverName: editServerDialog.value.searchKeyword
  }).then(res => {
    if (res.respCode === 2000) {
      availableServers.value = res.respData.pageData.map(server => {
        return {
          ...server,
          adding: false
        };
      });
      editServerDialog.value.pageInfo.total = res.respData.totalCount;
    } else {
      ElMessage.error(res.respMsg);
    }
    editServerDialog.value.loading = false;
  });
};

const addServer = async function (row: ServerInfoTable) {
  row.adding = true;

  await api_addChaosServer(row.id!)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success(`服务器 ${row.serverName} 已添加为混沌测试服务器`);
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      row.adding = false;
    });

  fetchChaosServerList();
};

const deployScript = async function (serverId: string) {
  deployingServerId.value = serverId;
  await api_setup(serverId)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("部署成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      deployingServerId.value = null;
    });
};

const deleteServerInfo = function (id: string) {
  tableLoading.value = true;
  api_deleteById(id)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("删除成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      tableLoading.value = false;
      fetchChaosServerList();
    });
};

const handleChaosCommand = function (command: string, serverId: string) {
  // 场景组件及标题映射
  const chaosMapping: Record<string, { component: any; title: string }> = {
    "cpu-full": { component: CpuFull, title: "CPU满载" },
    "disk-burn": { component: DiskBurn, title: "磁盘IO" },
    "disk-fill": { component: DiskFill, title: "磁盘填充" },
    "network-delay": { component: NetworkDelay, title: "网络延迟" },
    "network-loss": { component: NetworkLoss, title: "网络丢包" },
    "process-hang": { component: ProcessHang, title: "进程挂起" }
  };
  const item = chaosMapping[command];
  if (item) {
    chaosDialog.value.title = item.title;
    chaosDialog.value.component = item.component;
    chaosDialog.value.serverId = serverId;
    chaosDialog.value.visible = true;
  }
};

const handleInjectSuccess = function () {
  chaosDialog.value.visible = false;
  refreshExecutingTasks();
};

const refreshExecutingTasks = function () {
  executingTasksLoading.value = true;
  api_getExecutingList()
    .then(res => {
      if (res.respCode === 2000) {
        executingTasks.value = res.respData;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      executingTasksLoading.value = false;
    });
};

const terminateTask = function (id: string) {
  executingTasksLoading.value = true;
  api_destroy(id)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success(`已终止任务`);
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      refreshExecutingTasks();
      executingTasksLoading.value = false;
    });
};

// 服务器状态相关函数
const checkServerStatus = function (server: ServerInfoExtended) {
  if (!server || !server.id) return;

  server.checking = true;

  api_checkStatus(server.id)
    .then(res => {
      if (res.respCode === 2000) {
        // 直接存储数值状态
        server.onlineStatus = res.respData;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      server.checking = false;
    });
};

const getServerStatusType = function (server: ServerInfoExtended): "success" | "danger" | "info" {
  if (server.onlineStatus === undefined || server.onlineStatus === -1) return "info";
  return server.onlineStatus === 1 ? "success" : "danger";
};

const getServerStatusText = function (server: ServerInfoExtended) {
  if (server.onlineStatus === undefined || server.onlineStatus === -1) {
    console.log(server, server.onlineStatus);
    return "未检测";
  }
  return server.onlineStatus === 1 ? "在线" : "离线";
};

// 故障类型标签映射
const getFaultTypeTag = (faultType: string): "success" | "warning" | "info" | "danger" | null => {
  const typeMap: Record<string, "success" | "warning" | "info" | "danger"> = {
    "cpu-full": "danger",
    "disk-burn": "warning",
    "disk-fill": "warning",
    "network-delay": "info",
    "network-loss": "info",
    "process-hang": "danger"
  };

  return typeMap[faultType] || null;
};

// 计算剩余时间百分比
const getTimeProgressPercentage = (row: any) => {
  if (!row.duration || !row.secondsRemain) return 0;
  const elapsed = row.duration - row.secondsRemain;
  return Math.min(Math.round((elapsed / row.duration) * 100), 100);
};

// 计算进度条状态
const getTimeProgressStatus = (row: any) => {
  if (!row.secondsRemain) return "success";
  if (row.secondsRemain < 30) return "success";
  if (row.secondsRemain < 120) return "warning";
  return ""; // 空字符串表示默认状态
};

onMounted(() => {
  fetchChaosServerList();
  refreshExecutingTasks();
});
</script>

<style scoped lang="scss">
.chaos-server-management {
  padding: 16px;

  // 顶部统计卡片
  .dashboard-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .stat-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background-color: #409eff;
        color: white;
        margin-right: 16px;

        :deep(svg) {
          width: 24px;
          height: 24px;
        }

        &.warning {
          background-color: #e6a23c;
        }

        &.success {
          background-color: #67c23a;
        }
      }

      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          line-height: 1.2;
        }

        .stat-label {
          color: #909399;
          font-size: 14px;
          margin-top: 4px;
        }
      }
    }
  }

  // 操作区域
  .action-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .section-title {
      h2 {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }

      .subtitle {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
        display: block;
      }
    }

    .actions {
      display: flex;
      gap: 12px;

      .add-server-btn {
        display: flex;
        align-items: center;

        :deep(.el-icon) {
          margin-right: 4px;
        }
      }

      .search-input {
        width: 240px;
      }
    }
  }

  // 表格容器
  .table-container {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .server-name-cell {
      display: flex;
      align-items: center;

      :deep(.el-icon) {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .time-cell {
      display: flex;
      align-items: center;

      :deep(.el-icon) {
        margin-right: 6px;
        color: #909399;
      }
    }

    .operation-buttons {
      display: flex;
      gap: 8px;
    }

    .chaos-btn {
      width: 100%;
    }

    .remaining-time {
      display: flex;
      flex-direction: column;
      gap: 4px;

      span {
        font-size: 12px;
        color: #606266;
      }
    }

    .status-cell {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .el-tag {
        flex: 0 0 auto;
        min-width: 60px;
        display: flex;
        justify-content: center;
      }

      .status-tag {
        padding: 0 8px;
      }

      .el-button {
        margin-left: 4px;
      }
    }
  }

  .pagination-container {
    padding: 16px 0 0;
    display: flex;
    justify-content: flex-end;
  }

  // 故障区域
  .active-faults-section {
    margin-top: 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
  }

  // 抽屉表单
  .drawer-content {
    padding: 16px;

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
    }

    .server-select-header {
      margin-bottom: 16px;
    }

    .server-select-list {
      height: calc(100vh - 180px);
      overflow-y: auto;
    }
  }
}

// 下拉菜单样式
:deep(.dropdown-item-content) {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    font-size: 16px;
  }
}

// 表格行样式
:deep(.table-row) {
  transition: all 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }
}
</style>
