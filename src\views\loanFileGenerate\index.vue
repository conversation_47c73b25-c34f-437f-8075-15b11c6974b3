<template>
  <div class="loan-document-container">
    <el-card class="main-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="title">网贷放还款文件生成工具</span>
        </div>
      </template>

      <!-- 配置区域 -->
      <div class="config-section">
        <el-card shadow="never" class="config-card">
          <!-- 数据库选择和维护 -->
          <div class="database-section">
            <el-form label-position="top">
              <el-form-item label="数据库配置">
                <div class="database-controls">
                  <el-select v-model="selectedDatabase" placeholder="请选择数据库" style="width: 220px" clearable filterable>
                    <el-option v-for="db in dbConnectionInfoList" :key="db.id" :label="db.dbName" :value="db.id!">
                      <span style="float: left">{{ db.dbName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ db.dbType }}</span>
                    </el-option>
                  </el-select>
                  <el-button type="primary" plain @click="showDbInfoDialog = true" icon="Setting"> 数据库维护</el-button>
                </div>
              </el-form-item>

              <!-- 步骤1：文件类型选择 -->
              <el-form-item label="文件类型" v-if="currentStep >= 1">
                <el-radio-group v-model="selectedFileType" @change="handleFileTypeChange" id="file_type">
                  <el-radio-button v-for="item in FILE_TYPES" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </div>

      <!-- 步骤2：动态组件展示 -->
      <div class="loan-table-section" v-if="currentStep >= 2">
        <component
          :is="fileTypeComponent"
          :selected-db-connection-info-id="selectedDatabase"
          ref="currComponentRef"
        />
      </div>

      <!-- 生成和下载按钮 -->
      <div class="action-buttons" v-if="currentStep >= 2">
        <el-button type="primary" size="large" @click="writeCsv" :loading="generating" icon="MagicStick"> 生成文件</el-button>
        <el-button type="success" size="large" @click="downloadDocuments" icon="Download"> 下载文件</el-button>
      </div>
    </el-card>

    <!-- 数据库维护对话框 -->
    <DbInfoDialog
      v-model="showDbInfoDialog"
      :db-connection-info-list="dbConnectionInfoList"
      @fetch-list="fetchDbConnectionInfo"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, shallowRef } from "vue";
import { ElMessage } from "element-plus";
import DbInfoDialog from "./components/DbInfoDialog.vue";
import { api_getDbConnectionInfo, DbConnectionInfo } from "@/api/modules/service-integrated-tester/db-management";
import ByteDance from "@/views/loanFileGenerate/components/ByteDance.vue";

interface DynamicComponent {
  download: () => void;
  writeCsv: () => void;
}

const currComponentRef = shallowRef<DynamicComponent>();
const currentStep = ref(1); // 当前步骤，1:文件类型, 2:展示组件

const FILE_TYPES = [
  { value: "BQD", label: "行内接口标准" },
  { value: "ByteDance", label: "字节标准" }
];

// 文件类型选择
const selectedFileType = ref();

const fileTypeComponent = computed(() => {
  if (!selectedFileType.value) return null;

  // 使用对象映射更清晰
  const componentMap = {
    [FILE_TYPES[1].value]: ByteDance
  };

  return componentMap[selectedFileType.value] || null;
});

const handleFileTypeChange = () => {
  if (selectedFileType.value) {
    currentStep.value = 2; // 进入下一步：展示组件
  }
};

// 数据库相关
const selectedDatabase = ref();
const showDbInfoDialog = ref(false);
const dbConnectionInfoList = ref<DbConnectionInfo[]>([]);
const generating = ref(false);

const fetchDbConnectionInfo = function () {
  api_getDbConnectionInfo().then(res => {
    if (res.respCode === 2000) {
      dbConnectionInfoList.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

// 生成文件
const writeCsv = () => {
  if (!selectedDatabase.value) {
    ElMessage.warning("请选择数据库");
    return;
  }

  if (!selectedFileType.value) {
    ElMessage.warning("请选择文件类型");
    return;
  }

  currComponentRef.value!.writeCsv();
};

// 下载文件
const downloadDocuments = () => {
  currComponentRef.value!.download();
};

onMounted(() => {
  fetchDbConnectionInfo();
});
</script>

<style scoped lang="scss">
.loan-document-container {
  background-color: #f0f2f5;

  .card-header {
    .title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
  }

  .config-section {
    margin-bottom: 20px;

    .config-card {
      border: none;
      background-color: #f9fafc;
    }

    .database-controls {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }

  .loan-table-section {
    margin-bottom: 30px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .table-title {
        font-size: 15px;
        font-weight: 500;
      }
    }

    .loan-table {
      margin-top: 10px;
    }

    .table-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;

      .record-count {
        font-size: 14px;
        color: #909399;
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid #ebeef5;

    .el-button {
      width: 180px;
      font-weight: 500;
    }
  }
}

@media (max-width: 1200px) {
  .loan-document-container {
    padding: 10px;
  }
}

.config-section .el-form-item {
  transition: all 0.3s ease;
}

.action-buttons {
  margin-top: 20px;
}
</style>
