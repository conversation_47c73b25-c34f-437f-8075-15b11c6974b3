<template>
  <div class="container" v-loading="pageLoading">
    <!--选择数据库-->
    <div class="db-info">
      <el-button type="warning" plain @click="showAddDBInfoDialog">新增</el-button>
      <el-table class="db-info-table" :data="requestResendDBList" max-height="260" v-loading="dbInfoTableLoading">
        <el-table-column width="70" label="选择">
          <template #default="scope">
            <el-radio v-model="resendForm.dbInfoId" :value="scope.row.id"></el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="databaseName" label="数据库名称" width="180"></el-table-column>
        <el-table-column prop="connectionUrl" label="连接URL" width="350"></el-table-column>
        <el-table-column prop="userName" label="用户名" width="150"></el-table-column>
        <el-table-column prop="tableName" label="表名" width="230"></el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="info" plain size="small" @click="showEditDBInfoDialog(scope.row)">编辑</el-button>
            <el-button type="warning" plain size="small" @click="testConnection(scope.row.id)">测试连接</el-button>
            <el-popconfirm width="100" confirm-button-text="删除" cancel-button-text="取消" title="确定要删除吗" @confirm="deleteDBInfo(scope.row.id)">
              <template #reference>
                <el-button type="danger" plain size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="dbInfoDialogVisible" :title="dbInfoDialogTitle" @closed="initDBInfoDialog">
        <div v-loading="dbInfoDialogLoading">
          <el-form label-width="100">
            <el-form-item label="数据库名称">
              <el-input v-model="requestResendDBInfo.databaseName"></el-input>
            </el-form-item>
            <el-form-item label="连接URL">
              <el-input v-model="requestResendDBInfo.connectionUrl"></el-input>
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="requestResendDBInfo.userName"></el-input>
            </el-form-item>
            <el-form-item label="密码">
              <el-input v-model="requestResendDBInfo.password"></el-input>
            </el-form-item>
            <el-form-item label="表名">
              <el-input v-model="requestResendDBInfo.tableName"></el-input>
            </el-form-item>
          </el-form>
          <el-button class="cfm-btn" type="primary" @click="addOrUpdateDBInfo">确定</el-button>
          <el-button type="info" plain @click="() => dbInfoDialogVisible = false">取消</el-button>
        </div>
      </el-dialog>
    </div>

    <el-divider />

    <div>
      <el-form class="resend-form" label-width="150">
        <el-form-item label="指定服务提供方">
          <el-select v-model="resendForm.serviceProvider" multiple clearable filterable>
            <el-option v-for="item in serviceProviderList" :key="item.serviceProvider" :value="item.serviceProvider" :label="item.serviceProvider">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="selectedTime"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="取值间隔时间(分钟)">
          <el-input v-model="resendForm.timeInterval"></el-input>
        </el-form-item>
        <el-form-item label="发送主机">
          <el-input v-model="resendForm.sendHost"></el-input>
        </el-form-item>
        <el-form-item label="线程数">
          <el-input v-model="resendForm.threadNum"></el-input>
        </el-form-item>
      </el-form>
      <el-button class="resend-btn" type="primary" @click="requestResend">开始重发</el-button>
    </div>

  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import {
  api_addRequestResendDBInfo, api_deleteRequestResendDBInfo,
  api_getRequestResendDBList, api_requestResend, api_testDBConnection,
  api_updateRequestResendDBInfo, RequestResendDBInfo, RequestResendForm
} from "@/api/modules/esbNetworkReplay/requestResend";
import { ElMessage } from "element-plus";
import {api_getServiceModelFiledValue, EsbServiceModel} from "@/api/modules/service-esb-data/service-model";

const dbInfoDialogTitle = ref<string>();
const requestResendDBList = ref<RequestResendDBInfo[]>();
const dbInfoDialogVisible = ref(false);
const requestResendDBInfo = ref<RequestResendDBInfo>({
  databaseName: "",
  connectionUrl: "",
  userName: "",
  password: "",
  tableName: ""
});
const dbInfoDialogLoading = ref(false);
const dbInfoTableLoading = ref(false);
const selectedTime = ref();
const resendForm = ref<RequestResendForm>({
  dbInfoId: "",
  serviceProvider: [],
  startTime: "",
  endTime: "",
  timeInterval: "30",
  sendHost: "",
  threadNum: "2"
});
const pageLoading = ref(false);
const serviceProviderList = ref<EsbServiceModel[]>([]);

const showAddDBInfoDialog = function () {
  dbInfoDialogTitle.value = "新增数据库信息";
  dbInfoDialogVisible.value = true;
}

const showEditDBInfoDialog = function (rowData) {
  dbInfoDialogTitle.value = "编辑数据库信息";
  requestResendDBInfo.value = JSON.parse(JSON.stringify(rowData));
  dbInfoDialogVisible.value = true;
}

const addOrUpdateDBInfo = function () {
  if (requestResendDBInfo.value.id) {
    updateDBInfo();
  } else {
    addDBInfo();
  }
}

const addDBInfo = async function () {
  dbInfoDialogLoading.value = true;
  await api_addRequestResendDBInfo(requestResendDBInfo.value).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("添加成功");
      queryRequestResendDBList();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    dbInfoDialogLoading.value = false;
    dbInfoDialogVisible.value = false;
  });
}

const updateDBInfo = async function () {
  dbInfoDialogLoading.value = true;
  await api_updateRequestResendDBInfo(requestResendDBInfo.value).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("修改成功");
      queryRequestResendDBList();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    dbInfoDialogLoading.value = false;
    dbInfoDialogVisible.value = false;
  });
}

const queryRequestResendDBList = async function () {
  dbInfoTableLoading.value = true;
  await api_getRequestResendDBList().then(res => {
    if (res.respCode === 2000) {
      requestResendDBList.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    dbInfoTableLoading.value = false;
  })
}

const initDBInfoDialog = function () {
  requestResendDBInfo.value = {
    databaseName: "",
    connectionUrl: "",
    userName: "",
    password: "",
    tableName: ""
  }
}

const deleteDBInfo = async function (id: string) {
  dbInfoTableLoading.value = true;
  await api_deleteRequestResendDBInfo(id).then(async res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除成功");
      await queryRequestResendDBList();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    dbInfoTableLoading.value = false;
  });
}

const testConnection = function (id: string) {
  api_testDBConnection(id).then(res => {
    if (res.respCode === 2000) {
      if (res.respData === true){
        ElMessage.success("连接成功");
      }
      else {
        ElMessage.error("连接失败");
      }
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
}

const queryServiceProviderList = function () {
  api_getServiceModelFiledValue("SERVICE_PROVIDER").then(res => {
    if (res.respCode === 2000) {
      serviceProviderList.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
}

const requestResend = async function () {
  console.log(resendForm.value);
  if (!resendDataCheck()) {
    return;
  }
  pageLoading.value = true;
  await api_requestResend(resendForm.value).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("重发成功");
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    pageLoading.value = false;
  });
}

const resendDataCheck = function (): boolean {
  if (resendForm.value.dbInfoId === ""){
    ElMessage.error("请选择数据库信息");
    return false;
  }
  if (resendForm.value.serviceProvider.length === 0){
    ElMessage.error("请选择服务提供方");
    return false;
  }
  if (resendForm.value.startTime === "" || resendForm.value.endTime === ""){
    ElMessage.error("请选择时间范围");
    return false;
  }
  if (resendForm.value.timeInterval === "") {
    ElMessage.error("请输入取值间隔");
    return false;
  }
  if (resendForm.value.sendHost === ""){
    ElMessage.error("请填写发送主机");
    return false;
  }
  if (resendForm.value.threadNum === ""){
    ElMessage.error("请填写发送主机");
    return false;
  }
  return true;
}

watch(() => selectedTime.value, (newVal) => {
  resendForm.value.startTime = newVal[0];
  resendForm.value.endTime = newVal[1];
}, {
  deep: true
})

onMounted(() => {
  queryRequestResendDBList();
  queryServiceProviderList();
})
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
