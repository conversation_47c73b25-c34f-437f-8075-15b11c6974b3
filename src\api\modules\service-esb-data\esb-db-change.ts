import http from "@/api";
import {Page} from "@/api/interface";
import {SERVICE_ESB_DATA} from "@/api/config/servicePort";

export const api_deduplicate = function (interfaceId: string, fieldPath: string) {
  return http.get(SERVICE_ESB_DATA + "/esbDbChange/deduplicate", {interfaceId, fieldPath});
}

export const api_insertToDbByIntfId = function (interfaceId: string) {
  return http.get(SERVICE_ESB_DATA + "/esbDbChange/insertToDbByIntfId", {interfaceId});
}

export const api_countInfoByInterfaceId = function (interfaceId: string) {
  return http.get(SERVICE_ESB_DATA + "/esbDbChange/countInfoByInterfaceId", {interfaceId});
}

export const api_deleteByInterfaceId = function (interfaceId: string) {
  return http.get(SERVICE_ESB_DATA + "/esbDbChange/deleteByInterfaceId", {interfaceId});
}

export const api_getFieldByInterfaceId = function (interfaceId: string) {
  return http.get<[]>(SERVICE_ESB_DATA + "/esbDbChange/getFieldByInterfaceId", {interfaceId});
}

export const api_getDataPaged = function (pageInfo: any) {
  return http.post<Page>(SERVICE_ESB_DATA + "/esbDbChange/getDataPaged", pageInfo);
}

export const api_deleteByCondition = function (condition: any) {
  return http.post(SERVICE_ESB_DATA + "/esbDbChange/deleteByCondition", condition);
}

export const api_sendAndCompare = function (data: any) {
  return http.post<any>(SERVICE_ESB_DATA + "/esbDbChange/sendAndCompare", data);
}
