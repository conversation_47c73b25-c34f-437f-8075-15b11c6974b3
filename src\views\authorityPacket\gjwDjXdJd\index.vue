<template>
  <div v-loading="cardLoading">
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span class="title">国监委-冻结/续冻/解冻</span>
        </div>
      </template>
      
      <el-collapse v-model="activeNames" class="query-form-collapse">
      <el-collapse-item title="BASICINFO" name="basicInfo">
          <el-form label-width="170" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="QQDBS[请求单标识]">
            <div style="display: flex">
                    <el-input v-model="attrValues.basicInfo.qqdbs" style="width: 210px;" />
              <el-tooltip v-if="attrValues.basicInfo.qqdbs" :content="Base64.encode(attrValues.basicInfo.qqdbs)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="QQCSLX[请求措施类型]">
            <div style="display: flex">
                    <el-select clearable v-model="attrValues.basicInfo.qqcslx" placeholder="请选择" style="width: 200px;">
                <el-option key="05" value="05" label="05-冻结" />
                <el-option key="06" value="06" label="06-续冻" />
                <el-option key="07" value="07" label="07-解冻" />
              </el-select>
              <el-tooltip v-if="attrValues.basicInfo.qqcslx" :content="Base64.encode(attrValues.basicInfo.qqcslx)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="CKZTLB[查控主体类别]">
            <div style="display: flex">
                    <el-select clearable v-model="attrValues.basicInfo.ckztlb" placeholder="请选择" style="width: 200px;">
                <el-option key="01" value="01" label="01-个人" />
                <el-option key="02" value="02" label="02-对公" />
              </el-select>
              <el-tooltip v-if="attrValues.basicInfo.ckztlb" :content="Base64.encode(attrValues.basicInfo.ckztlb)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
        </el-form>
      </el-collapse-item>

        <el-collapse-item 
          v-for="(freezeAccount, index) in attrValues.freezeAccountList" 
          :key="index"
          :name="'freezeAccount' + index"
        >
          <template #title>
            <div class="collapse-title">
              <span>FREEZEACCOUNT</span>
            </div>
          </template>
          
          <div class="form-toolbar">
            <div class="template-controls">
              <el-select
                v-model="selectedCommonInfoIdList[index]"
                @change="commonInfoChange(index)"
                size="small"
                placeholder="选择常用信息模板"
                clearable
                style="width: 220px"
              >
                <el-option v-for="item in commonInfoList" :key="item.id" :value="item.id!" :label="item.infoName" />
              </el-select>
              
              <el-button 
                type="primary" 
                size="small" 
                v-if="selectedCommonInfoIdList[index]" 
                @click="updateCommonInfo('FREEZEACCOUNT', index)" 
                plain
              >
                <el-icon><RefreshRight /></el-icon>更新信息
              </el-button>
              
              <el-popconfirm 
                title="确定要删除这条常用信息吗?" 
                v-if="selectedCommonInfoIdList[index]" 
                @confirm="deleteCommonInfo(index)"
                confirm-button-text="删除"
                cancel-button-text="取消"
              >
                <template #reference>
                  <el-button type="danger" size="small" plain>
                    <el-icon><Delete /></el-icon>删除信息
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>
          
          <el-form label-width="180" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="RWLSH[任务流水号]">
            <div style="display: flex">
                    <el-input v-model="freezeAccount.rwlsh" style="width: 260px;" />
              <el-tooltip v-if="freezeAccount.rwlsh" :content="Base64.encode(freezeAccount.rwlsh)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="YRWLSH[原任务流水号]">
            <div style="display: flex">
                    <el-input v-model="freezeAccount.yrwlsh" style="width: 260px;" />
              <el-tooltip v-if="freezeAccount.yrwlsh" :content="Base64.encode(freezeAccount.yrwlsh)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="DJZHHZ[客户名称]">
            <div style="display: flex">
              <el-input v-model="freezeAccount.djzhhz" style="width: 260px;" />
              <el-tooltip v-if="freezeAccount.djzhhz" :content="Base64.encode(freezeAccount.djzhhz)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="ZZLXDM(证件类型代码)">
            <div style="display: flex">
                    <el-select placeholder="请选择证件类型" clearable v-model="freezeAccount.zzlxdm" style="width: 220px;">
                <el-option v-for="item in idType1" :key="item.key" :value="item.key" :label="item.key + ' - ' + item.label" />
              </el-select>
              <el-tooltip v-if="freezeAccount.zzlxdm" :content="Base64.encode(freezeAccount.zzlxdm)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="ZZHM[证照号码(证件号)]">
            <div style="display: flex">
              <el-input v-model="freezeAccount.zzhm" style="width: 260px;" />
              <el-tooltip v-if="freezeAccount.zzhm" :content="Base64.encode(freezeAccount.zzhm)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="ZHXH[账号序号]">
            <div style="display: flex">
              <el-input v-model="freezeAccount.zhxh" style="width: 260px;" />
              <el-tooltip v-if="freezeAccount.zhxh" :content="Base64.encode(freezeAccount.zhxh)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="ZH[账卡号]">
            <div style="display: flex">
              <el-input v-model="freezeAccount.zh" style="width: 260px;" />
              <el-tooltip v-if="freezeAccount.zh" :content="Base64.encode(freezeAccount.zh)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="DJFS[冻结方式]">
            <div style="display: flex">
                    <el-select placeholder="请选择冻结方式" v-model="freezeAccount.djfs" clearable @change="freezeAccount.je = ''" style="width: 200px;">
                <el-option key="01" value="01" label="01-金额冻结" />
                <el-option key="02" value="02" label="02-账户冻结" />
              </el-select>
              <el-tooltip v-if="freezeAccount.djfs" :content="Base64.encode(freezeAccount.djfs)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="JE[冻结金额]">
            <div style="display: flex">
              <el-input v-model="freezeAccount.je" style="width: 260px;" />
              <el-tooltip v-if="freezeAccount.je" :content="Base64.encode(freezeAccount.je)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="BZ[币种]">
            <div style="display: flex">
                    <el-select placeholder="请选择币种" v-model="freezeAccount.bz" clearable filterable style="width: 200px;">
                <el-option
                  v-for="item in currencyType1"
                  :key="item.en"
                  :label="item.en + '【' + item.cn + '/' + item.unit + '】'"
                  :value="item.en"
                />
              </el-select>
              <el-tooltip v-if="freezeAccount.bz" :content="Base64.encode(freezeAccount.bz)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="KSSJ[控制开始时间]">
            <div style="display: flex">
              <el-date-picker
                v-model="freezeAccount.kssj"
                type="date"
                placeholder="选择开始时间"
                format="YYYYMMDD"
                value-format="YYYYMMDD"
                @change="onTimeChange(freezeAccount)"
                style="width: 200px;"
              />
              <el-tooltip v-if="freezeAccount.kssj" :content="Base64.encode(freezeAccount.kssj)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="JSSJ[控制结束时间]">
            <div style="display: flex">
              <el-date-picker
                v-model="freezeAccount.jssj"
                type="date"
                placeholder="选择结束时间"
                format="YYYYMMDD"
                value-format="YYYYMMDD"
                @change="onTimeChange(freezeAccount)"
                style="width: 200px;"
              />
              <el-tooltip v-if="freezeAccount.jssj" :content="Base64.encode(freezeAccount.jssj)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <div class="bottom-actions">
              <el-button type="success" size="small" @click="saveCommonInfo('FREEZEACCOUNT', freezeAccount)" plain>
                <el-icon><Download /></el-icon>保存为常用信息
              </el-button>
            </div>
        </el-form>
      </el-collapse-item>
    </el-collapse>

      <div class="card-footer-actions">
        <el-button type="info" plain size="small" @click="initRandomDigits">
          <el-icon><Refresh /></el-icon>刷新随机数
        </el-button>
        <el-button type="primary" size="small" @click="addFreezeAccount" plain>
          <el-icon><Plus /></el-icon>添加FREEZEACCOUNT
        </el-button>
        <el-button type="danger" size="small" @click="deleteFreezeAccount" plain v-if="attrValues.freezeAccountList.length !== 1">
          <el-icon><Delete /></el-icon>删除FREEZEACCOUNT
        </el-button>
      </div>
    </el-card>

    <el-card class="action-card">
      <template #header>
        <div class="card-header">
          <span class="title">操作</span>
        </div>
      </template>
      
      <div class="action-buttons">
        <el-button type="warning" @click="generatePacket">
          <el-icon><Document /></el-icon>生成报文
        </el-button>
        <el-button type="primary" @click="downloadZipPacket" :disabled="!packetZipName">
          <el-icon><Download /></el-icon>下载
        </el-button>
        <el-button type="success" @click="ftpUpload" :disabled="!packetZipName">
          <el-icon><Upload /></el-icon>上传至服务器
        </el-button>
    </div>

      <div v-if="packetZipName" class="file-info">
        <el-tag type="success">文件已生成: {{ packetZipName }}</el-tag>
    </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  api_deleteCommonInfo,
  api_downloadZipPacket, api_editCommonInfo, api_ftpUpload,
  api_generatePacket, api_getCommonInfo,
  api_getXmlStructureTree, api_saveTemplate, EntryDto, PacketCommonInfo,
  XmlTreeDto
} from "@/api/modules/service-integrated-tester/authority-packet";
import {onMounted, ref} from "vue";
import {Base64} from "js-base64";
import currencyType1 from "@/data/currencyType1.json";
import idType1 from "@/data/idType1.json";
import {ElMessage, ElMessageBox} from "element-plus";
import { Plus, Delete, Download, Refresh, RefreshRight, Document, Upload } from '@element-plus/icons-vue';

const packetType = "国监委-冻结/续冻/解冻";
const templateFolderName = "ZYJWGJJW006H1014403010012QQDBS00020220402161327";
const templateXmlName = "SS17006H1014403010012QQDBS00020220402161327.xml";
const ftpRemotePath = "/中央纪委国家监委/Download";
const templateFreezeAccount = {
  rwlsh: "",
  yrwlsh: "",
  bz: "CNY",
  djzhhz: "",
  zzlxdm: "",
  zzhm: "",
  zhxh: "",
  zh: "",
  djfs: "",
  je: "",
  kssj: "",
  jssj: ""
}

const lastRandomDigits = ref();
const cardLoading = ref(false);
const activeNames = ref(["basicInfo", "freezeAccount0"]);
const commonInfoList = ref<PacketCommonInfo[]>([]);
const selectedCommonInfoIdList = ref<(string | undefined) []>([]);
const selectedCommonInfoList = ref<(PacketCommonInfo | undefined) []>([]);
const packetXml = ref<XmlTreeDto>();
const attrValues = ref({
  basicInfo: {
    qqdbs: "",
    qqcslx: "",
    ckztlb: ""
  },
  freezeAccountList: [{ ... templateFreezeAccount }]
});
const packetZipName = ref<string>();

const initRandomDigits = function () {
  lastRandomDigits.value = "QQDBS" + Date.now().toString() + Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  for (let i = 0; i < attrValues.value.freezeAccountList.length; i++) {
    const currFreezeAccount = attrValues.value.freezeAccountList[i];
    currFreezeAccount.rwlsh = lastRandomDigits.value + (i+1).toString().padStart(5, '0');
  }
  attrValues.value.basicInfo.qqdbs = lastRandomDigits.value;
};

const queryTemplateXml = function () {
  cardLoading.value = true;
  api_getXmlStructureTree({
    templateFolderName,
    templateXmlName
  }).then(res => {
    if (res.respCode === 2000) {
      packetXml.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    cardLoading.value = false;
  });
};

const rulesCheck = function () {
  if (!attrValues.value.basicInfo.qqdbs) {
    ElMessage.error("QQDBS[请求单标识]为必填项");
    return false;
  }
  if (!attrValues.value.basicInfo.qqcslx) {
    ElMessage.error("QQCSLX[请求措施类型]为必填项");
    return false;
  }
  if (!attrValues.value.basicInfo.ckztlb) {
    ElMessage.error("CKZTLB[查控主体类别]为必填项");
    return false;
  }
  for (let i = 0; i < attrValues.value.freezeAccountList.length; i++) {
    const currFreezeAccount = attrValues.value.freezeAccountList[i];
    const errMsgPrefix = "第" + (i+1) + "个元素 - ";
    if (!currFreezeAccount.rwlsh) {
      ElMessage.error(errMsgPrefix + "RWLSH[任务流水号]为必填项");
      return false;
    }
    if (attrValues.value.basicInfo.qqcslx === "06" || attrValues.value.basicInfo.qqcslx === "07") {
      if (!currFreezeAccount.yrwlsh) {
        ElMessage.error(errMsgPrefix + "QQCSLX[请求措施类型]为06或07时，YRWLSH[原任务流水号]必填");
        return false;
      }
    }
    if (attrValues.value.basicInfo.qqcslx === "05") {
      if (currFreezeAccount.yrwlsh) {
        ElMessage.error(errMsgPrefix + "QQCSLX[请求措施类型]为05时，YRWLSH[原任务流水号]不填");
        return false;
      }
    }
    if (!currFreezeAccount.djzhhz) {
      ElMessage.error(errMsgPrefix + "DJZHHZ[客户名称]为必填项");
      return false;
    }
    if (!currFreezeAccount.zzlxdm) {
      ElMessage.error(errMsgPrefix + "ZZLXDM(证件类型代码)为必填项");
      return false;
    }
    if (!currFreezeAccount.zzhm) {
      ElMessage.error(errMsgPrefix + "ZZHM[证照号码(证件号)]为必填项");
      return false;
    }
    if (!currFreezeAccount.zh) {
      ElMessage.error(errMsgPrefix + "ZH[账卡号]为必填项");
      return false;
    }
    if (!currFreezeAccount.djfs) {
      ElMessage.error(errMsgPrefix + "请选择DJFS[冻结方式]");
      return false;
    }
    if (currFreezeAccount.djfs === "01") {
      if (!currFreezeAccount.je) {
        ElMessage.error(errMsgPrefix + "DJFS[冻结方式]为01-金额冻结时，JE[冻结金额]必填");
        return false;
      }
      const regex = /^\d+\.\d{2}$/;
      if (!regex.test(currFreezeAccount.je)) {
        ElMessage.error(errMsgPrefix + "JE[冻结金额]小数点后须保留两位");
        return false;
      }
    }
    if (currFreezeAccount.djfs === "02" && currFreezeAccount.je) {
      ElMessage.error(errMsgPrefix + "DJFS[冻结方式]为02-账户冻结时，JE[冻结金额]不填");
      return false;
    }
    if (!currFreezeAccount.kssj) {
      ElMessage.error(errMsgPrefix + "KSSJ[控制开始时间]为必填项");
      return false;
    }
    if (!currFreezeAccount.jssj) {
      ElMessage.error(errMsgPrefix + "JSSJ[控制结束时间]为必填项");
      return false;
    }
    if (currFreezeAccount.kssj > currFreezeAccount.jssj) {
      ElMessage.error(errMsgPrefix + "JSSJ[控制结束时间]应大于KSSJ[控制开始时间]");
      return false;
    }
  }
  return true;
}

const assignValue = function () {
  const basicInfo = packetXml.value?.children.find(item => item.tag === "BASICINFO");
  for (let key in attrValues.value.basicInfo) {
    basicInfo!.attributeList.find(item => item.key === key.toUpperCase())!.value = Base64.encode(attrValues.value.basicInfo[key]);
  }

  const templateFreezeAccountTree = JSON.parse(JSON.stringify(packetXml.value?.children.find(item => item.tag === "FREEZEACCOUNTS")?.children[0]));
  for (let i = 0; i < attrValues.value.freezeAccountList.length; i++) {
    const currFreezeAccount = attrValues.value.freezeAccountList[i];
    const currTree = JSON.parse(JSON.stringify(templateFreezeAccountTree));
    for (let key in currFreezeAccount) {
      currTree!.attributeList.find((item: EntryDto) => item.key === key.toUpperCase())!.value = Base64.encode(currFreezeAccount[key]);
    }
    packetXml.value!.children.find(item => item.tag === "FREEZEACCOUNTS")!.children[i] = currTree!;
  }
};

const generatePacket = function () {
  cardLoading.value = true;
  if (!rulesCheck()) {
    cardLoading.value = false;
    return;
  }
  assignValue();
  api_generatePacket({
    xmlTreeDto: packetXml.value!,
    templateFolderName,
    templateXmlName,
    lastRandomDigits: lastRandomDigits.value
  }).then(res => {
    if (res.respCode === 2000) {
      packetZipName.value = res.respData;
      ElMessage.success("报文包生成成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    cardLoading.value = false;
  });
};

const downloadZipPacket = function () {
  if (!packetZipName.value) {
    ElMessage.error({ message: "无文件下载，请先生成文件", showClose: true, duration: 10000 });
    return;
  }
  api_downloadZipPacket(packetZipName.value!);
};

const ftpUpload = function () {
  cardLoading.value = true;
  if (!packetZipName.value) {
    ElMessage.warning({ message: "无文件上传，请先生成文件", showClose: true, duration: 10000 });
    cardLoading.value = false;
    return;
  }
  api_ftpUpload(packetZipName.value, ftpRemotePath).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("上传成功");
    } else {
      ElMessage.error({ message: res.respMsg, showClose: true, duration: 10000 });
    }
  }).finally(() => {
    cardLoading.value = false;
  })
};

const initFreezeAccount = function (listIndex: number) {
  const freezeAccount = attrValues.value.freezeAccountList[listIndex];
  freezeAccount.rwlsh = lastRandomDigits.value + (listIndex + 1).toString().padStart(5, '0');
  freezeAccount.yrwlsh = "";
  freezeAccount.bz = "CNY";
  freezeAccount.djzhhz = "";
  freezeAccount.zzlxdm = "";
  freezeAccount.zzhm = "";
  freezeAccount.zhxh = "";
  freezeAccount.zh = "";
  freezeAccount.djfs = "";
  freezeAccount.je = "";
  freezeAccount.kssj = "";
  freezeAccount.jssj = "";
};

const queryCommonInfo = function (tagName: string) {
  api_getCommonInfo({
    infoName: "",
    packetType,
    tagName,
    infoContent: ""
  }).then(res => {
    if (res.respCode === 2000) {
      commonInfoList.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const commonInfoChange = function (listIndex: number) {
  initFreezeAccount(listIndex);
  if (!selectedCommonInfoIdList.value[listIndex]) {
    selectedCommonInfoList.value[listIndex] = undefined;
    return;
  }
  selectedCommonInfoList.value[listIndex] = commonInfoList.value.find(item => item.id === selectedCommonInfoIdList.value[listIndex]);
  const currFreezeAccount = attrValues.value.freezeAccountList[listIndex];
  const commonInfoJson = JSON.parse(selectedCommonInfoList.value[listIndex]!.infoContent);
  for (let key in commonInfoJson) {
    currFreezeAccount[key] = commonInfoJson[key];
  }
};

const saveCommonInfo = function (tagName: string, freezeAccount: any) {
  let copyFreezeAccount = JSON.parse(JSON.stringify(freezeAccount));
  delete copyFreezeAccount.rwlsh;
  delete copyFreezeAccount.yrwlsh;
  ElMessageBox.prompt("请输入名称", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(({value}) => {
    cardLoading.value = true;
    api_saveTemplate({
      infoName: value,
      packetType,
      tagName,
      infoContent: JSON.stringify(copyFreezeAccount)
    }).then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("信息保存成功");
        queryCommonInfo(tagName);
      } else {
        ElMessage.error(res.respMsg);
      }
    }).finally(() => {
      cardLoading.value = false;
    });
  });
};

const updateCommonInfo = function (tagName: string, listIndex: number) {
  let copyFreezeAccount = JSON.parse(JSON.stringify(attrValues.value.freezeAccountList[listIndex]));
  delete copyFreezeAccount.rwlsh;
  delete copyFreezeAccount.yrwlsh;
  const newCommonInfo: PacketCommonInfo = {
    id: selectedCommonInfoIdList.value[listIndex],
    infoName: selectedCommonInfoList.value[listIndex]!.infoName,
    packetType,
    tagName,
    infoContent: JSON.stringify(copyFreezeAccount)
  }
  api_editCommonInfo(newCommonInfo).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("更新成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    queryCommonInfo("FREEZEACCOUNT");
  });
};

const deleteCommonInfo = function (listIndex: number) {
  api_deleteCommonInfo(selectedCommonInfoIdList.value[listIndex]!).then(res => {
    if (res.respCode === 2000) {
      selectedCommonInfoIdList.value[listIndex] = undefined;
      commonInfoChange(listIndex);
      ElMessage.success("删除成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    queryCommonInfo("FREEZEACCOUNT");
  });
};

const onTimeChange = function (freezeAccount: any) {
  if (!freezeAccount.kssj) {
    freezeAccount.kssj = "";
  }
  if (!freezeAccount.jssj) {
    freezeAccount.jssj = "";
  }
};

const addFreezeAccount = function () {
  attrValues.value.freezeAccountList.push({... templateFreezeAccount});
  initFreezeAccount(attrValues.value.freezeAccountList.length - 1);
  selectedCommonInfoIdList.value.push(undefined);
  selectedCommonInfoList.value.push(undefined);
};

const deleteFreezeAccount = function () {
  attrValues.value.freezeAccountList.pop();
  selectedCommonInfoIdList.value.pop();
  selectedCommonInfoList.value.pop();
};

onMounted(() => {
  queryTemplateXml();
  queryCommonInfo("FREEZEACCOUNT");
  initRandomDigits();
  initFreezeAccount(0);
});
</script>

<style scoped lang="scss">
.query-card, .action-card {
  margin-bottom: 16px;
  border-radius: 8px;
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .form-info {
    color: #606266;
    font-size: 13px;
    background-color: #f0f2f5;
    padding: 2px 8px;
    border-radius: 4px;
    
    small {
      color: #909399;
      margin-left: 4px;
    }
  }
}

.form-toolbar {
  display: flex;
  margin-bottom: 16px;
  background: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  
  .template-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.query-form {
  margin-top: 10px;
  
  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-picker) {
    width: 100%;
  }
}

.bottom-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-start;
}

.card-footer-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.query-form-collapse {
  :deep(.el-collapse-item__header) {
    padding: 0 8px;
    
    &.is-active {
      background-color: #ecf5ff;
      border-bottom-color: #d9ecff;
    }
  }
  
  :deep(.el-collapse-item__wrap) {
    padding: 16px;
    background-color: #fbfbfb;
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.file-info {
  margin-top: 16px;
}
</style>
