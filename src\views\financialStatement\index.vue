<template>
  <div v-loading="containerLoading" class="financial-statement-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>协同办公报表比对工具</span>
        </div>
      </template>

      <!-- 上传区域 -->
      <el-form :model="uploadInfo" label-position="top">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="报表类型">
              <el-select
                v-model="uploadInfo.statementType"
                clearable
                placeholder="请选择报表类型"
                class="statement-type-select"
                @change="onStatementTypeChange"
              >
                <el-option
                  v-for="[value, label] in Object.entries(statementTypeOptions)"
                  :value="value"
                  :key="value"
                  :label="label"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <div v-if="uploadInfo.isShowUpload" class="upload-section">
          <el-row :gutter="20">
            <el-col :md="12" :sm="24">
              <el-form-item label="文件1">
                <el-upload
                  v-model:file-list="uploadInfo.firstFile"
                  :action="api_upload(uploadInfo.uploadId, 'first')"
                  :limit="1"
                  class="upload-container"
                  :before-remove="beforeRemove"
                  :on-exceed="handleExceed1"
                  ref="upload1"
                  :on-success="setUploadId"
                  drag
                  id="upload1"
                >
                  <el-icon class="upload-icon"><Upload /></el-icon>
                  <div class="upload-text">将文件拖到此处或<em>点击上传</em></div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24">
              <el-form-item label="文件2">
                <el-upload
                  v-model:file-list="uploadInfo.secondFile"
                  :action="api_upload(uploadInfo.uploadId, 'second')"
                  :limit="1"
                  class="upload-container"
                  :before-remove="beforeRemove"
                  :on-exceed="handleExceed2"
                  ref="upload2"
                  :on-success="setUploadId"
                  drag
                  id="upload2"
                >
                  <el-icon class="upload-icon"><Upload /></el-icon>
                  <div class="upload-text">将文件拖到此处或<em>点击上传</em></div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row class="compare-btn-row">
            <el-button
              type="primary"
              size="large"
              @click="compare"
              :disabled="!uploadInfo.firstFile.length || !uploadInfo.secondFile.length"
              icon="Search"
              round
              id="compareBtn"
            >
              开始比对
            </el-button>
          </el-row>
        </div>
      </el-form>

      <!-- 比对说明 -->
      <div v-if="uploadInfo.isShowUpload" class="guide-section">
        <div class="section-title">
          <el-icon><InfoFilled /></el-icon>
          <span>比对说明</span>
        </div>

        <el-collapse v-model="activeCollapse">
          <el-collapse-item title="基本规则" name="1">
            <div class="rule-item">
              <el-icon color="#409EFF"><Check /></el-icon>
              <span>仅比对数值类型单元格</span>
            </div>
            <div class="rule-item">
              <el-icon color="#409EFF"><Check /></el-icon>
              <span>比对结果将删除原单元格的背景颜色</span>
            </div>
          </el-collapse-item>

          <el-collapse-item title="差异标记说明" name="2">
            <div class="color-guide-grid">
              <div class="color-guide-item">
                <div class="color-block" style="background: #d9f02d"></div>
                <div class="color-desc">
                  <div class="color-title">第一个文件非0，第二个文件为0的单元格</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'fund-status'">（仅【余额】列）</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'fund-status-all'">（忽略【比年初】列）</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'major-customer'">
                    （时点余额大于500万客户明细 - 仅【余额】列） （年度新增100万以上客户明细 - 仅【余额】【年平均】【月平均】列）
                    （年度减少100万以上客户明细 - 仅【余额】【年平均】【月平均】列） （贷款余额500万以上客户明细 -
                    仅【贷款余额】【存款余额】列）
                  </div>
                </div>
              </div>

              <div class="color-guide-item">
                <div class="color-block" style="background: #d47be7"></div>
                <div class="color-desc">
                  <div class="color-title">第一个文件为0，第二个文件非0的单元格</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'fund-status'">（仅【余额】列）</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'fund-status-all'">（忽略【比年初】列）</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'major-customer'">
                    （时点余额大于500万客户明细 - 仅【余额】列） （年度新增100万以上客户明细 - 仅【余额】【年平均】【月平均】列）
                    （年度减少100万以上客户明细 - 仅【余额】【年平均】【月平均】列） （贷款余额500万以上客户明细 -
                    仅【贷款余额】【存款余额】列）
                  </div>
                </div>
              </div>

              <div class="color-guide-item">
                <div class="color-block" style="background: #7bb9e7"></div>
                <div class="color-desc">
                  <div class="color-title d-flex align-center">
                    <span>第一个文件与第二个文件差值大于</span>
                    <el-input-number
                      size="small"
                      v-model="valueDiff"
                      :min="0"
                      :step="1000"
                      controls-position="right"
                      class="diff-input"
                    />
                    <span>的单元格</span>
                  </div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'fund-status'">（仅【余额】列）</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'fund-status-all'">（忽略【比年初】列）</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'major-customer'">
                    （时点余额大于500万客户明细 - 仅【余额】列） （年度新增100万以上客户明细 - 仅【余额】【年平均】【月平均】列）
                    （年度减少100万以上客户明细 - 仅【余额】【年平均】【月平均】列） （贷款余额500万以上客户明细 -
                    仅【贷款余额】【存款余额】列）
                  </div>
                </div>
              </div>

              <div class="color-guide-item">
                <div class="color-block" style="background: #3be764"></div>
                <div class="color-desc">
                  <div class="color-title">第一个文件中不存在，第二个文件中新增的行</div>
                </div>
              </div>

              <div class="color-guide-item">
                <div class="color-block" style="background: #ea2020"></div>
                <div class="color-desc">
                  <div class="color-title">第一个文件中存在，第二个文件中删除的行</div>
                  <div class="color-note">（此类差异统一展示在最后）</div>
                </div>
              </div>

              <div class="color-guide-item" v-if="!uploadInfo.statementType.includes('balance-sheet')">
                <div class="color-block" style="background: #fad3d9"></div>
                <div class="color-desc">
                  <div class="color-title">第二个文件中值为空的单元格</div>
                </div>
              </div>

              <div class="color-guide-item" v-if="!uploadInfo.statementType.includes('balance-sheet')">
                <div class="color-block" style="background: #bc8f8f"></div>
                <div class="color-desc">
                  <div class="color-title">第二个文件中支行名称相同的行</div>
                  <div class="color-note" v-if="uploadInfo.statementType === 'indicator-change'">
                    <b>信贷主要变化情况</b>：企业名称+业务品种+发生类型+支行名称<br />
                    <b>当前逾期与垫款明细（大于100万）</b>：企业名称+业务品种+支行名称<br />
                    <b>大额入款情况表/大额出款情况表</b>：企业名称+支行名称<br />
                    <b>欠息明细</b>：企业名称+表内表外+支行名称<br />
                    <b>不良贷款明细</b>：企业名称+五级分类+支行名称
                  </div>
                </div>
              </div>

              <div
                class="color-guide-item"
                v-if="
                  !uploadInfo.statementType.includes('balance-sheet') &&
                  uploadInfo.statementType !== 'indicator-change' &&
                  uploadInfo.statementType !== 'major-customer'
                "
              >
                <div class="color-block" style="background: #40e0d0"></div>
                <div class="color-desc">
                  <div class="color-title">第二个文件中数值重复的行</div>
                  <div class="color-note">（若该行数值类型数据全为0则不标注）</div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 比对结果 -->
      <div v-if="compareResult && uploadInfo.isShowUpload" class="result-section">
        <div class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          <span>比对结果</span>
        </div>

        <div class="result-actions">
          <el-button 
            type="info" 
            @click="openNewWindow" 
            icon="Link" 
            class="action-button view-button"
            :plain="false"
            round
          >
            新窗口查看
          </el-button>
          
          <el-button 
            type="primary" 
            @click="downloadResult" 
            icon="Download" 
            id="downloadBtn"
            class="action-button download-button"
            round
          >
            下载比对结果
          </el-button>

          <div class="result-notice">
            <el-icon><Warning /></el-icon>
            <span>由于涉及敏感数据，比对结果服务器不做保留，如需留存请及时下载比对结果</span>
          </div>
        </div>

        <div class="result-frame-container">
          <iframe :srcdoc="compareResult" class="result-frame"></iframe>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";
import {
  api_cleanUpload,
  api_compare,
  api_getCompareTypes,
  api_upload
} from "@/api/modules/service-integrated-tester/financial-statement";
import { ElMessage, genFileId, UploadFile, UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { ResultData } from "@/api/interface";
import { Document, Upload, DataAnalysis, InfoFilled, Warning, Check } from "@element-plus/icons-vue";

const containerLoading = ref(false);
const uploadInfo = ref({
  statementType: "",
  isShowUpload: false,
  firstFile: [],
  secondFile: [],
  uploadId: ""
});
const statementTypeOptions = ref<Record<string, string>>({});
const compareResult = ref<string>("");
const valueDiff = ref<number>(10000);
const upload1 = ref<UploadInstance>();
const upload2 = ref<UploadInstance>();
const activeCollapse = ref<string[]>(["1", "2"]);

const queryCompareType = function () {
  containerLoading.value = true;
  api_getCompareTypes()
    .then(res => {
      if (res.respCode === 2000) {
        statementTypeOptions.value = res.respData;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      containerLoading.value = false;
    });
};

const handleExceed1: UploadProps["onExceed"] = files => {
  upload1.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  upload1.value!.handleStart(file);
  upload1.value!.submit();
};

const handleExceed2: UploadProps["onExceed"] = files => {
  upload2.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  upload2.value!.handleStart(file);
  upload2.value!.submit();
};

const setUploadId = function (res: ResultData<string>) {
  uploadInfo.value.uploadId = res.respData;
};

const beforeRemove: UploadProps["beforeRemove"] = function (uploadFile: UploadFile) {
  if (uploadInfo.value.firstFile[0] === uploadFile) {
    cleanUploadFile("first");
  }
  if (uploadInfo.value.secondFile[0] === uploadFile) {
    cleanUploadFile("second");
  }
  return true;
};

const compare = function () {
  if (!uploadInfo.value.firstFile.length || !uploadInfo.value.secondFile.length) {
    ElMessage.warning("请上传两个文件后再进行比对");
    return;
  }

  containerLoading.value = true;
  compareResult.value = "";
  api_compare(uploadInfo.value.statementType, uploadInfo.value.uploadId, valueDiff.value)
    .then(res => {
      if (res.respCode === 2000) {
        compareResult.value = res.respData;
        cleanUploadFile("");
        ElMessage.success({
          message: "比对成功",
          type: "success",
          duration: 2000
        });
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      containerLoading.value = false;
    });
};

const cleanUploadFile = function (fileIndex: string) {
  api_cleanUpload(uploadInfo.value.uploadId, fileIndex).then(res => {
    if (res.respCode === 2000) {
      if (fileIndex === "first") {
        uploadInfo.value.firstFile = [];
      }
      if (fileIndex === "second") {
        uploadInfo.value.secondFile = [];
      } else {
        uploadInfo.value.firstFile = [];
        uploadInfo.value.secondFile = [];
      }
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const openNewWindow = function () {
  nextTick(() => {
    const newWindow = window.open();
    if (!newWindow) {
      ElMessage.error("新页面打开失败");
      return;
    }
    newWindow.document.write(compareResult.value);
    newWindow.document.close();
  });
};

const downloadResult = function () {
  const blob = new Blob([compareResult.value], { type: "text/html;charset=utf-8" });
  const tempUrl = URL.createObjectURL(blob);
  const tempLink = document.createElement("a");
  tempLink.href = tempUrl;
  tempLink.download = "比对结果.html";
  tempLink.click();
  URL.revokeObjectURL(tempUrl);
};

// 设置元素id，提供给自动化脚本
const onStatementTypeChange = async function () {
  await nextTick();
  const selectedSpan = document.querySelector(".el-select__selected-item span");
  if (selectedSpan) {
    selectedSpan.id = `statement-type`;
  }
};

watch(
  () => uploadInfo.value.statementType,
  newValue => {
    cleanUploadFile("");
    compareResult.value = "";
    if (newValue) {
      uploadInfo.value.isShowUpload = true;
      return;
    }
    uploadInfo.value.isShowUpload = false;
  }
);

onMounted(() => {
  queryCompareType();
});

onBeforeUnmount(() => {
  cleanUploadFile("");
});
</script>

<style scoped lang="scss">
.financial-statement-container {
  background-color: #f5f7fa;

  // 添加全局v-loading样式
  :deep(.el-loading-mask) {
    z-index: 1000;
  }

  .main-card {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #333;

      .el-icon {
        margin-right: 8px;
        font-size: 20px;
        color: #7e41b6;
      }
    }
  }

  .statement-type-select {
    width: 320px;
  }

  .w-100 {
    width: 100%;
  }

  .upload-section {
    margin-top: 10px;
    padding: 10px 0;

    .upload-container {
      width: 100%;

      .upload-icon {
        font-size: 22px;
        color: #7e41b6;
        margin-bottom: 5px;
      }

      .upload-text {
        color: #666;
        font-size: 13px;

        em {
          color: #7e41b6;
          font-style: normal;
          text-decoration: underline;
        }
      }

      :deep(.el-upload-dragger) {
        padding: 8px;
        height: auto;
        min-height: 80px;
      }
    }

    .compare-btn-row {
      display: flex;
      justify-content: center;
      margin-top: 15px;
      margin-bottom: 5px;
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;

    .el-icon {
      margin-right: 8px;
      font-size: 18px;
      color: #7e41b6;
    }
  }

  .guide-section {
    margin-top: 30px;

    .rule-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .el-icon {
        margin-right: 8px;
      }
    }

    .color-guide-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 16px;

      .color-guide-item {
        display: flex;
        align-items: flex-start;
        padding: 10px;
        border-radius: 6px;
        background-color: #f9f9f9;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
        }

        .color-block {
          width: 36px;
          height: 36px;
          border-radius: 4px;
          margin-right: 12px;
          border: 1px solid rgba(0, 0, 0, 0.1);
          flex-shrink: 0;
        }

        .color-desc {
          .color-title {
            font-weight: 500;
            margin-bottom: 4px;
          }

          .color-note {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
          }
        }
      }
    }

    .d-flex {
      display: flex;
    }

    .align-center {
      align-items: center;
    }

    .diff-input {
      width: 100px;
      margin: 0 8px;
    }
  }

  .result-section {
    margin-top: 30px;

    .result-actions {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .el-button {
        margin-right: 15px;
      }
      
      .action-button {
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 6px;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        .button-icon {
          margin-right: 2px;
        }
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
      }
      
      .view-button {
        background-color: #7e41b6;
        color: white;
        
        &:hover {
          background-color: #9050c9;
        }
        
        &:active {
          background-color: #6a3699;
        }
      }
      
      .download-button {
        background: linear-gradient(135deg, #1890ff, #0050b3);
        
        &:hover {
          background: linear-gradient(135deg, #40a9ff, #096dd9);
        }
        
        &:active {
          background: linear-gradient(135deg, #096dd9, #003a8c);
        }
      }
      
      .result-notice {
        display: flex;
        align-items: center;
        margin-left: 10px;
        font-size: 12px;
        color: #ff9800;
        flex: 1;
        
        .el-icon {
          margin-right: 5px;
        }
      }
    }

    .result-frame-container {
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
      height: 600px;

      .result-frame {
        border: 0;
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
