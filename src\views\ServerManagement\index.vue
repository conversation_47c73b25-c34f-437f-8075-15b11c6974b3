<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="main-card" shadow="hover">
          <template #header>
            <div class="page-header">
              <div class="title-container">
                <el-icon class="header-icon">
                  <Monitor />
                </el-icon>
                <div>
                  <h2 class="header-title">服务器信息管理</h2>
                  <div class="server-count">
                    共 <span class="count-num">{{ totalServers }}</span> 台服务器
                  </div>
                </div>
              </div>
              <el-button type="primary" size="large" class="add-button" @click="openAddDialog">
                <el-icon>
                  <Plus />
                </el-icon>
                新增服务器
              </el-button>
            </div>
          </template>

          <!-- 搜索区域 -->
          <div class="search-container">
            <el-form :inline="true" class="search-form">
              <el-form-item>
                <el-input
                  v-model="searchQuery"
                  placeholder="请输入服务器名称搜索"
                  clearable
                  @clear="handleSearchClear"
                  @keyup.enter="handleSearch"
                  class="search-input"
                >
                  <template #prefix>
                    <el-icon>
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" class="search-button" @click="handleSearch">
                  <el-icon>
                    <Search />
                  </el-icon>
                  搜索
                </el-button>
                <el-button class="reset-button" @click="resetSearch">
                  <el-icon>
                    <Refresh />
                  </el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 服务器列表表格 -->
          <el-table
            :data="tableData"
            v-loading="loading"
            border
            stripe
            highlight-current-row
            style="width: 100%; margin-top: 20px"
            :header-cell-style="{
              background: '#f0f5ff',
              color: '#1e3a8a',
              fontWeight: 'bold',
              fontSize: '14px',
              textAlign: 'center'
            }"
            class="server-table"
          >
            <el-table-column prop="serverName" label="服务器名称" min-width="180" sortable>
              <template #default="{ row }">
                <div class="server-name">
                  <el-icon class="server-icon">
                    <Monitor />
                  </el-icon>
                  <span>{{ row.serverName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="serverIp" label="IP地址" min-width="150" sortable />
            <el-table-column prop="environment" label="环境类型" min-width="100" />
            <el-table-column prop="username" label="用户名" min-width="120" />
            <el-table-column prop="remark" label="描述" min-width="200" show-overflow-tooltip />
            <el-table-column label="操作" fixed="right" width="180">
              <template #default="scope">
                <div class="action-group">
                  <el-tooltip content="查看详情" placement="top">
                    <el-button type="primary" circle size="small" @click.stop="handleView(scope.row)" class="action-button">
                      <el-icon>
                        <View />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="编辑信息" placement="top">
                    <el-button type="warning" circle size="small" @click.stop="handleEdit(scope.row)" class="action-button">
                      <el-icon>
                        <EditPen />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="删除服务器" placement="top">
                    <el-button type="danger" circle size="small" @click.stop="handleDelete(scope.row.id)" class="action-button">
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              background
              layout="prev, pager, next"
              :total="totalServers"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增/编辑服务器对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑服务器信息' : '新增服务器信息'"
      width="50%"
      destroy-on-close
      @closed="resetForm"
      class="server-dialog"
    >
      <el-form ref="serverFormRef" :model="serverForm" :rules="rules" label-width="120px" label-position="right" status-icon>
        <el-form-item label="服务器名称" prop="serverName">
          <el-input v-model="serverForm.serverName" placeholder="请输入服务器名称" clearable />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="IP地址" prop="serverIp">
              <el-input v-model="serverForm.serverIp" placeholder="请输入IP地址" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="环境类型" prop="environment">
              <el-radio-group v-model="serverForm.environment">
                <el-radio label="压测">压测</el-radio>
                <el-radio label="Q2">Q2</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="验证方式" prop="authType">
              <el-radio-group v-model="serverForm.authType">
                <el-radio label="pw" value="pw">密码</el-radio>
                <el-radio label="key" value="key">密钥</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="serverForm.username" placeholder="请输入用户名" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item v-if="serverForm.authType === 'pw'" label="密码" prop="pw">
              <el-input v-model="serverForm.pw" type="password" placeholder="请输入密码" show-password clearable />
            </el-form-item>
            <div v-if="serverForm.authType === 'key'" class="key-tip-container">
              <div class="key-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>密钥验证仅适用于测试环境</span>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="remark">
          <el-input v-model="serverForm.remark" type="textarea" :rows="3" placeholder="请输入服务器描述信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" class="cancel-button">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading" class="confirm-button">确 认 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 服务器详情对话框 -->
    <el-dialog v-model="detailVisible" title="服务器详情" width="60%" class="detail-dialog">
      <div class="detail-content">
        <div class="detail-header">
          <div class="server-avatar">
            <el-icon>
              <Monitor />
            </el-icon>
          </div>
          <div class="server-title">{{ serverDetail!.serverName }}</div>
        </div>

        <el-divider content-position="left">基本信息</el-divider>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ serverDetail!.id }}</el-descriptions-item>
          <el-descriptions-item label="服务器名称">{{ serverDetail!.serverName }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ serverDetail!.serverIp }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ serverDetail!.username }}</el-descriptions-item>
          <el-descriptions-item label="环境类型">{{ serverDetail!.environment }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ serverDetail!.remark }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false" class="close-button">关 闭</el-button>
          <el-button type="primary" @click="handleEdit(serverDetail)" class="edit-detail-button">编 辑</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Plus, Refresh, Delete, EditPen, View, Monitor, InfoFilled } from "@element-plus/icons-vue";
import {
  api_addServer,
  api_deleteById,
  api_getByPageInfo,
  api_updateServer,
  ServerInfo
} from "@/api/modules/service-server-operation/server-management";

// 表格数据
const tableData = ref<ServerInfo[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const searchQuery = ref("");
const submitLoading = ref(false);
const totalServers = ref<number>(0);

// 对话框相关
const dialogVisible = ref(false);
const detailVisible = ref(false);
const isEdit = ref(false);
const serverForm = ref<ServerInfo>({
  id: "",
  serverName: "",
  serverIp: "",
  username: "",
  pw: "",
  remark: "",
  authType: "pw",
  environment: "压测" // 新增环境类型字段，默认为压测环境
});

// 表单验证规则
const rules = reactive({
  serverName: [{ required: true, message: "请输入服务器名称", trigger: "blur" }],
  serverIp: [
    { required: true, message: "请输入IP地址", trigger: "blur" },
    {
      pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
      message: "请输入正确的IP地址",
      trigger: "blur"
    }
  ],
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  pw: [{ required: true, message: "请输入密码", trigger: "blur" }],
  authType: [{ required: true, message: "请选择验证方式", trigger: "change" }],
  environment: [{ required: true, message: "请选择环境类型", trigger: "change" }] // 新增环境类型验证规则
});
const serverDetail = ref<ServerInfo>();
const serverFormRef = ref();

// 获取服务器列表
const fetchServerList = () => {
  loading.value = true;
  api_getByPageInfo(currentPage.value, pageSize.value, { serverName: searchQuery.value })
    .then(res => {
      if (res.respCode === 2000) {
        tableData.value = res.respData.pageData;
        totalServers.value = res.respData.totalCount;
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 分页大小变化
const handleSizeChange = val => {
  pageSize.value = val;
  fetchServerList();
};

// 当前页变化
const handleCurrentChange = val => {
  currentPage.value = val;
  fetchServerList();
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchServerList();
};

// 清空搜索
const handleSearchClear = () => {
  searchQuery.value = "";
  currentPage.value = 1;
  fetchServerList();
};

// 重置搜索
const resetSearch = () => {
  searchQuery.value = "";
  currentPage.value = 1;
  fetchServerList();
};

// 打开新增对话框
const openAddDialog = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 查看服务器详情
const handleView = row => {
  serverDetail.value = { ...row };
  detailVisible.value = true;
};

// 编辑服务器
const handleEdit = row => {
  isEdit.value = true;
  Object.assign(serverForm.value, JSON.parse(JSON.stringify(row)));
  console.log(serverForm.value);
  dialogVisible.value = true;
  // 如果详情对话框正在显示，则关闭它
  if (detailVisible.value) {
    detailVisible.value = false;
  }
};

// 删除服务器
const handleDelete = id => {
  ElMessageBox.confirm("确定要删除该服务器信息吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    api_deleteById(id).then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("删除成功");
        fetchServerList();
      } else {
        ElMessage.error(res.respMsg);
      }
    });
  });
};

// 提交表单
const submitForm = async () => {
  if (!serverFormRef.value) return;

  await serverFormRef.value.validate(valid => {
    if (valid) {
      submitLoading.value = true;
      if (isEdit.value) {
        api_updateServer(serverForm.value).then(res => {
          if (res.respCode === 2000) {
            ElMessage.success("更新成功");
          } else {
            ElMessage.error(res.respMsg);
          }
        });
      } else {
        api_addServer(serverForm.value).then(res => {
          if (res.respCode === 2000) {
            ElMessage.success("添加成功");
          } else {
            ElMessage.error(res.respMsg);
          }
        });
      }
    }
  });

  dialogVisible.value = false;
  fetchServerList();
  submitLoading.value = false;
};

// 重置表单
const resetForm = () => {
  serverForm.value.remark = "";
  serverForm.value.authType = "pw";
  serverForm.value.serverName = "";
  serverForm.value.serverIp = "";
  serverForm.value.username = "";
  serverForm.value.pw = "";
  serverForm.value.id = "";
  serverForm.value.environment = "压测"; // 重置环境类型为默认值
  isEdit.value = false;
};

// 初始化加载数据
onMounted(() => {
  fetchServerList();
});
</script>

<style scoped>
.main-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s;
  overflow: hidden;
}

.main-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.title-container {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 28px;
  margin-right: 14px;
  color: var(--el-color-primary);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.header-title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #1a2b42;
  letter-spacing: 0.5px;
}

.server-count {
  margin-top: 5px;
  font-size: 14px;
  color: #606266;
}

.count-num {
  font-weight: bold;
  color: var(--el-color-primary);
  font-size: 16px;
}

.add-button {
  transition: all 0.3s;
  border-radius: 8px;
  font-weight: 500;
}

.add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.search-container {
  margin: 20px 0;
  padding: 20px;
  background-color: #f9fafd;
  border-radius: 12px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
}

.search-input {
  width: 300px;
  border-radius: 8px;
  transition: all 0.3s;
}

.search-input:focus,
.search-input:hover {
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.2);
}

.search-button,
.reset-button {
  border-radius: 8px;
  transition: all 0.3s;
  padding: 8px 16px;
}

.search-button:hover,
.reset-button:hover {
  transform: translateY(-2px);
}

.server-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.server-name {
  display: flex;
  align-items: center;
}

.server-icon {
  color: var(--el-color-primary);
  margin-right: 8px;
  font-size: 18px;
}

.action-button {
  transition: all 0.3s;
}

.action-button:hover {
  transform: translateY(-2px);
}

.view-button:hover {
  background-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.edit-button:hover {
  background-color: #e6a23c;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.delete-button:hover {
  background-color: #f56c6c;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

.pagination-container {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}

.server-dialog,
.detail-dialog {
  border-radius: 12px;
}

.server-dialog :deep(.el-dialog__header),
.detail-dialog :deep(.el-dialog__header) {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.server-dialog :deep(.el-dialog__body),
.detail-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.cancel-button,
.confirm-button,
.close-button,
.edit-detail-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s;
}

.cancel-button:hover,
.close-button:hover {
  background-color: #f2f6fc;
}

.confirm-button:hover,
.edit-detail-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.detail-content {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.server-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e6f7ff, #bae7ff);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.2);
}

.server-avatar .el-icon {
  font-size: 36px;
  color: var(--el-color-primary);
}

.server-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a2b42;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .search-form {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .search-input {
    width: 100%;
    margin-bottom: 10px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.action-group {
  display: flex;
  gap: 8px;
}

.key-tip-container {
  padding: 10px 0;
  margin-left: 120px;
}

.key-tip {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background: linear-gradient(135deg, #e6f7ff, #bae7ff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  border-left: 4px solid var(--el-color-primary);
}

.key-tip .el-icon {
  color: var(--el-color-primary);
  font-size: 18px;
  margin-right: 10px;
}

.key-tip span {
  color: #1a2b42;
  font-size: 14px;
  font-weight: 500;
}
</style>
