export const getCurrentDateYYYYMMDD = () :string => {
  const now = new Date();
  const year = now.getFullYear().toString();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要补零
  const day = String(now.getDate()).padStart(2, '0');        // 日期补零
  return `${year}${month}${day}`;
}

export const getCurrentDateTime = () :string => {
  const now = new Date();
  const year = now.getFullYear().toString();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，补零
  const day = String(now.getDate()).padStart(2, '0');        // 日期补零
  const hours = String(now.getHours()).padStart(2, '0');     // 小时补零
  const minutes = String(now.getMinutes()).padStart(2, '0'); // 分钟补零
  const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒补零
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

// 格式化时间函数
export const formatTime = (timeString: string) => {
  if (!timeString) return '';
  const date = new Date(timeString);
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};