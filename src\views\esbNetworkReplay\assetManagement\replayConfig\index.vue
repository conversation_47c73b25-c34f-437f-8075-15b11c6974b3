<template>
  <div class="container">
    <div class="card">
      <el-form inline>
        <el-form-item>
          <el-button type="info" plain size="small" @click="handleReturn">返回</el-button>
        </el-form-item>
        <el-divider direction="vertical" style="margin-top: -18px"/>
        <el-form-item label="接口ID" style="width: 160px">
          <el-select size="small" disabled v-model="searchCondition.interfaceId"></el-select>
        </el-form-item>
        <el-form-item label="类型" style="width: 120px">
          <el-select size="small" v-model="searchCondition.type" @change="queryVersionNumberList" disabled>
            <el-option key="resp" label="resp" value="resp"/>
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" style="width: 130px">
          <el-select size="small" v-model="searchCondition.versionNumber" @change="queryNameList">
            <el-option v-for="item in filterFormData.versionNumberList" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" style="width: 600px;">
          <el-select size="small" v-model="searchCondition.name">
            <el-option v-for="item in filterFormData.nameList" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="startReplay">开始回放</el-button>
          <el-button type="primary" size="small" @click="queryFieldTree">配置比对忽略</el-button>
          <el-button type="primary" size="small" @click="showSampleXml">查看样例报文</el-button>
          <el-button type="primary" size="small" @click="getLatestSampleXml">获取最新数据</el-button>
          <el-button type="primary" size="small" @click="showReplaceConfig">字段值替换</el-button>
        </el-form-item>
        <el-form-item label="当前接口报文获取时间">
          <el-tag round effect="light" type="primary">{{ sampleXmlUpdateTime }}</el-tag>
        </el-form-item>
      </el-form>
    </div>

    <div class="card" style="margin-top: 0.5%">
      <el-scrollbar>
        <el-tree
          style="margin-left: 25%; max-width: 600px"
          :data="fieldTree"
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          highlight-current
          ref="fieldTreeRef"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span v-if="data.isAdded || data.isAddedGlobal" style="color: #d12b58">{{ node.label }}<el-icon><Warning/></el-icon></span>
              <span v-else>{{ node.label }}</span>
                    <span>
                      <el-button v-if="!data.isAdded" size="small" type="primary" link @click="addIgnoredField(data)">添加</el-button>
                      <el-button v-if="data.isAdded" size="small" type="danger" link @click="removeIgnoredField(data)">删除</el-button>
                      <el-button v-if="!data.isAddedGlobal" size="small" type="warning" link @click="addIgnoredFieldGlobal(data)">添加全局</el-button>
                      <el-button v-if="data.isAddedGlobal" size="small" type="danger" link @click="removeIgnoredFieldGlobal(data)">删除全局</el-button>
                    </span>
            </span>
          </template>
        </el-tree>
      </el-scrollbar>

      <el-dialog v-model="sampleXmlDialog.visible" width="70%" :title="sampleXmlDialog.title" @closed="initSampleDialog" draggable>
        <el-input v-model="sampleXmlDialog.sampleXml" type="textarea" :rows="20" style="white-space: nowrap;"/>
        <el-button type="primary" style="margin-left: 90%; margin-top: 1%" @click="updateSampleXml">保存修改</el-button>
      </el-dialog>

      <el-drawer
        v-model="replaceConfigDrawer.visible"
        title="I am the title"
        :direction="'rtl'"
        style="min-width: 60%"
      >
        <el-tabs>
          <el-tab-pane label="替换规则">
            <el-button type="primary" size="small" style="margin-bottom: 1%" @click="showAddReplaceFieldDialog">新增替换规则</el-button>
            <el-table :data="replaceConfigDrawer.replaceFieldList" border>
              <el-table-column prop="tag" label="字段名" />
              <el-table-column prop="value" label="替换值" />
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="danger" size="small" @click="deleteReplaceField(scope.row.id)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="变量管理">
            <el-button type="primary" size="small" style="margin-bottom: 1%" @click="showAddReplaceVariableDialog">新增变量</el-button>
            <el-table :data="replaceConfigDrawer.replaceVariableList" border>
              <el-table-column prop="name" label="变量名称"/>
              <el-table-column prop="value" label="变量值"/>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="danger" size="small" @click="deleteReplaceVariable(scope.row.id)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-drawer>

      <el-dialog v-model="addReplaceFieldDialog.visible">
        <el-form>
          <el-form-item label="版本号">
            <el-select v-model="addReplaceFieldDialog.versionNumber" filterable clearable @change="queryReplaceNameList">
              <el-option v-for="item in addReplaceFieldDialog.versionNumberList" :key="item" :label="item" :value="item"/>
            </el-select>
          </el-form-item>
          <el-form-item label="名称">
            <el-select v-model="addReplaceFieldDialog.name" filterable clearable @change="queryNodeList">
              <el-option v-for="item in addReplaceFieldDialog.nameList" :key="item" :label="item" :value="item"/>
            </el-select>
          </el-form-item>
          <el-form-item label="字段名">
            <el-select v-model="addReplaceFieldDialog.tag" filterable clearable>
              <el-option v-for="item in addReplaceFieldDialog.nodeList" :key="item" :label="item" :value="item"/>
            </el-select>
          </el-form-item>
          <el-form-item label="替换值">
            <el-input v-model="addReplaceFieldDialog.value"></el-input>
          </el-form-item>
        </el-form>
        <el-button style="margin-left: 90%" @click="addReplaceField">添加</el-button>
      </el-dialog>

      <el-dialog v-model="addReplaceVariableDialog.visible">
        <el-form>
          <el-form-item label="名称">
            <el-input v-model="addReplaceVariableDialog.name"></el-input>
          </el-form-item>
          <el-form-item label="值">
            <el-input v-model="addReplaceVariableDialog.value"></el-input>
          </el-form-item>
        </el-form>
        <el-button style="margin-left: 90%" @click="addReplaceVariable">添加</el-button>
      </el-dialog>

    </div>
  </div>
</template>

<script setup lang="ts">
import {TransParams} from "@/views/esbNetworkReplay/assetManagement/assetInfo/index.vue";
import {onBeforeMount, ref} from "vue";
import {
  api_downloadSampleXmlFromRemote,
  api_getFieldTree,
  api_getSampleXml,
  api_getValueByCondition, api_updateSampleXmlContent,
  SampleXmlDto
} from "@/api/modules/service-esb-data/sample-xml";
import {ElMessage} from "element-plus";
import {TreeFilterNode} from "@/api/interface";
import {Warning} from "@element-plus/icons-vue";
import {
  api_deleteIgnoredFieldGlobal,
  api_getIgnoredField,
  api_getIgnoredFieldGlobal,
  api_updateIgnoredField
} from "@/api/modules/esbNetworkReplay/compare";
import {api_addIgnoredFieldGlobal} from "@/api/modules/esbNetworkReplay/compare";
import {api_replay} from "@/api/modules/esbNetworkReplay/replay";
import {
  api_addReplaceField, api_addReplaceVariable,
  api_deleteReplaceField, api_deleteReplaceVariable,
  api_getReplaceField, api_getReplaceVariable,
  EsbReplaceField,
  EsbReplaceVariable
} from "@/api/modules/esbNetworkReplay/replace";

interface FilterFormData {
  typeList: string[];
  versionNumberList: string[];
  nameList: string[];
}

interface FieldTree extends TreeFilterNode {
  isAdded?: boolean;
  isAddedGlobal?: boolean;
}

interface ReplaceConfigDrawer {
  visible: boolean;
  replaceFieldList: EsbReplaceField[];
  replaceVariableList: EsbReplaceVariable[];
}

interface AddReplaceFieldDialog {
  visible: boolean;
  title: string;
  tag: string;
  value: string;
  nodeList: string[];
  versionNumber: string;
  versionNumberList: string[];
  name: string;
  nameList: string[];
}

interface AddReplaceVariableDialog {
  visible: boolean;
  name: string;
  value: string;
}

const emits = defineEmits(["changeComponent"]);
const props = defineProps<{ transParams: TransParams }>();
const searchCondition = ref<SampleXmlDto>({
  interfaceId: props.transParams.selectedInterfaceId,
  name: "",
  versionNumber: "",
  type: "resp",
  sampleXml: ""
});
const filterFormData = ref<FilterFormData>({
  typeList: [],
  versionNumberList: [],
  nameList: [],
});
const sampleXmlUpdateTime = ref<string>("");
const fieldTree = ref<FieldTree[]>([]);
const sampleXmlDialog = ref({
  visible: false,
  sampleXml: "",
  title: "样例报文"
});
const fieldTreeRef = ref();
const replaceConfigDrawer = ref<ReplaceConfigDrawer>({
  visible: false,
  replaceFieldList: [],
  replaceVariableList: [],
});
const addReplaceFieldDialog = ref<AddReplaceFieldDialog>({
  visible: false,
  title: "",
  tag: "",
  value: "",
  nodeList: [],
  versionNumber: "",
  versionNumberList: [],
  name: "",
  nameList: []
});
const addReplaceVariableDialog = ref<AddReplaceVariableDialog>({
  visible: false,
  name: "",
  value: ""
});

const handleReturn = function () {
  emits("changeComponent", "AssetInfo", props.transParams);
};

const initVersionNumber = function () {
  searchCondition.value.versionNumber = "";
  filterFormData.value.versionNumberList = [];
};

const initName = function () {
  searchCondition.value.name = "";
  filterFormData.value.nameList = [];
}

const queryVersionNumberList = function () {
  initVersionNumber();
  initName();
  api_getValueByCondition(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.versionNumberList = [...new Set(res.respData.sampleXmlDtoList.map(item => item.versionNumber))];
      sampleXmlUpdateTime.value = res.respData.updateTime;
    }
  });
};

const queryNameList = function () {
  initName();
  api_getValueByCondition(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.nameList = res.respData.sampleXmlDtoList.map(item => item.name);
      sampleXmlUpdateTime.value = res.respData.updateTime;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const startReplay = function () {
  api_replay({
    interfaceId: searchCondition.value.interfaceId,
    versionNumber: searchCondition.value.versionNumber
    }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("回放成功");
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
};

const queryFieldTree = async function () {
  await api_getFieldTree(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      fieldTree.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
  queryIgnoredField();
  queryIgnoredFieldGlobal();
};

const queryIgnoredField = function () {
  api_getIgnoredField(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      updateTreeWithIds(fieldTree.value[0], res.respData, false);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
 });
};

const queryIgnoredFieldGlobal = function () {
  api_getIgnoredFieldGlobal().then(res => {
    if (res.respCode === 2000) {
      updateTreeWithIds(fieldTree.value[0], res.respData, true);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const addIgnoredField = function (data) {
  data.isAdded = true;
  updateIgnoredField();
};

const removeIgnoredField = function (data) {
  data.isAdded = false;
  updateIgnoredField();
};

const addIgnoredFieldGlobal = function (data: FieldTree) {
  api_addIgnoredFieldGlobal(data.id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("添加全局成功");
      queryIgnoredFieldGlobal();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const removeIgnoredFieldGlobal = function (data) {
  api_deleteIgnoredFieldGlobal(data.id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除全局成功");
      queryIgnoredFieldGlobal();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const updateIgnoredField = function () {
  const tree = fieldTreeRef.value.data[0];
  const addedNodeList = getAddedNode(tree);
  api_updateIgnoredField({
    fieldList: addedNodeList,
    interfaceId: searchCondition.value.interfaceId,
  }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("修改成功");
      queryIgnoredField();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const getAddedNode = function (tree: any) {
  let result: string[] = [];

  function traverse(node: any) {
    if (node.isAdded) {
      result.push(node.id);
    }

    if (node.children && node.children.length > 0) {
      node.children.forEach(child => traverse(child));
    }
  }

  traverse(tree);
  return result;
};

const updateTreeWithIds = function (tree: FieldTree, ids: string[], isGlobal: boolean): void {
  // 如果当前节点的 id 在数组中，则将 isAdded 设置为 true
  if (isGlobal) {
    tree.isAddedGlobal = ids.includes(tree.id);
  } else {
    tree.isAdded = ids.includes(tree.id);
  }

  // 如果有子节点，递归调用函数处理每个子节点
  if (tree.children && tree.children.length > 0) {
    tree.children.forEach(child => updateTreeWithIds(child, ids, isGlobal));
  }
};

const showSampleXml = function () {
  sampleXmlDialog.value.visible = true;
  querySampleXMl();
};

const querySampleXMl = function () {
  api_getSampleXml(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      sampleXmlDialog.value.sampleXml = res.respData.sampleXml;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
};

const initSampleDialog = function () {
  sampleXmlDialog.value.sampleXml = "";
  sampleXmlDialog.value.title = "";
};

const getLatestSampleXml = async function () {
  initVersionNumber();
  initName();
  initSampleDialog();
  fieldTree.value = [];
  await api_downloadSampleXmlFromRemote(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("获取最新数据成功");
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
  queryVersionNumberList();
};

const updateSampleXml = function () {
  api_updateSampleXmlContent({
    interfaceId: searchCondition.value.interfaceId,
    sampleXml: sampleXmlDialog.value.sampleXml,
    name: searchCondition.value.name,
    type: searchCondition.value.type,
    versionNumber: searchCondition.value.versionNumber
  }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("修改成功");
      querySampleXMl();
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    sampleXmlDialog.value.visible = false;
  })
};

const showReplaceConfig = function () {
  replaceConfigDrawer.value.visible = true;
  queryReplaceField();
  queryReplaceVariable();
};

const queryReplaceField = function () {
  api_getReplaceField(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      replaceConfigDrawer.value.replaceFieldList = res.respData;
    }
  })
};

const queryReplaceVariable = function () {
  api_getReplaceVariable().then(res => {
    if (res.respCode === 2000) {
      replaceConfigDrawer.value.replaceVariableList = res.respData;
    }
  });
}

const deleteReplaceField = function (id: string) {
  api_deleteReplaceField(id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除成功");
      queryReplaceField();
    }
  });
};

const deleteReplaceVariable = function (id: string) {
  api_deleteReplaceVariable(id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除成功");
      queryReplaceVariable();
    }
  });
};

const showAddReplaceFieldDialog = function () {
  addReplaceFieldDialog.value.visible = true;
  queryReplaceVersionNumberList();
};

const queryReplaceVersionNumberList = function () {
  api_getValueByCondition({
    interfaceId: searchCondition.value.interfaceId,
    type: "reqt",
    versionNumber: "",
    name: "",
    sampleXml: ""
  }).then(res => {
    if (res.respCode === 2000) {
      addReplaceFieldDialog.value.versionNumberList = [...new Set(res.respData.sampleXmlDtoList.map(item => item.versionNumber))];
    }
  });
};

const queryReplaceNameList = function () {
  api_getValueByCondition({
    interfaceId: searchCondition.value.interfaceId,
    type: "reqt",
    versionNumber: addReplaceFieldDialog.value.versionNumber,
    name: "",
    sampleXml: ""
  }).then(res => {
    if (res.respCode === 2000) {
      addReplaceFieldDialog.value.nameList = res.respData.sampleXmlDtoList.map(item => item.name);
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
};

const queryNodeList = function () {
  api_getFieldTree({
    interfaceId: searchCondition.value.interfaceId,
    versionNumber: addReplaceFieldDialog.value.versionNumber,
    name: addReplaceFieldDialog.value.name,
    sampleXml: "",
    type: "reqt"
  }).then(res => {
    if (res.respCode === 2000) {
      addReplaceFieldDialog.value.nodeList = getAllId(res.respData[0]);
    }
  })
}

function getAllId(node: any) {
  const ids: any = [];

  function traverse(node) {
    if (node.label) {
      ids.push(node.id);
    }
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        traverse(child);
      }
    }
  }

  traverse(node);
  return ids;
}

const addReplaceField = function () {
  api_addReplaceField({
    interfaceId: searchCondition.value.interfaceId,
    tag: addReplaceFieldDialog.value.tag,
    value: addReplaceFieldDialog.value.value
  }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("添加成功");
      queryReplaceField();
    }
  });
};

const showAddReplaceVariableDialog = function () {
  addReplaceVariableDialog.value.visible = true;
};

const addReplaceVariable = function () {
  api_addReplaceVariable({
    name: addReplaceVariableDialog.value.name,
    value: addReplaceVariableDialog.value.value
  }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("添加成功");
      queryReplaceVariable();
    }
  });
};

onBeforeMount(() => {
  queryVersionNumberList();
})

</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
