<template>
  <div class="container" v-loading="containerLoading">

    <div class="card">
      接口号：
      <el-input
        placeholder="请输入接口号"
        v-model="searchCondition.interfaceId"
        style="width: 170px;"
        @change="initDeduplicateField"
      ></el-input>
      <el-button style="margin-left: 1%" @click="pullEsbData">拉取报文</el-button>
      <el-button style="margin-left: 1%" @click="getDataCount">查询报文数量</el-button>
      <span style="margin-left: 1%">该接口已拉取报文 {{ infoDataCount ? infoDataCount : '-' }} 个</span>
      <el-button @click="deleteByInterfaceId" style="margin-left: 1%">删除该接口报文</el-button>
    </div>

    <div class="card" style="margin-top: 1%">
      <el-form inline>
        <el-form-item label="接口id" style="width: 170px">
          <el-input
            placeholder="请输入接口号"
            v-model="searchCondition.interfaceId"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型" style="width: 120px">
          <el-select size="small" v-model="searchCondition.type" @change="queryVersionNumberList">
            <el-option key="reqt" label="reqt" value="reqt"/>
            <el-option key="resp" label="resp" value="resp"/>
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" style="width: 130px">
          <el-select size="small" v-model="searchCondition.versionNumber" @change="queryNameList">
            <el-option v-for="item in filterFormData.versionNumberList" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" style="width: 600px;">
          <el-select size="small" v-model="searchCondition.name" @change="queryFieldTree">
            <el-option v-for="item in filterFormData.nameList" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="showSampleXml">查看样例报文</el-button>
          <el-button type="primary" size="small" @click="getLatestSampleXml">获取最新数据</el-button>
        </el-form-item>
        <el-form-item label="当前样例报文获取时间">
          <el-tag round effect="light" type="primary">{{ sampleXmlUpdateTime }}</el-tag>
        </el-form-item>
      </el-form>
    </div>

    <div class="card" style="max-height: 30rem; margin-top: 1%">
      <el-scrollbar>
        <el-tree
          style="margin-left: 25%; max-width: 600px"
          :data="fieldTree"
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          highlight-current
          ref="fieldTreeRef"
          @node-click="selectField"
        >
          <template #default="{ node }">
        <span class="custom-tree-node">
          <span>{{ node.label }}</span>
        </span>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>

    <div class="card" style="margin-top: 1%">
      <el-button @click="startDeduplicate">开始去重</el-button>
    </div>

    <div class="card" style="margin-top: 1%">
      <el-form inline>
        <el-form-item label="接口ID">
          <el-input placeholder="请输入接口ID" v-model="tableSearch.interfaceId" @blur="getField"></el-input>
        </el-form-item>
        <el-form-item label="标签" style="width: 350px">
          <el-select v-model="tableSearch.field" filterable clearable>
            <el-option v-for="item in tableSearch.fieldList" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="searchResult">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-popconfirm @confirm="deleteByCondition" :title="'确认要删除吗？'">
            <template #reference>
              <el-button type="danger">删除</el-button>
            </template>
          </el-popconfirm>
        </el-form-item>
      </el-form>
      <el-table :data="tableData">
        <el-table-column label="ID" prop="id"></el-table-column>
        <el-table-column label="接口ID" prop="interfaceId"></el-table-column>
        <el-table-column label="字段" prop="field"></el-table-column>
        <el-table-column label="枚举值" prop="enumValue"></el-table-column>
        <el-table-column label="查看报文">
          <template #default="scope">
            <el-button @click="showContent(scope.row.content)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination layout="prev, pager, next" @change="pageChange" :total="pageInfo.totalCount"/>
    </div>

    <el-dialog v-model="sampleXmlDialog.visible" width="70%" :title="sampleXmlDialog.title" @closed="initSampleDialog" draggable>
      <el-input v-model="sampleXmlDialog.sampleXml" type="textarea" :rows="20" style="white-space: nowrap;"/>
    </el-dialog>

    <el-dialog v-model="contentDialog.visible" width="70%" :title="contentDialog.title" @closed="initContentDialog" draggable>
      <el-input v-model="contentDialog.content" type="textarea" :rows="20" style="white-space: nowrap;"/>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {SampleXmlDto} from "@/api/modules/service-esb-data/sample-xml";
import {ElMessage} from "element-plus";
import {TreeFilterNode} from "@/api/interface";
import vkbeautify from "vkbeautify";
import {api_getValueByCondition, api_getFieldTree, api_getSampleXml, api_downloadSampleXmlFromRemote} from "@/api/modules/service-esb-data/sample-xml";
import {
  api_countInfoByInterfaceId,
  api_deduplicate, api_deleteByCondition,
  api_deleteByInterfaceId,
  api_getDataPaged,
  api_getFieldByInterfaceId,
  api_insertToDbByIntfId
} from "@/api/modules/service-esb-data/esb-db-change";

interface FilterFormData {
  typeList: string[];
  versionNumberList: string[];
  nameList: string[];
}

interface FieldTree extends TreeFilterNode {
  isAdded?: boolean;
  isAddedGlobal?: boolean;
}

const searchCondition = ref<SampleXmlDto>({
  interfaceId: "",
  name: "",
  versionNumber: "",
  type: "",
  sampleXml: ""
});
const filterFormData = ref<FilterFormData>({
  typeList: [],
  versionNumberList: [],
  nameList: []
});
const sampleXmlUpdateTime = ref();
const fieldTree = ref<FieldTree[]>([]);
const fieldTreeRef = ref();
const sampleXmlDialog = ref({
  visible: false,
  sampleXml: "",
  title: "样例报文"
});
const deduplicateField = ref("");
const infoDataCount = ref();
const containerLoading = ref(false);
const tableData = ref([]);
const tableSearch = ref({
  interfaceId: "",
  field: "",
  fieldList: []
});
const contentDialog = ref({
  visible: false,
  title: "请求报文",
  content: ""
});
const pageInfo = ref({
  pageNo: 1,
  pageSize: 10,
  totalCount: 0,
  pageCount: 0
});

const queryVersionNumberList = function () {
  initVersionNumber();
  initName();
  api_getValueByCondition(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.versionNumberList = [...new Set(res.respData.sampleXmlDtoList.map(item => item.versionNumber))];
      sampleXmlUpdateTime.value = res.respData.updateTime;
    }
  });
}

const queryNameList = function () {
  initName();
  api_getValueByCondition(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      filterFormData.value.nameList = res.respData.sampleXmlDtoList.map(item => item.name);
      sampleXmlUpdateTime.value = res.respData.updateTime;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
}

const queryFieldTree = async function () {
  await api_getFieldTree(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      fieldTree.value = res.respData;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
}

const showSampleXml = function () {
  sampleXmlDialog.value.visible = true;
  querySampleXMl();
}

const querySampleXMl = function () {
  api_getSampleXml(searchCondition.value).then(res => {
    if (res.respCode === 2000) {
      sampleXmlDialog.value.sampleXml = res.respData.sampleXml;
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  })
}

const initSampleDialog = function () {
  sampleXmlDialog.value.sampleXml = "";
  sampleXmlDialog.value.title = "";
}

const getLatestSampleXml = async function () {
  initVersionNumber();
  initName();
  initSampleDialog();
  fieldTree.value = [];
  await api_downloadSampleXmlFromRemote(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("获取最新数据成功");
    }
    if (res.respCode === 5000) {
      ElMessage.error(res.respMsg);
    }
  });
  queryVersionNumberList();
};

const initVersionNumber = function () {
  searchCondition.value.versionNumber = "";
  filterFormData.value.versionNumberList = [];
}

const initName = function () {
  searchCondition.value.name = "";
  filterFormData.value.nameList = [];
}

const selectField = function (selectNode: FieldTree) {
  deduplicateField.value = selectNode.id;
}

const initDeduplicateField = function () {
  deduplicateField.value = "";
}

const startDeduplicate = function () {
  api_deduplicate(searchCondition.value.interfaceId, deduplicateField.value).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("去重成功");
    }
  });
  ElMessage.info("去重开始");
}

const pullEsbData = function () {
  api_insertToDbByIntfId(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("拉取报文成功");
    }
  });
  ElMessage.info("开始拉取报文");
}

const getDataCount = async function () {
  containerLoading.value = true;
  await api_countInfoByInterfaceId(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      infoDataCount.value = res.respData;
      ElMessage.success("查询成功");
    }
  }).finally(() => {
    containerLoading.value = false;
  })
}

const deleteByInterfaceId = function () {
  api_deleteByInterfaceId(searchCondition.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除成功");
    }
  });
  ElMessage.info("开始删除报文");
}

watch(() => searchCondition.value.interfaceId, () => {
  initName();
  initVersionNumber();
  searchCondition.value.type = "";
  infoDataCount.value = '-';
  sampleXmlUpdateTime.value = '';
});

const showContent = function (content) {
  contentDialog.value.visible = true;
  contentDialog.value.content = vkbeautify.xml(content);
}

const getField = function () {
  api_getFieldByInterfaceId(tableSearch.value.interfaceId).then(res => {
    if (res.respCode === 2000) {
      tableSearch.value.fieldList = res.respData;
    }
  });
}

const searchResult = function () {
  api_getDataPaged({
    pageNo: pageInfo.value.pageNo,
    pageSize: pageInfo.value.pageSize,
    interfaceId: tableSearch.value.interfaceId,
    field: tableSearch.value.field,
  }).then(res => {
    if (res.respCode === 2000) {
      tableData.value = res.respData.pageData;
      pageInfo.value.pageNo = res.respData.pageNo;
      pageInfo.value.pageSize = res.respData.pageSize;
      pageInfo.value.totalCount = res.respData.totalCount;
    }
  });
}

const initContentDialog = function () {
  contentDialog.value.content = "";
}

const pageChange = function (currNo) {
  pageInfo.value.pageNo = currNo;
  searchResult();
}

const deleteByCondition = function () {
  api_deleteByCondition({
    interfaceId: tableSearch.value.interfaceId,
    field: tableSearch.value.field,
  }).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除成功");
    }
  });
  ElMessage.info("删除开始");
}

</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
