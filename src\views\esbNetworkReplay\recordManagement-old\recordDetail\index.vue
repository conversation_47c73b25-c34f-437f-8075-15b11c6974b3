<template>
  <div class="container">
    <div class="card">

      <el-form inline class="filter-form">
        <el-form-item>
          <el-button type="info" plain size="small" @click="handleBack">返回</el-button>
        </el-form-item>
        <el-divider direction="vertical" style="margin-top: -18px"/>
        <el-form-item class="item-csmrId" label="请求方ID">
          <el-select v-model="infoDetailSearchCondition.csmrId" clearable filterable size="small">
            <el-option v-for="item in filterFormData.csmrIdList" :key="item.csmrId" :label="item.csmrId" :value="item.csmrId"/>
          </el-select>
        </el-form-item>
        <el-form-item class="item-versionNumber" label="服务提供方版本号">
          <el-select v-model="infoDetailSearchCondition.versionNumber" clearable filterable size="small">
            <el-option
              v-for="item in filterFormData.versionNumberList"
              :key="item.versionNumber"
              :label="item.versionNumber"
              :value="item.versionNumber"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-transCde" label="交易代码">
          <el-select v-model="infoDetailSearchCondition.transCde" clearable filterable size="small">
            <el-option
              v-for="item in filterFormData.transCdeList"
              :key="item.transCde"
              :label="item.transCde"
              :value="item.transCde"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-esbRespCode" label="ESB响应码">
          <el-select v-model="infoDetailSearchCondition.esbRespCode" clearable filterable size="small">
            <el-option
              v-for="item in filterFormData.esbRespCodeList"
              :key="item.esbRespCode"
              :label="item.esbRespCode"
              :value="item.esbRespCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-esbRespMsg" label="ESB响应信息">
          <el-select v-model="infoDetailSearchCondition.esbRespMsg" clearable filterable size="small">
            <el-option
              v-for="item in filterFormData.esbRespMsgList"
              :key="item.esbRespMsg"
              :label="item.esbRespMsg"
              :value="item.esbRespMsg"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-channelId" label="渠道ID">
          <el-select v-model="infoDetailSearchCondition.channelId" clearable filterable size="small">
            <el-option
              v-for="item in filterFormData.channelIdList"
              :key="item.channelId"
              :label="item.channelId"
              :value="item.channelId"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="item-timeRange" label="时间范围">
          <el-date-picker
            v-model="infoDetailSearchCondition.selectedTime"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            size="small"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search" size="small">查询</el-button>
        </el-form-item>
      </el-form>

      <el-table class="info-detail-table" :data="infoDetailTableData.data" border v-loading="infoDetailTableData.loading" highlight-current-row>
        <el-table-column prop="recordInfoId" label="录制ID" width="300" fixed/>
        <el-table-column prop="csmrId" label="请求方ID" width="90"/>
        <el-table-column prop="versionNumber" label="版本号" width="70"/>
        <el-table-column prop="transCde" label="交易代码" width="100"/>
        <el-table-column prop="channelId" label="渠道ID" width="100"/>
        <el-table-column prop="esbRespCode" label="响应码" width="150" show-overflow-tooltip/>
        <el-table-column prop="esbRespMsg" label="响应信息" show-overflow-tooltip width="200"/>
        <el-table-column prop="requestTime" label="请求时间" width="200"/>
        <el-table-column prop="" label="操作" fixed="right" min-width="230">
          <template #default="scope">
            <el-button type="primary" link size="small" @click="showRequestBody(scope.row.recordInfoId)">查看请求</el-button>
            <el-button type="primary" link size="small" @click="showResponseBody(scope.row.recordInfoId)">查看响应</el-button>
            <el-button type="success" link size="small" disabled>回放</el-button>
            <el-button type="danger" link size="small" disabled>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        layout="prev, pager, next, jumper"
        v-model:current-page="infoDetailTableData.pageNo"
        :page-size="infoDetailTableData.pageSize"
        :total="infoDetailTableData.totalCount"
        @change="pageChange"
      />

      <el-dialog
        class="detail-dialog"
        v-model="detailDialog.visible"
        :title="detailDialog.title"
        width="60%"
        @closed="initDialogData"
      >
        <el-scrollbar>
          <div style="height: 60vh">
            <pre v-text="detailDialog.data"/>
          </div>
        </el-scrollbar>
      </el-dialog>

    </div>
  </div>
</template>

<script setup lang="ts">
import {onBeforeMount, ref, watch} from "vue";
import {
  api_getPagedRecordInfoDetail, api_getRecordDetailFieldValue,
  api_getRecordInfoFieldValue, InfoDetailDto,
  InfoDetailSearchCondition,
  RecordSearchFilterDto
} from "@/api/modules/esbNetworkReplay/record";
import vkbeautify from "vkbeautify";
import {PropsParams} from "@/views/esbNetworkReplay/recordManagement-old/recordInfo/index.vue";

const emits = defineEmits(["changeComponent"]);
const props = defineProps<{ params: PropsParams }>();

interface FilterFormData {
  csmrIdList: RecordSearchFilterDto[];
  versionNumberList: RecordSearchFilterDto[];
  transCdeList: RecordSearchFilterDto[];
  esbRespCodeList: RecordSearchFilterDto[];
  esbRespMsgList: RecordSearchFilterDto[];
  channelIdList: RecordSearchFilterDto[];
}

interface InfoDetailTableData {
  data: InfoDetailDto[];
  pageNo: number;
  pageSize: number;
  totalCount: number;
  pageCount: number;
  loading: boolean;
}

const filterFormData = ref<FilterFormData>({
  csmrIdList: [],
  versionNumberList: [],
  transCdeList: [],
  esbRespCodeList: [],
  esbRespMsgList: [],
  channelIdList: [],
});

const selectedInterfaceId = ref<string>(props.params.selectedInterfaceId);
const infoDetailSearchCondition = ref<InfoDetailSearchCondition>({
  interfaceId: selectedInterfaceId.value,
  pageNo: 1,
  pageSize: 10
});

const infoDetailTableData = ref<InfoDetailTableData>({
  data: [],
  pageNo: 1,
  pageSize: 10,
  totalCount: 0,
  pageCount: 0,
  loading: false
});

const detailDialog = ref<{ visible: boolean; data?: string; title: string; }>({
  visible: false,
  title: "",
});

const handleBack = function () {
  emits("changeComponent", "RecordInfo", {carriedInfoSearchCondition: props.params.carriedInfoSearchCondition});
}

const queryCsmrIdList = function () {
  api_getRecordInfoFieldValue("CSMR_ID", selectedInterfaceId.value).then((res) => {
    if (res.respCode === 2000) {
      filterFormData.value.csmrIdList = res.respData;
    }
  })
}

const queryVersionNumberList = function () {
  api_getRecordInfoFieldValue("VERSION_NUMBER", selectedInterfaceId.value).then((res) => {
    if (res.respCode === 2000) {
      filterFormData.value.versionNumberList = res.respData;
    }
  })
}

const queryTransCdeList = function () {
  api_getRecordInfoFieldValue("TRANS_CDE", selectedInterfaceId.value).then((res) => {
    if (res.respCode === 2000) {
      filterFormData.value.transCdeList = res.respData;
    }
  })
}

const queryEsbRespCodeList = function () {
  api_getRecordInfoFieldValue("ESB_RESP_CODE", selectedInterfaceId.value).then((res) => {
    if (res.respCode === 2000) {
      filterFormData.value.esbRespCodeList = res.respData;
    }
  })
}

const queryEsbRespMsgList = function () {
  api_getRecordInfoFieldValue("ESB_RESP_MSG", selectedInterfaceId.value).then((res) => {
    if (res.respCode === 2000) {
      filterFormData.value.esbRespMsgList = res.respData;
    }
  })
}

const queryChannelIdList = function () {
  api_getRecordInfoFieldValue("CHANNEL_ID", selectedInterfaceId.value).then((res) => {
    if (res.respCode === 2000) {
      filterFormData.value.channelIdList = res.respData;
    }
  })
}

const search = async function () {
  infoDetailTableData.value.loading = true;
  await api_getPagedRecordInfoDetail(infoDetailSearchCondition.value).then(res => {
    if (res.respCode === 2000) {
      infoDetailTableData.value.data = res.respData.pageData;
      infoDetailTableData.value.pageNo = res.respData.pageNo;
      infoDetailTableData.value.pageSize = res.respData.pageSize;
      infoDetailTableData.value.totalCount = res.respData.totalCount;
      infoDetailTableData.value.pageCount = res.respData.pageCount;
    }
  }).finally(() => {
    infoDetailTableData.value.loading = false;
  });
}

const showRequestBody = function (infoId: string) {
  detailDialog.value.title = "请求报文";
  detailDialog.value.visible = true;
  queryRequestBody(infoId);
}

const queryRequestBody = async function (infoId: string) {
  await api_getRecordDetailFieldValue(infoId, "REQUEST_BODY").then(res => {
    if (res.respCode === 2000) {
      detailDialog.value.data = vkbeautify.xml(res.respData.requestBody);
    }
  })
}

const showResponseBody = function (infoId: string) {
  detailDialog.value.title = "响应报文";
  detailDialog.value.visible = true;
  queryResponseBody(infoId);
}

const queryResponseBody = async function (infoId: string) {
  await api_getRecordDetailFieldValue(infoId, "RESPONSE_BODY").then(res => {
    if (res.respCode === 2000) {
      detailDialog.value.data = vkbeautify.xml(res.respData.responseBody);
    }
  })
}

const initDialogData = function () {
  detailDialog.value.data = undefined;
}

const pageChange = function (pageNo: number) {
  infoDetailSearchCondition.value.pageNo = pageNo;
  search();
}

const refresh = function () {
  queryCsmrIdList();
  queryVersionNumberList();
  queryTransCdeList();
  queryEsbRespCodeList();
  queryEsbRespMsgList();
  queryChannelIdList();
  search();
}

watch(() => infoDetailSearchCondition.value.selectedTime, (newValue) => {
  if (!newValue) {
    infoDetailSearchCondition.value.startTime = undefined;
    infoDetailSearchCondition.value.endTime = undefined;
    return;
  }
  infoDetailSearchCondition.value.startTime = newValue[0];
  infoDetailSearchCondition.value.endTime = newValue[1];
}, {immediate: true});

onBeforeMount(() => {
  if (props.params.carriedInfoSearchCondition.selectedTime) {
    infoDetailSearchCondition.value.selectedTime = props.params.carriedInfoSearchCondition.selectedTime;
  }
  refresh();
});

defineExpose({refresh})

</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
