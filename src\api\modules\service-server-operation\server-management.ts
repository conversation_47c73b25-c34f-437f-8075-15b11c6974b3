import http from "@/api";
import { SERVICE_SERVER_OPERATION } from "@/api/config/servicePort";
import { Page } from "@/api/interface";

export interface ServerInfo {
  id?: string;
  serverName?: string;
  environment?: string;
  serverIp?: string;
  username?: string;
  pw?: string;
  authType?: string;
  remark?: string;
}

export const api_addServer = (serverInfo: ServerInfo) => {
  return http.post(SERVICE_SERVER_OPERATION + "/serverManagement/addServer", serverInfo);
};

export const api_getByPageInfo = (pageNo: number, pageSize: number, serverInfo: ServerInfo) => {
  return http.post<Page<ServerInfo[]>>(
    `${SERVICE_SERVER_OPERATION}/serverManagement/getByPageInfo?pageNo=${pageNo}&pageSize=${pageSize}`,
    serverInfo
  );
};

export const api_getByCondition = (serverInfo: ServerInfo) => {
  return http.post<ServerInfo[]>(SERVICE_SERVER_OPERATION + "/serverManagement/getByCondition", serverInfo);
};

export const api_deleteById = (id: string) => {
  return http.get(SERVICE_SERVER_OPERATION + "/serverManagement/deleteById", { id });
};

export const api_updateServer = (serverInfo: ServerInfo) => {
  return http.post(`${SERVICE_SERVER_OPERATION}/serverManagement/updateById`, serverInfo);
};
