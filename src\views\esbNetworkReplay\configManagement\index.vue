<template>
  <div class="container">
    <el-tabs v-model="currentTab" class="card tab">
      <el-tab-pane label="录制环境配置" name="recordEnvConfig">
        <RecordEnvConfig />
      </el-tab-pane>
      <el-tab-pane label="请求重发" name="requestResend">
        <RequestResend />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import RecordEnvConfig from "@/views/esbNetworkReplay/configManagement/recordEnvConfig/index.vue";
import RequestResend from "@/views/esbNetworkReplay/configManagement/requestResend/index.vue";
import { ref } from "vue";

const currentTab = ref("recordEnvConfig");
</script>

<style scoped lang="scss">
@import "index";
</style>
