<template>
  <div v-loading="cardLoading">
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span class="title">中央军委-查询-证件号</span>
        </div>
      </template>
      
      <el-collapse v-model="activeNames" class="query-form-collapse">
      <el-collapse-item title="BASICINFO" name="basicInfo">
          <el-form label-width="180" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="QQDBS[请求单标识]">
                  <div class="flex-align-center">
                    <el-input v-model="attrValues.basicInfo.qqdbs" style="width: 210px;" />
                    <el-tooltip
                      v-if="attrValues.basicInfo.qqdbs"
                      :content="Base64.encode(attrValues.basicInfo.qqdbs)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="QQCSLX[请求措施类型]">
                  <div class="flex-align-center">
                    <el-select clearable v-model="attrValues.basicInfo.qqcslx" style="width: 200px;">
                <el-option key="01" value="01" label="01-常规查询" />
                <el-option key="02" value="02" label="02-动态查询" />
                <el-option key="03" value="03" label="03-继续动态查询" />
                <el-option key="04" value="04" label="04-解除动态查询" />
              </el-select>
                    <el-tooltip
                      v-if="attrValues.basicInfo.qqcslx"
                      :content="Base64.encode(attrValues.basicInfo.qqcslx)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="ZTLB[查控主体类别]">
                  <div class="flex-align-center">
                    <el-select clearable v-model="attrValues.basicInfo.ztlb" style="width: 200px;">
                <el-option key="01" value="01" label="01-对私" />
                <el-option key="02" value="02" label="02-对公" />
              </el-select>
                    <el-tooltip
                      v-if="attrValues.basicInfo.ztlb"
                      :content="Base64.encode(attrValues.basicInfo.ztlb)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
        </el-form>
      </el-collapse-item>

        <el-collapse-item 
          v-for="(queryMain, index) in attrValues.queryMainList" 
          :key="index"
          :name="'queryMain' + index"
        >
          <template #title>
            <div class="collapse-title">
              <span>QUERYMAIN</span>
            </div>
          </template>
          
          <div class="form-toolbar">
            <div class="template-controls">
              <el-select
                v-model="selectedCommonInfoIdList[index]"
                @change="commonInfoChange(index)"
                size="small"
                placeholder="选择常用信息模板"
                clearable
                style="width: 220px"
              >
                <el-option v-for="item in commonInfoList" :key="item.id" :value="item.id!" :label="item.infoName" />
              </el-select>
              
              <el-button 
                type="primary" 
                size="small" 
                v-if="selectedCommonInfoIdList[index]" 
                @click="updateCommonInfo('QUERYMAIN', index)" 
                plain
              >
                <el-icon><RefreshRight /></el-icon>更新信息
              </el-button>
              
              <el-popconfirm 
                title="确定要删除这条常用信息吗?" 
                v-if="selectedCommonInfoIdList[index]" 
                @confirm="deleteCommonInfo(index)"
                confirm-button-text="删除"
                cancel-button-text="取消"
              >
                <template #reference>
                  <el-button type="danger" size="small" plain>
                    <el-icon><Delete /></el-icon>删除信息
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>
          
          <el-form label-width="180" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="RWLSH[任务流水号]">
                  <div class="flex-align-center">
                    <el-input v-model="queryMain.rwlsh" style="width: 260px;" />
                    <el-tooltip
                      v-if="queryMain.rwlsh"
                      :content="Base64.encode(queryMain.rwlsh)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="ZZLXDM(证件类型)">
                  <div class="flex-align-center">
                    <el-select placeholder="请选择证件类型" clearable v-model="queryMain.zzlxdm" style="width: 200px;">
                      <el-option v-for="item in idType1" :key="item.key" :value="item.key" :label="item.key + ' - ' + item.label" />
                    </el-select>
                    <el-tooltip
                      v-if="queryMain.zzlxdm"
                      :content="Base64.encode(queryMain.zzlxdm)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="ZZHM[证照号码]">
                  <div class="flex-align-center">
              <el-input v-model="queryMain.zzhm" style="width: 260px;" />
                    <el-tooltip
                      v-if="queryMain.zzhm"
                      :content="Base64.encode(queryMain.zzhm)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="ZTMC[主体名称(客户名)]">
                  <div class="flex-align-center">
              <el-input v-model="queryMain.ztmc" style="width: 200px;" />
                    <el-tooltip
                      v-if="queryMain.ztmc"
                      :content="Base64.encode(queryMain.ztmc)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="CXNR[查询内容]">
                  <div class="flex-align-center">
                    <el-select v-model="queryMain.cxnr" clearable style="width: 200px;">
                <el-option key="01" value="01" label="01-账户信息" />
                <el-option key="02" value="02" label="02-交易明细" />
                <el-option key="03" value="03" label="03-账户+交易明细" />
                <el-option key="05" value="05" label="05-理财信息" />
              </el-select>
                    <el-tooltip
                      v-if="queryMain.cxnr"
                      :content="Base64.encode(queryMain.cxnr)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="MXSDLX[明细时段类型]">
                  <div class="flex-align-center">
                    <el-select v-model="queryMain.mxsdlx" clearable style="width: 200px;">
                <el-option key="01" value="01" label="01-开户至今" />
                <el-option key="02" value="02" label="02-当年" />
                <el-option key="03" value="03" label="03-自定义" />
              </el-select>
                    <el-tooltip
                      v-if="queryMain.mxsdlx"
                      :content="Base64.encode(queryMain.mxsdlx)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="MXQSSJ[明细起始时间]">
                  <div class="flex-align-center">
              <el-date-picker
                v-model="queryMain.mxqssj"
                type="date"
                placeholder="选择起始时间"
                format="YYYYMMDD"
                value-format="YYYYMMDD"
                @change="onTimeChange(queryMain)"
                      style="width: 200px;"
              />
                    <el-tooltip
                      v-if="queryMain.mxqssj"
                      :content="Base64.encode(queryMain.mxqssj)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="MXJZSJ[明细截至时间]">
                  <div class="flex-align-center">
              <el-date-picker
                v-model="queryMain.mxjzsj"
                type="date"
                placeholder="选择截至时间"
                format="YYYYMMDD"
                value-format="YYYYMMDD"
                @change="onTimeChange(queryMain)"
                      style="width: 200px;"
              />
                    <el-tooltip
                      v-if="queryMain.mxjzsj"
                      :content="Base64.encode(queryMain.mxjzsj)"
                      placement="top"
                    >
                      <el-text size="small" style="margin-left: 10px; text-decoration: underline">base64</el-text>
                    </el-tooltip>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <div class="bottom-actions">
              <el-button type="success" size="small" @click="saveCommonInfo('QUERYMAIN', queryMain)" plain>
                <el-icon><Download /></el-icon>保存为常用信息
              </el-button>
            </div>
        </el-form>
      </el-collapse-item>
    </el-collapse>

      <div class="card-footer-actions">
        <el-button type="info" plain size="small" @click="initRandomDigits">
          <el-icon><Refresh /></el-icon>刷新随机数
        </el-button>
        <el-button type="primary" size="small" @click="addQueryMain" plain>
          <el-icon><Plus /></el-icon>添加QUERYMAIN
        </el-button>
        <el-button type="danger" size="small" @click="deleteQueryMain" plain v-if="attrValues.queryMainList.length !== 1">
          <el-icon><Delete /></el-icon>删除QUERYMAIN
        </el-button>
      </div>
    </el-card>

    <el-card class="action-card">
      <template #header>
        <div class="card-header">
          <span class="title">操作</span>
        </div>
      </template>
      
      <div class="action-buttons">
        <el-button type="warning" @click="generatePacket">
          <el-icon><Document /></el-icon>生成报文
        </el-button>
        <el-button type="primary" @click="downloadZipPacket" :disabled="!packetZipName">
          <el-icon><Download /></el-icon>下载
        </el-button>
        <el-button type="success" @click="ftpUpload" :disabled="!packetZipName">
          <el-icon><Upload /></el-icon>上传至服务器
        </el-button>
    </div>

      <div v-if="packetZipName" class="file-info">
        <el-tag type="success">文件已生成: {{ packetZipName }}</el-tag>
    </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  api_deleteCommonInfo,
  api_downloadZipPacket, 
  api_editCommonInfo, 
  api_ftpUpload,
  api_generatePacket, 
  api_getCommonInfo,
  api_getXmlStructureTree, 
  api_saveTemplate, 
  EntryDto, 
  PacketCommonInfo,
  XmlTreeDto
} from "@/api/modules/service-integrated-tester/authority-packet";
import { onMounted, ref } from "vue";
import { Base64 } from "js-base64";
import idType1 from "@/data/idType1.json"
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete, Download, Refresh, RefreshRight, Document, Upload } from '@element-plus/icons-vue';

const packetType = "中央军委-查询-证件号";
const templateFolderName = "ZYJWSJSF059H101370201001ZJ23110409593200001204";
const templateXmlName = "SS02F059H101370201001ZJ23110409593200001204.xml";
const ftpRemotePath = "/中央军委审计署/Download";
const templateQueryMain = {
  rwlsh: "",
  zzlxdm: "",
  zzhm: "",
  ztmc: "",
  cxnr: "",
  mxsdlx: "",
  mxqssj: "",
  mxjzsj: ""
};

const lastRandomDigits = ref();
const cardLoading = ref(false);
const activeNames = ref(["basicInfo", "queryMain0"]);
const commonInfoList = ref<PacketCommonInfo[]>([]);
const selectedCommonInfoIdList = ref<(string | undefined) []>([]);
const selectedCommonInfoList = ref<(PacketCommonInfo | undefined) []>([]);
const packetXml = ref<XmlTreeDto>();
const attrValues = ref({
  basicInfo: {
    qqdbs: "",
    qqcslx: "",
    ztlb: ""
  },
  queryMainList: [{ ... templateQueryMain }]
});
const packetZipName = ref<string>();

const initRandomDigits = function () {
  lastRandomDigits.value = "ZJ" + Date.now().toString() + Math.floor(Math.random() * 1000000).toString().padStart(7, '0');
  for (let i = 0; i < attrValues.value.queryMainList.length; i++) {
    const currQueryMain = attrValues.value.queryMainList[i];
    currQueryMain.rwlsh = lastRandomDigits.value + (i+1).toString().padStart(5, '0');
  }
  attrValues.value.basicInfo.qqdbs = lastRandomDigits.value;
};

const queryTemplateXml = function () {
  cardLoading.value = true;
  api_getXmlStructureTree({
    templateFolderName,
    templateXmlName
  }).then(res => {
    if (res.respCode === 2000) {
      packetXml.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    cardLoading.value = false;
  });
};

const rulesCheck = function () {
  if (!attrValues.value.basicInfo.qqdbs) {
    ElMessage.error("QQDBS[请求单标识]为必填项");
    return false;
  }
  if (!attrValues.value.basicInfo.qqcslx) {
    ElMessage.error("QQCSLX[请求措施类型]为必填项");
    return false;
  }
  if (!attrValues.value.basicInfo.ztlb) {
    ElMessage.error("ZTLB[查控主体类别]为必填项");
    return false;
  }
  for (let i = 0; i < attrValues.value.queryMainList.length; i++) {
    const currQueryMain = attrValues.value.queryMainList[i];
    const errMsgPrefix = "第" + (i+1) + "个元素 - ";
    if (!currQueryMain.rwlsh) {
      ElMessage.error(errMsgPrefix + "RWLSH[任务流水号]为必填项");
      return false;
    }
    if (!currQueryMain.zzlxdm) {
      ElMessage.error(errMsgPrefix + "ZZLXDM[证件类型]为必填项");
      return false;
    }
    if (!currQueryMain.zzhm) {
      ElMessage.error(errMsgPrefix + "ZZHM(证件号码)为必填项");
      return false;
    }
    if (!currQueryMain.ztmc) {
      ElMessage.error(errMsgPrefix + "ZTMC[主体名称(客户名)]为必填项");
      return false;
    }
    if (!currQueryMain.cxnr) {
      ElMessage.error( errMsgPrefix + "CXNR[查询内容]为必填项");
      return false;
    }
    if (!currQueryMain.mxsdlx) {
      ElMessage.error(errMsgPrefix + "MXSDLX[明细时段类型]为必填项");
      return false;
    }
    if (currQueryMain.mxsdlx === "03" && !currQueryMain.mxqssj) {
      ElMessage.error(errMsgPrefix + "MXSDLX[明细时段类型]为03时，MXQSSJ[明细起始时间]为必填项");
      return false;
    }
    if (currQueryMain.mxsdlx === "03" && !currQueryMain.mxjzsj) {
      ElMessage.error(errMsgPrefix + "MXSDLX[明细时段类型]为03时，MXJZSJ[明细截至时间]为必填项");
      return false;
    }
    if (currQueryMain.mxqssj > currQueryMain.mxjzsj) {
      ElMessage.error(errMsgPrefix + "MXQSSJ[明细起始时间]应大于MXJZSJ[明细截至时间]");
      return false;
    }
  }
  return true;
}

const assignValue = function () {
  const basicInfo = packetXml.value?.children.find(item => item.tag === "BASICINFO");
  for (let key in attrValues.value.basicInfo) {
    basicInfo!.attributeList.find(item => item.key === key.toUpperCase())!.value = Base64.encode(attrValues.value.basicInfo[key]);
  }

  const templateQueryMainTree = JSON.parse(JSON.stringify(packetXml.value?.children.find(item => item.tag === "QUERYMAINS")?.children[0]));
  for (let i = 0; i < attrValues.value.queryMainList.length; i++) {
    const currQueryMain = attrValues.value.queryMainList[i];
    const currTree = JSON.parse(JSON.stringify(templateQueryMainTree));
    for (let key in currQueryMain) {
      currTree!.attributeList.find((item: EntryDto) => item.key === key.toUpperCase())!.value = Base64.encode(currQueryMain[key]);
    }
    packetXml.value!.children.find(item => item.tag === "QUERYMAINS")!.children[i] = currTree!;
  }
};

const generatePacket = function () {
  cardLoading.value = true;
  if (!rulesCheck()) {
    cardLoading.value = false;
    return;
  }
  assignValue();
  api_generatePacket({
    xmlTreeDto: packetXml.value!,
    templateFolderName,
    templateXmlName,
    lastRandomDigits: lastRandomDigits.value
  }).then(res => {
    if (res.respCode === 2000) {
      packetZipName.value = res.respData;
      ElMessage.success("报文包生成成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    cardLoading.value = false;
  });
};

const downloadZipPacket = function () {
  if (!packetZipName.value) {
    ElMessage.error({ message: "无文件下载，请先生成文件", showClose: true, duration: 10000 });
    return;
  }
  api_downloadZipPacket(packetZipName.value!);
};

const ftpUpload = function () {
  cardLoading.value = true;
  if (!packetZipName.value) {
    ElMessage.warning({ message: "无文件上传，请先生成文件", showClose: true, duration: 10000 });
    cardLoading.value = false;
    return;
  }
  api_ftpUpload(packetZipName.value, ftpRemotePath).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("上传成功");
    } else {
      ElMessage.error({ message: res.respMsg, showClose: true, duration: 10000 });
    }
  }).finally(() => {
    cardLoading.value = false;
  });
};

const initQueryMain = function (listIndex: number) {
  const queryMain = attrValues.value.queryMainList[listIndex];
  queryMain.rwlsh = lastRandomDigits.value + (listIndex + 1).toString().padStart(5, '0');
  queryMain.zzlxdm = "";
  queryMain.zzhm = "";
  queryMain.ztmc = "";
  queryMain.cxnr = "";
  queryMain.mxsdlx = "";
  queryMain.mxqssj = "";
  queryMain.mxjzsj = "";
};

const queryCommonInfo = function (tagName: string) {
  api_getCommonInfo({
    infoName: "",
    packetType,
    tagName,
    infoContent: ""
  }).then(res => {
    if (res.respCode === 2000) {
      commonInfoList.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const commonInfoChange = function (listIndex: number) {
  initQueryMain(listIndex);
  if (!selectedCommonInfoIdList.value[listIndex]) {
    selectedCommonInfoList.value[listIndex] = undefined;
    return;
  }
  selectedCommonInfoList.value[listIndex] = commonInfoList.value.find(item => item.id === selectedCommonInfoIdList.value[listIndex]);
  const currQueryMain = attrValues.value.queryMainList[listIndex];
  const commonInfoJson = JSON.parse(selectedCommonInfoList.value[listIndex]!.infoContent);
  for (let key in commonInfoJson) {
    currQueryMain[key] = commonInfoJson[key];
  }
};

const saveCommonInfo = function (tagName: string, infoContent: Object) {
  let copy = JSON.parse(JSON.stringify(infoContent));
  delete copy.rwlsh;
  delete copy.yrwlsh;
  ElMessageBox.prompt("请输入名称", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(({value}) => {
    cardLoading.value = true;
    api_saveTemplate({
      infoName: value,
      packetType,
      tagName,
      infoContent: JSON.stringify(copy)
    }).then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("信息保存成功");
        queryCommonInfo(tagName);
      } else {
        ElMessage.error(res.respMsg);
      }
    }).finally(() => {
      cardLoading.value = false;
    });
  });
};

const updateCommonInfo = function (tagName: string, listIndex: number) {
  let copyQueryMain = JSON.parse(JSON.stringify(attrValues.value.queryMainList[listIndex]));
  delete copyQueryMain.rwlsh;
  delete copyQueryMain.yrwlsh;
  const newCommonInfo: PacketCommonInfo = {
    id: selectedCommonInfoIdList.value[listIndex],
    infoName: selectedCommonInfoList.value[listIndex]!.infoName,
    packetType,
    tagName,
    infoContent: JSON.stringify(copyQueryMain)
  }
  api_editCommonInfo(newCommonInfo).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("更新成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    queryCommonInfo("QUERYMAIN");
  });
};

const deleteCommonInfo = function (listIndex: number) {
  api_deleteCommonInfo(selectedCommonInfoIdList.value[listIndex]!).then(res => {
    if (res.respCode === 2000) {
      selectedCommonInfoIdList.value[listIndex] = undefined;
      commonInfoChange(listIndex);
      ElMessage.success("删除成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    queryCommonInfo("QUERYMAIN");
  });
};

const onTimeChange = function (queryMain: any) {
  if (!queryMain.mxqssj) {
    queryMain.mxqssj = "";
  }
  if (!queryMain.mxjzsj) {
    queryMain.mxjzsj = "";
  }
};

const addQueryMain = function () {
  attrValues.value.queryMainList.push({... templateQueryMain});
  initQueryMain(attrValues.value.queryMainList.length - 1);
  selectedCommonInfoIdList.value.push(undefined);
  selectedCommonInfoList.value.push(undefined);
};

const deleteQueryMain = function () {
  attrValues.value.queryMainList.pop();
  selectedCommonInfoIdList.value.pop();
  selectedCommonInfoList.value.pop();
};

onMounted(() => {
  queryTemplateXml();
  queryCommonInfo("QUERYMAIN");
  initRandomDigits();
  initQueryMain(0);
});
</script>

<style scoped lang="scss">
.query-card, .action-card {
  margin-bottom: 16px;
  border-radius: 8px;
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .form-info {
    color: #606266;
    font-size: 13px;
    background-color: #f0f2f5;
    padding: 2px 8px;
    border-radius: 4px;
    
    small {
      color: #909399;
      margin-left: 4px;
    }
  }
}

.form-toolbar {
  display: flex;
  margin-bottom: 16px;
  background: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  
  .template-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.query-form {
  margin-top: 10px;
  
  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-picker) {
    width: 100%;
  }
}

.flex-align-center {
  display: flex;
  align-items: center;
  
  .encoding-text {
    margin-left: 10px;
    font-size: 12px;
    color: #909399;
    word-break: break-all;
    max-width: 200px;
  }
}

.bottom-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-start;
}

.card-footer-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.query-form-collapse {
  :deep(.el-collapse-item__header) {
    padding: 0 8px;
    
    &.is-active {
      background-color: #ecf5ff;
      border-bottom-color: #d9ecff;
    }
  }
  
  :deep(.el-collapse-item__wrap) {
    padding: 16px;
    background-color: #fbfbfb;
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.file-info {
  margin-top: 16px;
}
</style>
