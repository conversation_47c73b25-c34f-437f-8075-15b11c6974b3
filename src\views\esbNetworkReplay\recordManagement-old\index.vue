<template>
  <div class="container">
    <ToolBar @refresh-component="refreshComponent" />
    <transition name="fade" mode="out-in">
      <component :is="currComponent" @change-component="changeComponent" :params="carriedParams" ref="comRef" />
    </transition>
  </div>
</template>

<script setup lang="ts">
import ToolBar from "./toolBar/index.vue";
import RecordInfo from "./recordInfo/index.vue";
import RecordDetail from "./recordDetail/index.vue";
import Deduplicate from "./deduplicate/index.vue"
import { ref, shallowRef } from "vue";
import {PropsParams} from "@/views/esbNetworkReplay/recordManagement-old/recordInfo/index.vue";

const currComponent = shallowRef<any>(RecordInfo);
const carriedParams = ref({});
const comRef = ref();

const changeComponent = (componentName: string, params: PropsParams) => {
  carriedParams.value = params;
  switch (componentName) {
    case "RecordInfo":
      currComponent.value = RecordInfo;
      return;
    case "RecordDetail":
      currComponent.value = RecordDetail;
      return;
    case "Deduplicate":
      currComponent.value = Deduplicate;
      return;
    default:
      currComponent.value = RecordInfo;
  }
};

const refreshComponent = async () => {
  await comRef.value.refresh();
}

</script>

<style scoped lang="scss">
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
@import "./index.scss";
</style>
