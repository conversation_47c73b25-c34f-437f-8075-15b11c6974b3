<template>
  <div class="container">
    <div class="card" style="display: flex">
      <el-upload
        v-model:file-list="fileList"
        :action="api_uploadZip()"
        :on-exceed="handleExceed"
        :on-success="handleSuccess"
        :on-remove="() => uploadId = ''"
        :limit="1"
        style="width: 300px"
      >
        <el-button type="primary" plain>上传文件</el-button>
        <template #tip>
          <div class="el-upload__tip">
            请上传准入测试压缩包.zip文件
          </div>
        </template>
      </el-upload>
      <el-text v-if="uploadId" style="margin-left: 100px"><b>上传id：{{ uploadId }}</b></el-text>
    </div>

    <div class="card" style="margin-top: 10px">
      <iframe
        src="http://**************:8888/chat/2d6lRh6XFUNItd9v"
        style="width: 100%; height: 100%; min-height: 700px; border: 0"
      >
      </iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {api_uploadZip} from "@/api/modules/service-llm/access-test-doc";
import {ResultData} from "@/api/interface";
import {ElMessage} from "element-plus";

const fileList = ref([]);
const uploadId = ref("");

const handleSuccess = function (resp: ResultData<string>) {
  uploadId.value = resp.respData;
};

const handleExceed = function () {
  ElMessage.error("超出上传限制，请删除原上传文件后重试");
};
</script>

<style scoped lang="scss">

</style>
