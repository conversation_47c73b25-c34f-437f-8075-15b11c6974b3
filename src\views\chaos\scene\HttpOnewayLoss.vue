<template>
  <div class="container">
    <div class="card" v-loading="injectionLoading">
      <el-form inline>
        <el-form-item label="当前服务器ip" style="width: 370px">
          <el-select v-model="httpOnewayLoss.serverInfoId" clearable placeholder="选择服务器">
            <el-option
              v-for="item in serverList"
              :key="item.id"
              :value="item.id!"
              :label="'[' + item.serverName + ']/[' + item.serverIp + ']/[' + item.username + ']'"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="请求返回服务器" style="width: 200px">
          <el-input v-model="httpOnewayLoss.remoteIp" />
        </el-form-item>
        <el-form-item label="请求返回端口号" style="width: 180px">
          <el-input v-model="httpOnewayLoss.remotePort" clearable />
        </el-form-item>
        <el-form-item>
          <el-button @click="injectHttpOnewayLoss" type="primary">注入故障</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { api_httpOnewayLoss, CommonReqtDto } from "@/api/modules/service-server-operation/chaos";
import { ElMessage } from "element-plus";
import { ServerInfo } from "@/api/modules/service-server-operation/server-management";
import { useRoute } from "vue-router";

const route = useRoute();
// const chaosType = "http-oneway-loss";

const injectionLoading = ref(false);
const httpOnewayLoss = ref({
  serverInfoId: "",
  remoteIp: "",
  remotePort: ""
});
const serverList = ref<ServerInfo[]>([]);

// 接收来自URL参数的服务器ID
watch(
  () => route.query.serverId,
  (newServerId) => {
    if (newServerId) {
      httpOnewayLoss.value.serverInfoId = newServerId as string;
    }
  },
  { immediate: true }
);

const injectHttpOnewayLoss = function () {
  injectionLoading.value = true;
  const reqtParam: CommonReqtDto = {
    param1: httpOnewayLoss.value.serverInfoId,
    param2: httpOnewayLoss.value.remoteIp,
    param3: httpOnewayLoss.value.remotePort
  };
  api_httpOnewayLoss(reqtParam)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("注入成功");
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      injectionLoading.value = false;
    });
};
</script>

<style scoped lang="scss"></style>
