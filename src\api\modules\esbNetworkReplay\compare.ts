import http from "@/api";
import {SERVICE_ENR} from "@/api/config/servicePort";
import {UpdateIgnoredFieldDto} from "@/api/modules/service-esb-data/service-model";

export const api_getIgnoredField = (interfaceId: string) => {
  return http.get<string[]>(SERVICE_ENR + "/compare/getIgnoredField", {interfaceId});
}

export const api_getIgnoredFieldGlobal = () => {
  return http.get<string[]>(SERVICE_ENR + "/compare/getIgnoredFieldGlobal");
}

export const api_updateIgnoredField = (updateIgnoredFieldDto: UpdateIgnoredFieldDto) => {
  return http.post(SERVICE_ENR + "/compare/updateIgnoredField", updateIgnoredFieldDto);
}

export const api_addIgnoredFieldGlobal = (ignoredField: string) => {
  return http.get(SERVICE_ENR + "/compare/addIgnoredFieldGlobal", {ignoredField});
}

export const api_deleteIgnoredFieldGlobal = (ignoredField: string) => {
  return http.get(SERVICE_ENR + "/compare/deleteIgnoredFieldGlobal", {ignoredField});
}
