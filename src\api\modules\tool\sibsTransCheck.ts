import http from "@/api"
import {SERVICE_INTEGRATED_TESTER} from "@/api/config/servicePort";
import {Page} from "@/api/interface";

export interface SibsFileUpload {
  id: string;
  fileType: string;
  uploadFileName: string;
  serverFileName: string;
  filePath: string;
  uploadTime: string;
}

export interface SibsHttpDataInfo {
  id: string;
  contentType: string;
  httpType: string;
  seqNo: string;
  interfaceId: string;
}

export interface SibsHttpData{
  infoId: string;
  data: string;
}

export interface SibsTransCheckResult {
  id: string;
  seqNo: string;
  interfaceId: string;
  checkResult: string;
}

export interface SibsTransCheckFailed {
  id: string;
  failedInfo: string;
}

export interface SibsTransCheckConfig {
  id?: string;
  type: string;
  content: string;
}

export const api_removeUploadFile = (id: string) => {
  return http.get<boolean>(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/removeUploadFile", { id: id })
};

export const api_getUploadFileList = () => {
  return http.get<SibsFileUpload[]>(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/getUploadFileList")
}

export const api_parseToDB = (id: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/parseToDB", { id: id });
}

export const api_clearHttpDataDB = () => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/clearHttpDataDB");
}

export const api_clearCheckResultDB = () => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/clearCheckResultDB");
}

export const api_clearDB = () => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/clearDB");
}

export const api_sibsTransCheck = () => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/sibsTransCheck");
}

export const api_getPagedHttpDataInfo = (pageNo: number, pageSize: number, seqNo?: string, transTimestamp?: string) => {
  return http.get<Page<SibsHttpDataInfo[]>>(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/getPagedHttpDataInfo", { pageNo: pageNo, pageSize: pageSize, seqNo: seqNo, transTimestamp: transTimestamp});
}

export const api_getHttpDataByInfoId = (infoId: string) => {
  return http.get<SibsHttpData>(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/getHttpDataByInfoId", { infoId: infoId });
}

export const api_getPagedTransCheckResult = (pageNo: number, pageSize: number) => {
  return http.get<Page<SibsTransCheckResult[]>>(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/getPagedTransCheckResult", {pageNo, pageSize});
}

export const api_getFailedInfoByResultId = (resultId: string) => {
  return http.get<SibsTransCheckFailed>(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/getFailedInfoByResultId", {resultId});
}

export const api_getCheckConfigListByType = (type: string) => {
  return http.get<SibsTransCheckConfig[]>(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/getCheckConfigListByType", {type});
}

export const api_addCheckConfig = (sibsTransCheckConfig: SibsTransCheckConfig) => {
  return http.post(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/addCheckConfig", sibsTransCheckConfig);
}

export const api_deleteCheckConfigById = (id: string) => {
  return http.get(SERVICE_INTEGRATED_TESTER + "/sibsTransCheck/deleteCheckConfigById", {id});
}
