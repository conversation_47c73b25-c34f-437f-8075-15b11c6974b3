import http from "@/api";
import {SERVICE_ESB_DATA} from "@/api/config/servicePort";
import {TreeFilterNode} from "@/api/interface";

export interface SampleXmlDto {
  interfaceId: string;
  name: string;
  versionNumber: string;
  type: string;
  sampleXml: string;
}

export interface SampleXmlRedisDto {
  sampleXmlDtoList: SampleXmlDto[];
  updateTime: string;
}

export const api_getValueByCondition = function (searchCondition: SampleXmlDto) {
  return http.post<SampleXmlRedisDto>(SERVICE_ESB_DATA + "/sampleXml/getValueByCondition", searchCondition);
}

export const api_getFieldTree = function (searchCondition: SampleXmlDto) {
  return http.post<TreeFilterNode[]>(SERVICE_ESB_DATA + "/sampleXml/getFieldTree", searchCondition);
}

export const api_getSampleXml = function (searchCondition: SampleXmlDto) {
  return http.post<SampleXmlDto>(SERVICE_ESB_DATA + "/sampleXml/getSampleXml", searchCondition);
}

export const api_downloadSampleXmlFromRemote = function (interfaceId: string) {
  return http.get(SERVICE_ESB_DATA + "/sampleXml/downloadSampleXmlFromRemote", {interfaceId});
}

export const api_updateSampleXmlContent = (sampleXmlDto: SampleXmlDto) => {
  return http.post(SERVICE_ESB_DATA + "/sampleXml/updateSampleXmlContent", sampleXmlDto);
}
