import http from "@/api/index";
import {SERVICE_ENR} from "@/api/config/servicePort";

export interface RequestResendDBInfo {
  id?: string;
  databaseName: string;
  connectionUrl: string;
  userName: string;
  password: string;
  tableName: string;
  insertTime?: string;
}

export interface RequestResendForm {
  dbInfoId: string;
  serviceProvider: string[];
  startTime: string;
  endTime: string;
  timeInterval: string;
  sendHost: string;
  threadNum: string;
}

export const api_getRequestResendDBList = () => {
  return http.get<RequestResendDBInfo[]>(SERVICE_ENR + "/config/getRequestResendDBList");
}

export const api_addRequestResendDBInfo = (requestResendDBInfo: RequestResendDBInfo) => {
  return http.post(SERVICE_ENR + "/config/addRequestResendDBInfo", requestResendDBInfo);
}

export const api_updateRequestResendDBInfo = (requestResendDBInfo: RequestResendDBInfo) => {
  return http.post(SERVICE_ENR + "/config/updateRequestResendDBInfo", requestResendDBInfo);
}

export const api_deleteRequestResendDBInfo = (id: string) => {
  return http.get(SERVICE_ENR + "/config/deleteRequestResendDBInfo", { id: id });
}

export const api_testDBConnection = (id: string) => {
  return http.get(SERVICE_ENR + "/config/testDBConnection", { id: id });
}

export const api_requestResend = (requestResendForm: RequestResendForm) => {
  return http.post(SERVICE_ENR + "/config/requestResend", requestResendForm);
}
