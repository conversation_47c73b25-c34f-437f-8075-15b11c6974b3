import http from "@/api";
import {SERVICE_ENR} from "@/api/config/servicePort";

interface ReplayReqtDto {
  interfaceId?: string;
  versionNumber?: string;
  startTime?: string;
  endTime?: string;
}

export interface EsbReplayPlan {
  id: string;
  createTime: string;
  planName: string;
  status: number;
}

export interface EsbReplayInfo {
  id: string;
  replayPlanId: string;
  url: string;
  interfaceId: string;
  status: number;
  requestTime: string;
  responseTime: string;
  versionNumber: string;
}

export interface EsbReplayDetail {
  infoId: string;
  replayRequestBody: string;
  rawResponseBody: string;
  replayResponseBody: string;
}

export const api_replay = (replayReqtDto: ReplayReqtDto) => {
  return http.post(SERVICE_ENR + "/replay/replay", replayReqtDto);
}

export const api_getReplayPlan = () => {
  return http.get<EsbReplayPlan[]>(SERVICE_ENR + "/replay/getReplayPlan");
}

export const api_getReplayInfo = (replayPlanId: string) => {
  return http.get<EsbReplayInfo[]>(SERVICE_ENR + "/replay/getReplayInfo", {replayPlanId});
}

export const api_deleteReplayPlan = (replayPlanId: string) => {
  return http.get(SERVICE_ENR + "/replay/deleteReplayPlan", {replayPlanId});
}

export const api_getReplayDetailFieldValue = (infoId: string, field: string) => {
  return http.get<EsbReplayDetail>(SERVICE_ENR + "/replay/getReplayDetailFieldValue", {infoId, field});
}

export const api_replayInOrder = (replayReqtDto: ReplayReqtDto) => {
  return http.post(SERVICE_ENR + "/replay/replayInOrder", replayReqtDto);
}
