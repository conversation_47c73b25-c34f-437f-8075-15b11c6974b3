<template>
  <div>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>Git 仓库管理</span>
          <div>
            <el-button type="primary" @click="showNewRepoDialog">新增仓库</el-button>
            <el-button @click="showCredentialDialog">凭证管理</el-button>
            <el-button @click="refreshRepositories">刷新列表</el-button>
          </div>
        </div>
      </template>

      <!-- 仓库列表 -->
      <el-table :data="repositories" v-loading="loading" border>
        <el-table-column prop="repositoryName" label="仓库名称" width="230" />
        <el-table-column prop="repositoryComment" label="备注" width="230">
          <template #default="scope">
            <el-input v-if="scope.row.editing" v-model="scope.row.repositoryComment" size="small" />
            <span v-else>
              {{ scope.row.repositoryComment }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="remoteUrl" label="远程地址" show-overflow-tooltip width="300" />
        <el-table-column label="凭证" width="150">
          <template #default="scope">
            <el-select
              v-model="scope.row.credentialId"
              placeholder="选择凭证"
              size="small"
              @change="updateRepoCredential(scope.row)"
            >
              <el-option label="无凭证" value=""></el-option>
              <el-option v-for="cred in credentials" :key="cred.id" :label="cred.name" :value="cred.id"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="branchName" label="分支" width="120" />
        <el-table-column prop="updateTime" label="最后同步时间" width="180" />
        <el-table-column prop="syncStatus" label="同步状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.updateStatus)">
              {{
                scope.row.updateStatus == "success"
                  ? "成功"
                  : scope.row.updateStatus == "failed"
                    ? "失败"
                    : scope.row.updateStatus == "updating"
                      ? "同步中"
                      : "未同步"
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="自动同步" width="160">
          <template #default="scope">
            <el-switch
              v-model="scope.row.autoSync"
              active-text="打开"
              inactive-text="关闭"
              @change="toggleScheduled(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="260" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="toggleEdit(scope.row)" :type="scope.row.editing ? 'success' : ''">
              {{ scope.row.editing ? "保存" : "编辑" }}
            </el-button>
            <el-button size="small" type="primary" @click="syncRepository(scope.row)" :loading="scope.row.syncing">
              同步
            </el-button>
            <el-popconfirm title="确认删除该仓库吗？" @confirm="deleteRepository(scope.row.id)">
              <template #reference>
                <el-button size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增仓库对话框 -->
    <el-dialog v-model="newRepoDialogVisible" title="新增Git本地仓库" width="600px">
      <el-form :model="newRepoForm" :rules="cloneRules" ref="cloneFormRef" label-width="100px">
        <el-form-item label="仓库名称" prop="repositoryName">
          <el-input v-model="newRepoForm!.repositoryName" placeholder="请输入仓库显示名称" />
        </el-form-item>
        <el-form-item label="仓库备注">
          <el-input v-model="newRepoForm!.repositoryComment" placeholder="请输入仓库备注" />
        </el-form-item>
        <el-form-item label="仓库URL" prop="remoteUrl">
          <el-input v-model="newRepoForm!.remoteUrl" placeholder="请输入Git仓库URL" />
        </el-form-item>
        <el-form-item label="凭证">
          <el-select v-model="newRepoForm!.credentialId" placeholder="选择凭证">
            <el-option label="无凭证" value=""></el-option>
            <el-option v-for="cred in credentials" :key="cred.id" :label="cred.name" :value="cred.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分支" prop="branch">
          <el-input v-model="newRepoForm!.branchName" placeholder="默认为主分支" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="newRepoDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddRepo" :loading="cloning">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 凭证管理对话框 -->
    <el-dialog v-model="credentialDialogVisible" title="Git凭证管理" width="800px">
      <div class="credential-actions">
        <el-button type="primary" @click="showAddCredentialDialog">添加凭证</el-button>
      </div>

      <el-table :data="credentials" border style="width: 100%">
        <el-table-column prop="name" label="凭证名称" width="180" />
        <el-table-column prop="description" label="凭证描述" width="180" />
        <el-table-column prop="username" label="用户名" width="300" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="editCredential(scope.row)">编辑</el-button>
            <el-popconfirm title="确认删除该凭证吗？" @confirm="deleteCredential(scope.row.id)">
              <template #reference>
                <el-button size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 添加/编辑凭证对话框 -->
    <el-dialog
      v-model="credentialEditDialogVisible"
      :title="credentialEditMode === 'add' ? '添加凭证' : '编辑凭证'"
      width="600px"
    >
      <el-form :model="credentialForm" :rules="credentialRules" ref="credentialFormRef" label-width="100px">
        <el-form-item label="凭证名称" prop="name">
          <el-input v-model="credentialForm!.name" placeholder="请输入凭证名称" />
        </el-form-item>
        <el-form-item label="凭证描述" prop="description">
          <el-input v-model="credentialForm!.description" placeholder="请输入凭证描述" />
        </el-form-item>

        <el-form-item label="用户名" prop="username">
          <el-input v-model="credentialForm!.username" placeholder="请输入Git用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="credentialForm!.password" type="password" placeholder="请输入Git密码" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="credentialEditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCredential" :loading="savingCredential">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  api_addCredential,
  api_addGitRepoInfo,
  api_deleteCredential,
  api_deleteGitRepo,
  api_getCredentials,
  api_getRepositories,
  api_pullOrClone,
  api_updateCredential,
  api_updateGitRepoInfo,
  GitCredential,
  GitRepoInfo
} from "@/api/modules/service-integrated-tester/git";

interface TableData extends GitRepoInfo {
  autoSync: boolean;
  editing: boolean;
  syncing: boolean;
}

const repositories = ref<TableData[]>([]);
const credentials = ref<GitCredential[]>([]);
const loading = ref(false);
const newRepoDialogVisible = ref(false);
const cloning = ref(false);
const credentialDialogVisible = ref(false);
const credentialEditDialogVisible = ref(false);
const credentialEditMode = ref("add"); // 'add' or 'edit'
const savingCredential = ref(false);
const newRepoForm = ref<GitRepoInfo>();
const credentialForm = ref<GitCredential>();

const cloneRules = {
  repositoryName: [
    { required: true, message: "请输入仓库名称", trigger: "blur" },
    { max: 50, message: "名称不超过50个字符", trigger: "blur" }
  ],
  remoteUrl: [
    { required: true, message: "请输入仓库URL", trigger: "blur" },
    { pattern: /^(https?|git):\/\/.+/i, message: "请输入有效的Git仓库URL", trigger: "blur" }
  ]
};

const credentialRules = {
  name: [
    { required: true, message: "请输入凭证名称", trigger: "blur" },
    { max: 50, message: "名称不超过50个字符", trigger: "blur" }
  ],
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码或Token", trigger: "blur" }],
  privateKey: [{ required: true, message: "请输入SSH私钥内容", trigger: "blur" }]
};

const cloneFormRef = ref(null);
const credentialFormRef = ref(null);

// 获取状态标签类型
const getStatusTagType = status => {
  const map = {
    success: "success",
    failed: "danger",
    updating: "warning"
  };
  return map[status] || "info";
};

// 获取仓库列表
const fetchRepositories = async () => {
  loading.value = true;
  api_getRepositories()
    .then(res => {
      if (res.respCode === 2000) {
        repositories.value = res.respData.map(item => ({
          id: item.id,
          remoteUrl: item.remoteUrl,
          localPath: item.localPath,
          repositoryName: item.repositoryName,
          branchName: item.branchName,
          scheduled: item.scheduled,
          updateTime: item.updateTime,
          repositoryComment: item.repositoryComment,
          credentialId: item.credentialId,
          updateStatus: item.updateStatus,
          autoSync: item.scheduled == "1",
          editing: false,
          syncing: false
        }));
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 获取凭证列表
const fetchCredentials = function () {
  api_getCredentials().then(res => {
    if (res.respCode === 2000) {
      credentials.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

// 刷新仓库列表
const refreshRepositories = () => {
  fetchRepositories();
};

// 显示新增仓库对话框
const showNewRepoDialog = () => {
  newRepoForm.value = {
    id: "",
    remoteUrl: "",
    localPath: "",
    repositoryName: "",
    branchName: "",
    scheduled: "1",
    updateTime: "",
    repositoryComment: "",
    credentialId: "",
    updateStatus: ""
  };
  newRepoDialogVisible.value = true;
};

// 显示凭证管理对话框
const showCredentialDialog = () => {
  fetchCredentials();
  credentialDialogVisible.value = true;
};

// 显示添加凭证对话框
const showAddCredentialDialog = () => {
  credentialEditMode.value = "add";
  credentialForm.value = {
    id: "",
    name: "",
    description: "",
    username: "",
    password: ""
  };
  credentialEditDialogVisible.value = true;
};

// 编辑凭证
const editCredential = (credential: GitCredential) => {
  credentialEditMode.value = "edit";
  credentialForm.value = {
    id: credential.id,
    name: credential.name,
    description: credential.description,
    username: credential.username,
    password: credential.password
  };
  credentialEditDialogVisible.value = true;
};

// 保存凭证
const saveCredential = async () => {
  try {
    savingCredential.value = true;
    if (credentialEditMode.value === "add") {
      api_addCredential(credentialForm.value!).then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("凭证添加成功");
          fetchCredentials();
        } else {
          ElMessage.error(res.respMsg);
        }
      });
    } else {
      // 更新现有凭证
      api_updateCredential(credentialForm.value!).then(res => {
        if (res.respCode === 2000) {
          ElMessage.success("凭证更新成功");
          fetchCredentials();
        } else {
          ElMessage.error(res.respMsg);
        }
      });
    }
    credentialEditDialogVisible.value = false;
  } finally {
    savingCredential.value = false;
  }
};

// 删除凭证
const deleteCredential = function (id: string) {
  api_deleteCredential(id).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("删除成功");
      fetchCredentials();
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

// 更新仓库凭证
const updateRepoCredential = function (repo: TableData) {
  api_updateGitRepoInfo(repo).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("保存成功");
      fetchRepositories();
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

// 确认添加仓库
const confirmAddRepo = function () {
  api_addGitRepoInfo(newRepoForm.value!)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("保存成功");
        fetchRepositories();
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      newRepoDialogVisible.value = false;
    });
};

// 切换编辑状态
const toggleEdit = (repo: TableData) => {
  if (repo.editing) {
    saveEdit(repo);
  } else {
    repo.editing = true;
  }
};

// 保存编辑
const saveEdit = (repo: TableData) => {
  // 验证名称
  if (!repo.repositoryName || repo.repositoryName.trim().length === 0) {
    throw new Error("仓库名称不能为空");
  }
  api_updateGitRepoInfo(repo).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("保存成功");
      fetchRepositories();
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

// 立即同步
const syncRepository = (repo: TableData) => {
  repo.syncing = true;
  repo.updateStatus = "updating";

  api_pullOrClone(repo.id)
    .then(res => {
      if (res.respCode === 2000) {
        ElMessage.success(`仓库 ${repo.repositoryName} 同步成功`);
      } else {
        ElMessage.error(res.respMsg);
      }
    })
    .finally(() => {
      fetchRepositories();
      repo.syncing = false;
    });
};

// 切换自动同步
const toggleScheduled = (repo: TableData) => {
  repo.scheduled = repo.autoSync ? "1" : "0";
  api_updateGitRepoInfo(repo).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("状态修改成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

// 删除仓库
const deleteRepository = (id: string) => {
  ElMessageBox.confirm("确认删除该仓库吗？", "警告", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    api_deleteGitRepo(id).then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("删除成功");
        fetchRepositories();
      } else {
        ElMessage.error(res.respMsg);
      }
    });
  });
};

// 初始化
onMounted(() => {
  fetchRepositories();
  fetchCredentials();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.credential-actions {
  margin-bottom: 20px;
}
</style>
