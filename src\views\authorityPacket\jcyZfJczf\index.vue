<template>
  <div v-loading="cardLoading">
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span class="title">检察院-止付/解除止付</span>
        </div>
      </template>
      
      <el-collapse v-model="activeNames" class="query-form-collapse">
      <el-collapse-item title="BASICINFO" name="basicInfo">
          <el-form label-width="170" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="QQDBS[请求单标识]">
            <div style="display: flex">
                    <el-input v-model="attrValues.basicInfo.qqdbs" style="width: 210px;" />
              <el-tooltip v-if="attrValues.basicInfo.qqdbs" :content="Base64.encode(attrValues.basicInfo.qqdbs)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="QQCSLX[请求措施类型]">
            <div style="display: flex">
                    <el-select clearable v-model="attrValues.basicInfo.qqcslx" placeholder="请选择" style="width: 120px;">
                <el-option key="08" value="08" label="08-止付" />
                <el-option key="09" value="09" label="09-解除止付" />
              </el-select>
              <el-tooltip v-if="attrValues.basicInfo.qqcslx" :content="Base64.encode(attrValues.basicInfo.qqcslx)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
        </el-form>
      </el-collapse-item>

        <el-collapse-item 
          v-for="(stopPaymentAccount, index) in attrValues.stopPaymentAccountList" 
          :key="index"
          :name="'stopPaymentAccount' + index"
        >
          <template #title>
            <div class="collapse-title">
              <span>STOPPAYMENTACCOUNT</span>
            </div>
          </template>
          
          <div class="form-toolbar">
            <div class="template-controls">
              <el-select
                v-model="selectedCommonInfoIdList[index]"
                @change="commonInfoChange(index)"
                size="small"
                placeholder="选择常用信息模板"
                clearable
                style="width: 220px"
              >
                <el-option v-for="item in commonInfoList" :key="item.id" :value="item.id!" :label="item.infoName" />
              </el-select>
              
              <el-button 
                type="primary" 
                size="small" 
                v-if="selectedCommonInfoIdList[index]" 
                @click="updateCommonInfo('STOPPAYMENTACCOUNT', index)" 
                plain
              >
                <el-icon><RefreshRight /></el-icon>更新信息
              </el-button>
              
              <el-popconfirm 
                title="确定要删除这条常用信息吗?" 
                v-if="selectedCommonInfoIdList[index]" 
                @confirm="deleteCommonInfo(index)"
                confirm-button-text="删除"
                cancel-button-text="取消"
                width="220"
              >
                <template #reference>
                  <el-button type="danger" size="small" plain>
                    <el-icon><Delete /></el-icon>删除信息
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>
          
          <el-form label-width="180" class="query-form">
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="RWLSH[任务流水号]">
            <div style="display: flex">
                    <el-input v-model="stopPaymentAccount.rwlsh" style="width: 260px;" />
              <el-tooltip v-if="stopPaymentAccount.rwlsh" :content="Base64.encode(stopPaymentAccount.rwlsh)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
              <el-col :span="12">
          <el-form-item label="YRWLSH[原任务流水号]">
            <div style="display: flex">
                    <el-input v-model="stopPaymentAccount.yrwlsh" style="width: 260px;" />
              <el-tooltip v-if="stopPaymentAccount.yrwlsh" :content="Base64.encode(stopPaymentAccount.yrwlsh)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
          <el-form-item label="ZH[账卡号]">
            <div style="display: flex">
              <el-input v-model="stopPaymentAccount.zh" style="width: 260px;" />
              <el-tooltip v-if="stopPaymentAccount.zh" :content="Base64.encode(stopPaymentAccount.zh)" placement="top">
                <el-text size="small" style="margin-left: 10px; text-decoration: underline;">base64</el-text>
              </el-tooltip>
            </div>
          </el-form-item>
              </el-col>
            </el-row>
            
            <div class="bottom-actions">
              <el-button type="success" size="small" @click="saveCommonInfo('STOPPAYMENTACCOUNT', stopPaymentAccount)" plain>
                <el-icon><Download /></el-icon>保存为常用信息
              </el-button>
            </div>
        </el-form>
      </el-collapse-item>
    </el-collapse>

      <div class="card-footer-actions">
        <el-button type="info" plain size="small" @click="initRandomDigits">
          <el-icon><Refresh /></el-icon>刷新随机数
        </el-button>
        <el-button type="primary" size="small" @click="addStopPaymentAccount" plain>
          <el-icon><Plus /></el-icon>添加STOPPAYMENTACCOUNT
        </el-button>
        <el-button type="danger" size="small" @click="deleteStopPaymentAccount" plain v-if="attrValues.stopPaymentAccountList.length !== 1">
          <el-icon><Delete /></el-icon>删除STOPPAYMENTACCOUNT
        </el-button>
      </div>
    </el-card>

    <el-card class="action-card">
      <template #header>
        <div class="card-header">
          <span class="title">操作</span>
        </div>
      </template>
      
      <div class="action-buttons">
        <el-button type="warning" @click="generatePacket">
          <el-icon><Document /></el-icon>生成报文
        </el-button>
        <el-button type="primary" @click="downloadZipPacket" :disabled="!packetZipName">
          <el-icon><Download /></el-icon>下载
        </el-button>
        <el-button type="success" @click="ftpUpload" :disabled="!packetZipName">
          <el-icon><Upload /></el-icon>上传至服务器
        </el-button>
    </div>

      <div v-if="packetZipName" class="file-info">
        <el-tag type="success">文件已生成: {{ packetZipName }}</el-tag>
    </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {
  api_deleteCommonInfo,
  api_downloadZipPacket, api_editCommonInfo, api_ftpUpload,
  api_generatePacket, api_getCommonInfo,
  api_getXmlStructureTree, api_saveTemplate, EntryDto, PacketCommonInfo,
  XmlTreeDto
} from "@/api/modules/service-integrated-tester/authority-packet";
import {ElMessage, ElMessageBox} from "element-plus";
import {Base64} from "js-base64";
import { Plus, Delete, Download, Refresh, RefreshRight, Document, Upload } from '@element-plus/icons-vue';

const packetType = "检察院-止付/解除止付";
const templateFolderName = "100000F059H101370201001ZF88494202112153916693";
const templateXmlName = "SS21F059H101370201001ZF88494202112153916693.xml";
const ftpRemotePath = "/最高人民检察院/Download";
const templateStopPaymentAccount = {
  rwlsh: "",
  yrwlsh: "",
  zh: ""
};

const lastRandomDigits = ref();
const cardLoading = ref(false);
const activeNames = ref(["basicInfo", "stopPaymentAccount0"]);
const commonInfoList = ref<PacketCommonInfo[]>([]);
const selectedCommonInfoIdList = ref<(string | undefined) []>([]);
const selectedCommonInfoList = ref<(PacketCommonInfo | undefined) []>([]);
const packetXml = ref<XmlTreeDto>();
const attrValues = ref({
  basicInfo: {
    qqdbs: "",
    qqcslx: "",
  },
  stopPaymentAccountList: [{ ...templateStopPaymentAccount }]
});
const packetZipName = ref<string>();

const initRandomDigits = function () {
  lastRandomDigits.value = "ZF" + Date.now().toString() + Math.floor(Math.random() * ********).toString().padStart(7, '0');
  for (let i = 0; i < attrValues.value.stopPaymentAccountList.length; i++) {
    const currStopPaymentAccount = attrValues.value.stopPaymentAccountList[i];
    currStopPaymentAccount.rwlsh = lastRandomDigits.value + (i+1).toString().padStart(5, '0');
  }
  attrValues.value.basicInfo.qqdbs = lastRandomDigits.value;
};

const queryTemplateXml = function () {
  cardLoading.value = true;
  api_getXmlStructureTree({
    templateFolderName,
    templateXmlName
  }).then(res => {
    if (res.respCode === 2000) {
      packetXml.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    cardLoading.value = false;
  });
};

const rulesCheck = function () {
  if (!attrValues.value.basicInfo.qqdbs) {
    ElMessage.error("QQDBS[请求单标识]为必填项");
    return false;
  }
  if (!attrValues.value.basicInfo.qqcslx) {
    ElMessage.error("QQCSLX[请求措施类型]为必填项");
    return false;
  }
  for (let i = 0; i < attrValues.value.stopPaymentAccountList.length; i++) {
    const currStopPaymentAccount = attrValues.value.stopPaymentAccountList[i];
    const errMsgPrefix = "第" + (i+1) + "个元素 - ";
    if (!currStopPaymentAccount.rwlsh) {
      ElMessage.error(errMsgPrefix + "RWLSH[任务流水号]为必填项");
      return false;
    }
    if (attrValues.value.basicInfo.qqcslx === "08" && currStopPaymentAccount.yrwlsh) {
      ElMessage.error(errMsgPrefix + "QQCSLX[请求措施类型]为08时，YRWLSH[原任务流水号]不填");
      return false;
    }
    if (attrValues.value.basicInfo.qqcslx === "09" && !currStopPaymentAccount.yrwlsh) {
      ElMessage.error(errMsgPrefix + "QQCSLX[请求措施类型]为09时，YRWLSH[原任务流水号]必填");
      return false;
    }
    if (!currStopPaymentAccount.zh) {
      ElMessage.error(errMsgPrefix + "ZH[账卡号]为必填项");
      return false;
    }
  }
  return true;
};

const assignValue = function () {
  const basicInfo = packetXml.value?.children.find(item => item.tag === "BASICINFO");
  for (let key in attrValues.value.basicInfo) {
    basicInfo!.attributeList.find(item => item.key === key.toUpperCase())!.value = Base64.encode(attrValues.value.basicInfo[key]);
  }

  const templateStopPaymentAccountTree = JSON.parse(JSON.stringify(packetXml.value?.children.find(item => item.tag === "STOPPAYMENTACCOUNTS")?.children[0]));
  for (let i = 0; i < attrValues.value.stopPaymentAccountList.length; i++) {
    const currFreezeAccount = attrValues.value.stopPaymentAccountList[i];
    const currTree = JSON.parse(JSON.stringify(templateStopPaymentAccountTree));
    for (let key in currFreezeAccount) {
      currTree!.attributeList.find((item: EntryDto) => item.key === key.toUpperCase())!.value = Base64.encode(currFreezeAccount[key]);
    }
    packetXml.value!.children.find(item => item.tag === "STOPPAYMENTACCOUNTS")!.children[i] = currTree!;
  }
};

const generatePacket = function () {
  cardLoading.value = true;
  if (!rulesCheck()) {
    cardLoading.value = false;
    return;
  }
  assignValue();
  api_generatePacket({
    xmlTreeDto: packetXml.value!,
    templateFolderName,
    templateXmlName,
    lastRandomDigits: lastRandomDigits.value
  }).then(res => {
    if (res.respCode === 2000) {
      packetZipName.value = res.respData;
      ElMessage.success("报文包生成成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    cardLoading.value = false;
  });
};

const downloadZipPacket = function () {
  if (!packetZipName.value) {
    ElMessage.error({ message: "无文件下载，请先生成文件", showClose: true, duration: 10000 });
    return;
  }
  api_downloadZipPacket(packetZipName.value!);
};

const ftpUpload = function () {
  cardLoading.value = true;
  if (!packetZipName.value) {
    ElMessage.warning({ message: "无文件上传，请先生成文件", showClose: true, duration: 10000 });
    cardLoading.value = false;
    return;
  }
  api_ftpUpload(packetZipName.value, ftpRemotePath).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("上传成功");
    } else {
      ElMessage.error({ message: res.respMsg, showClose: true, duration: 10000 });
    }
  }).finally(() => {
    cardLoading.value = false;
  });
};

const initStopPaymentAccount = function (listIndex: number) {
  const stopPaymentAccount = attrValues.value.stopPaymentAccountList[listIndex];
  stopPaymentAccount.rwlsh = lastRandomDigits.value + (listIndex + 1).toString().padStart(5, '0');
  stopPaymentAccount.yrwlsh = "";
  stopPaymentAccount.zh = "";
};

const queryCommonInfo = function (tagName: string) {
  api_getCommonInfo({
    infoName: "",
    packetType,
    tagName,
    infoContent: ""
  }).then(res => {
    if (res.respCode === 2000) {
      commonInfoList.value = res.respData;
    } else {
      ElMessage.error(res.respMsg);
    }
  });
};

const commonInfoChange = function (listIndex: number) {
  initStopPaymentAccount(listIndex);
  if (!selectedCommonInfoIdList.value[listIndex]) {
    selectedCommonInfoList!.value[listIndex] = undefined;
    return;
  }
  selectedCommonInfoList.value[listIndex] = commonInfoList.value.find(item => item.id === selectedCommonInfoIdList.value[listIndex]);
  const currStopPaymentAccount = attrValues.value.stopPaymentAccountList[listIndex];
  const commonInfoJson = JSON.parse(selectedCommonInfoList.value[listIndex]!.infoContent);
  for (let key in commonInfoJson) {
    currStopPaymentAccount[key] = commonInfoJson[key];
  }
};

const saveCommonInfo = function (tagName: string, stopPaymentAccount: any) {
  let copyStopPaymentAccount = JSON.parse(JSON.stringify(stopPaymentAccount));
  delete copyStopPaymentAccount.rwlsh;
  delete copyStopPaymentAccount.yrwlsh;
  ElMessageBox.prompt("请输入名称", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(({value}) => {
    cardLoading.value = true;
    api_saveTemplate({
      infoName: value,
      packetType,
      tagName,
      infoContent: JSON.stringify(copyStopPaymentAccount)
    }).then(res => {
      if (res.respCode === 2000) {
        ElMessage.success("信息保存成功");
        queryCommonInfo(tagName);
      } else {
        ElMessage.error(res.respMsg);
      }
    }).finally(() => {
      cardLoading.value = false;
    });
  });
};

const updateCommonInfo = function (tagName: string, listIndex: number) {
  let copyContent = JSON.parse(JSON.stringify(attrValues.value.stopPaymentAccountList[listIndex]));
  delete copyContent.rwlsh;
  delete copyContent.yrwlsh;
  const newCommonInfo: PacketCommonInfo = {
    id: selectedCommonInfoList.value[listIndex]?.id,
    infoName: selectedCommonInfoList.value[listIndex]!.infoName,
    packetType,
    tagName,
    infoContent: JSON.stringify(copyContent)
  }
  api_editCommonInfo(newCommonInfo).then(res => {
    if (res.respCode === 2000) {
      ElMessage.success("更新成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    queryCommonInfo("STOPPAYMENTACCOUNT");
  });
};

const deleteCommonInfo = function (listIndex: number) {
  api_deleteCommonInfo(selectedCommonInfoIdList.value[listIndex]!).then(res => {
    if (res.respCode === 2000) {
      selectedCommonInfoIdList.value[listIndex] = undefined;
      commonInfoChange(listIndex);
      ElMessage.success("删除成功");
    } else {
      ElMessage.error(res.respMsg);
    }
  }).finally(() => {
    queryCommonInfo("STOPPAYMENTACCOUNT");
  });
};

const addStopPaymentAccount = function () {
  attrValues.value.stopPaymentAccountList.push({ ... templateStopPaymentAccount });
  initStopPaymentAccount(attrValues.value.stopPaymentAccountList.length - 1);
  selectedCommonInfoIdList.value.push(undefined);
  selectedCommonInfoList.value.push(undefined);
};

const deleteStopPaymentAccount = function () {
  attrValues.value.stopPaymentAccountList.pop();
  selectedCommonInfoIdList.value.pop();
  selectedCommonInfoList.value.pop();
};

onMounted(() => {
  queryTemplateXml();
  queryCommonInfo("STOPPAYMENTACCOUNT");
  initRandomDigits();
  initStopPaymentAccount(0);
});
</script>

<style scoped lang="scss">
.query-card, .action-card {
  margin-bottom: 16px;
  border-radius: 8px;
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .form-info {
    color: #606266;
    font-size: 13px;
    background-color: #f0f2f5;
    padding: 2px 8px;
    border-radius: 4px;
    
    small {
      color: #909399;
      margin-left: 4px;
    }
  }
}

.form-toolbar {
  display: flex;
  margin-bottom: 16px;
  background: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  
  .template-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.query-form {
  margin-top: 10px;
  
  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-picker) {
    width: 100%;
  }
}

.bottom-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-start;
}

.card-footer-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.query-form-collapse {
  :deep(.el-collapse-item__header) {
    padding: 0 8px;
    
    &.is-active {
      background-color: #ecf5ff;
      border-bottom-color: #d9ecff;
    }
  }
  
  :deep(.el-collapse-item__wrap) {
    padding: 16px;
    background-color: #fbfbfb;
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.file-info {
  margin-top: 16px;
}
</style>
