<template>
  <div class="graph-tab">
    <div id="container"></div>
  </div>
</template>

<script setup lang="ts">
import { watch } from "vue";
import G6, { IGroup, IShape, ModelConfig, TreeGraph } from "@antv/g6";
import { TreeFilterNode } from "@/api/interface";

const props = defineProps<{ graphData: TreeFilterNode }>();

watch(
  () => props.graphData,
  val => {
    if (graph) {
      graph.destroy();
    }
    if (val) {
      showCanvas();
    }
  }
);

interface CustomModelConfig extends ModelConfig {
  id: string;
  label: string;
  children: TreeFilterNode[] | null;
}

const drawShape = (cfg: CustomModelConfig, group?: IGroup): IShape => {
  const rect = group!.addShape("rect", {
    attrs: {
      fill: "#fff",
      stroke: "#666",
      x: 0,
      y: 0,
      width: 1,
      height: 1
    },
    // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
    name: "rect-shape"
  });
  // const content = cfg!.label.replace(/(.{19})/g, "$1\n");
  const text = group!.addShape("text", {
    attrs: {
      text: cfg!.label,
      x: 0,
      y: 0,
      textAlign: "left",
      textBaseline: "middle",
      fill: "#666"
    },
    // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
    name: "text-shape"
  });
  const bbox = text.getBBox();
  const hasChildren = cfg!.children && cfg!.children.length > 0;
  rect.attr({
    x: -bbox.width / 2 - 4,
    y: -bbox.height / 2 - 6,
    width: bbox.width + (hasChildren ? 26 : 12),
    height: bbox.height + 12
  });
  text.attr({
    x: -bbox.width / 2,
    y: 0
  });
  if (hasChildren) {
    group!.addShape("marker", {
      attrs: {
        x: bbox.width / 2 + 12,
        y: 0,
        r: 6,
        symbol: cfg!.collapsed ? G6.Marker.expand : G6.Marker.collapse,
        stroke: "#666",
        lineWidth: 2
      },
      // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
      name: "collapse-icon"
    });
  }
  return rect;
};

G6.registerNode(
  "tree-node",
  {
    drawShape(cfg, group) {
      return drawShape(cfg as CustomModelConfig, group);
    },
    update: (cfg, item) => {
      const group = item.getContainer();
      const icon = group.find(e => e.get("name") === "collapse-icon");
      icon.attr("symbol", cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse);
    }
  },
  "single-node"
);

let graph: TreeGraph;
function showCanvas() {
  const container = document.getElementById("container");
  const width = container?.scrollWidth;
  const height = container?.scrollHeight || 500;
  graph = new G6.TreeGraph({
    container: "container",
    width,
    height,
    modes: {
      default: [
        {
          type: "collapse-expand",
          onChange: function onChange(item: any, collapsed) {
            const data = item?.get("model");
            graph.updateItem(item, {
              collapsed
            });
            data.collapsed = collapsed;
            return true;
          }
        },
        "drag-canvas",
        "zoom-canvas"
      ]
    },
    defaultNode: {
      type: "tree-node",
      anchorPoints: [
        [0, 0.5],
        [1, 0.5]
      ]
    },
    defaultEdge: {
      type: "cubic-horizontal",
      style: {
        stroke: "#A3B1BF"
      }
    },
    layout: {
      type: "compactBox",
      direction: "LR",
      getId: function getId(d) {
        return d.id;
      },
      getHeight: function getHeight() {
        return 16;
      },
      getWidth: function getWidth() {
        return 16;
      },
      getVGap: function getVGap() {
        return 20;
      },
      getHGap: function getHGap() {
        return 80;
      }
    }
  });
  G6.Util.traverseTree(props.graphData, function (item) {
    item.id = item.id;
  });
  graph.data(props.graphData);
  graph.render();
  graph.fitView();

  if (typeof window !== "undefined")
    window.onresize = () => {
      if (!graph || graph.get("destroyed")) return;
      if (!container || !container.scrollWidth || !container.scrollHeight) return;
      graph.changeSize(container.scrollWidth, container.scrollHeight);
    };
}
</script>

<style>
@import "./index.scss";
</style>
