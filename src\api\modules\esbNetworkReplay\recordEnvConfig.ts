import http from "@/api";
import {SERVICE_ENR} from "@/api/config/servicePort";

export interface RecordDBEnv {
  id?: string;
  envName: string;
  url: string;
  userName: string;
  password: string;
  createTime?: string;
}

// 请求录制数据库环境列表
export const api_queryRecordDBEnvList = function () {
  return http.get<RecordDBEnv[]>(SERVICE_ENR + "/config/getRecordDBEnv");
};

// 新增录制数据库环境
export const api_addRecordDBEnv = function (data: RecordDBEnv) {
  return http.post(SERVICE_ENR + "/config/addRecordDBEnv", data);
};

// 根据id更新录制数据库环境
export const api_updateRecordDBEnv = function (data: RecordDBEnv) {
  return http.post(SERVICE_ENR + "/config/updateRecordDBEnv", data);
}

// 根据id删除录制数据库环境
export const api_deleteRecordDBEnv = function (id: string) {
  return http.get(SERVICE_ENR + "/config/deleteRecordDBEnv", { id: id });
}
