<template>
  <div class="container">
    <component :is="currentComponent" @change-component="changeComponent" :trans-params="transParams" />
  </div>
</template>

<script setup lang="ts">
import ReplayPlan, {TransParams} from "@/views/esbNetworkReplay/replayManagement/replayPlan/index.vue";
import ReplayInfo from "@/views/esbNetworkReplay/replayManagement/replayInfo/index.vue";
import {ref, shallowRef} from "vue";

const currentComponent = shallowRef<any>(ReplayPlan);
const transParams = ref<TransParams>();

const changeComponent = (componentName: string, params: TransParams) => {
  transParams.value = params;
  switch (componentName){
    case "ReplayPlan":
      currentComponent.value = ReplayPlan;
      return;
    case "ReplayInfo":
      currentComponent.value = ReplayInfo;
      return;
  }
}
</script>

<style scoped lang="scss">

</style>
