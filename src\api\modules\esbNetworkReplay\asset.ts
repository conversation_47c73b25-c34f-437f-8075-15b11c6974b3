import http from "@/api";
import { SERVICE_ENR } from "@/api/config/servicePort";
import { Page } from "@/api/interface";
import { ENRCommonRespDto } from "@/api/modules/service-esb-data/service-model";

export interface AssetInfoSearchDto {
  pageNo: number;
  pageSize: number;
  interfaceId: string;
  subjectDomain: string;
  serviceProvider: string;
  interfaceType: string;
  timeRange: any;
  startTime: string;
  endTime: string;
}

export interface AssetInfoDto extends ENRCommonRespDto {
  count: number;
}

export interface AssetInfoDetailSearchDto {
  pageNo: number;
  pageSize: number;
  interfaceId: string;
}

export interface AssetInfo {
  id: string;
  interfaceId: string;
  requestTime: string;
  responseTime: string;
  channelId: string;
  url: string;
  transCde: string;
  csmrId: string;
  versionNumber: string;
  esbRespMsg: string;
  esbRespCode: string;
}

export interface AssetDetail {
  id: string;
  requestBody: string;
  responseBody: string;
}

export const api_getPagedAssetInfo = (searchCondition: AssetInfoSearchDto) => {
  return http.post<Page<AssetInfoDto[]>>(SERVICE_ENR + "/asset/getPagedAssetInfo", searchCondition);
};

export const api_getPagedAssetInfoDetail = (searchCondition: AssetInfoDetailSearchDto) => {
  return http.post<Page<AssetInfo[]>>(SERVICE_ENR + "/asset/getPagedAssetInfoDetail", searchCondition);
};

export const api_getAssetDetailFieldValue = (id: string, field: string) => {
  return http.get<AssetDetail>(SERVICE_ENR + "/asset/getAssetDetailFieldValue", { id, field });
};

export const api_deleteAsset = (id: string) => {
  return http.get(SERVICE_ENR + "/asset/deleteAsset", { id });
};

export const api_getAssetInfoFieldValue = (field: string) => {
  return http.get<AssetInfoDto[]>(SERVICE_ENR + "/asset/getAssetInfoFieldValue", { field });
};

export const api_getInfoFieldValueByInterfaceId = (interfaceId: string, field: string) => {
  return http.get<string[]>(SERVICE_ENR + "/asset/getInfoFieldValueByInterfaceId", { interfaceId, field });
};
